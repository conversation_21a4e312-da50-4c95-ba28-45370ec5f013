package com.atom.diesnookerapp.ui.ergebniserfassung

fun getExerciseCategory(exerciseId: String?): String? {
    return when {
        exerciseId in listOf(
            "blue_to_pink", "long_shots", "medium_to_blue", "rest"
        ) -> "Potting"

        exerciseId in listOf(
            "line_up", "clearance", "sequence",
            "break_50", "break_75", "break_100", "break_125", "break_150",
            "t_break", "fifty_plus", "x_break", "y_break",
            "eight_reds", "five_reds", "frames", "black_routines",
            "brown_to_reds", "bc_to_reds"
        ) -> "Breakbuilding"

        exerciseId in listOf(
            "safety_shots", "snooker_escape", "defensive_position",
            "three_reds_save", "snooker_legen", "black_to_bc",
            "blue_pink_black"
        ) -> "Safeties"

        exerciseId in listOf(
            "split_shots", "cluster_break", "controlled_split",
            "black", "blue", "brown", "yellow_splits", "green"
        ) -> "Splits"

        exerciseId in listOf(
            "stellungsspiel_gelb", "stellungsspiel_gruen", "stellungsspiel_braun", "blau_durch_bc", "hohe_schwarze"
        ) -> "Stellungsspiel"

        exerciseId in listOf(
            "blue_doubletouch", "langer_stoss", "gerader_stoss_2_kreiden"
        ) -> "Technik"

        else -> null
    }
}

fun getExerciseTitle(exerciseId: String?): String {
    return when (exerciseId) {
        "blue_to_pink" -> "Blau zu Pink"
        "long_shots" -> "8 Long shots"
        "medium_to_blue" -> "Medium zu Blau"
        "rest" -> "Rest"
        "blau_durch_bc" -> "Blau durch BC"

        "line_up" -> "Line-up"
        "clearance" -> "Clearance"
        "sequence" -> "Sequenz"
        "break_50" -> "Break 50"
        "break_75" -> "Break 75"
        "break_100" -> "Break 100"
        "break_125" -> "Break 125"
        "break_150" -> "Break 150"
        "t_break" -> "T-Break"
        "fifty_plus" -> "50+"
        "x_break" -> "X-Break"
        "y_break" -> "Y-Break"
        "eight_reds" -> "8reds Break"
        "five_reds" -> "5reds clear"
        "frames" -> "Frames"
        "black_routines" -> "Black Routines"
        "brown_to_reds" -> "Braun zu Rot"
        "bc_to_reds" -> "BC zu Rot"

        "safety_shots" -> "Safety Shots"
        "snooker_escape" -> "Snooker Escape"
        "defensive_position" -> "Defensive Position"
        "three_reds_save" -> "3 Rote Save"
        "snooker_legen" -> "Snooker legen"
        "black_to_bc" -> "Schwarz zu BC"
        "blue_pink_black" -> "Blau Pink Schwarz"

        "split_shots" -> "Split Shots"
        "cluster_break" -> "Cluster Break"
        "controlled_split" -> "Kontrollierter Split"
        "black" -> "Schwarz"
        "blue" -> "Blau"
        "brown" -> "Braun"
        "yellow_splits" -> "Gelb"
        "green" -> "Grün"

        "blue_doubletouch" -> "Blue Doubletouch"
        "langer_stoss" -> "Langer Stoß"
        "gerader_stoss_2_kreiden" -> "Gerader Stoß 2 Kreiden"

        "stellungsspiel_gelb" -> "Stellungsspiel Gelb"
        "stellungsspiel_gruen" -> "Stellungsspiel Grün"
        "stellungsspiel_braun" -> "Stellungsspiel Braun"
        "hohe_schwarze" -> "Hohe Schwarze"

        null -> "Unbekannte Übung"

        else -> exerciseId
    }
}
