package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.animation.ValueAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView
import org.threeten.bp.LocalDate
import org.threeten.bp.YearMonth
import org.threeten.bp.format.DateTimeFormatter
import java.util.Locale

class HistoryAdapter(
    private val trainingRecords: List<DailyTrainingRecord>,
    private val questionRecords: List<DailyQuestionRecord>,
    private val onDateSelected: (LocalDate) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_MONTH = 0
        private const val VIEW_TYPE_DATE = 1
    }

    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val monthFormatter = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.GERMAN)

    // Data structure to hold our grouped records
    private val items = mutableListOf<HistoryItem>()

    // Map to track expanded state of each month
    private val expandedMonths = mutableMapOf<YearMonth, Boolean>()

    init {
        // Group all records by month and date
        val allDates = (trainingRecords.map { it.date } + questionRecords.map { it.date }).distinct().sortedDescending()

        // Group dates by month
        val datesByMonth = allDates.groupBy { YearMonth.from(it) }

        // Sort months in descending order (newest first)
        val sortedMonths = datesByMonth.keys.sortedDescending()

        // Add month headers and date items
        sortedMonths.forEach { month ->
            // Add month header
            items.add(HistoryItem.MonthHeader(month))

            // Set initial expanded state to false (collapsed)
            expandedMonths[month] = false

            // Add date items for this month (initially hidden)
            datesByMonth[month]?.forEach { date ->
                val recordsForDate = trainingRecords.filter { it.date == date } +
                                   questionRecords.filter { it.date == date }
                items.add(HistoryItem.DateItem(date, recordsForDate))
            }
        }

        // Initialize visible items
        updateVisibleItems()
    }

    private fun getTrainingTypeName(type: TrainingType): String = when (type) {
        TrainingType.BEFORE_TRAINING -> "Vor Training"
        TrainingType.AFTER_TRAINING -> "Nach Training"
        TrainingType.BEFORE_TOURNAMENT -> "Vor Turnier"
        TrainingType.AFTER_TOURNAMENT -> "Nach Turnier"
    }

    private fun getQuestionTypeName(type: QuestionType): String = when (type) {
        QuestionType.BEFORE_TRAINING -> "Fragen vor Training"
        QuestionType.AFTER_TRAINING -> "Fragen nach Training"
        QuestionType.BEFORE_TOURNAMENT -> "Fragen vor Turnier"
        QuestionType.AFTER_TOURNAMENT -> "Fragen nach Turnier"
    }

    // Define sealed class for different item types
    sealed class HistoryItem {
        data class MonthHeader(val month: YearMonth) : HistoryItem()
        data class DateItem(val date: LocalDate, val records: List<Any>) : HistoryItem()
    }

    // ViewHolder for date items
    class DateViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val dateText: TextView = view.findViewById(R.id.dateText)
        val typeText: TextView = view.findViewById(R.id.typeText)
        val contentText: TextView = view.findViewById(R.id.contentText)
    }

    // ViewHolder for month headers
    class MonthViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.monthCardView)
        val monthText: TextView = view.findViewById(R.id.monthText)
        val entriesCountText: TextView = view.findViewById(R.id.entriesCountText)
        val expandCollapseIcon: ImageView = view.findViewById(R.id.expandCollapseIcon)
        val headerLayout: LinearLayout = view.findViewById(R.id.headerLayout)
    }

    override fun getItemViewType(position: Int): Int {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }

        return when (visibleItems[position]) {
            is HistoryItem.MonthHeader -> VIEW_TYPE_MONTH
            is HistoryItem.DateItem -> VIEW_TYPE_DATE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_MONTH -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_month_header, parent, false)
                MonthViewHolder(view)
            }
            VIEW_TYPE_DATE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_history, parent, false)
                DateViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    // Cache for visible items
    private var visibleItems = listOf<HistoryItem>()

    // Update visible items cache
    private fun updateVisibleItems() {
        visibleItems = getVisibleItems()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }

        when (val item = visibleItems[position]) {
            is HistoryItem.MonthHeader -> {
                holder as MonthViewHolder
                val month = item.month

                // Set month name
                holder.monthText.text = month.format(monthFormatter)

                // Count entries in this month
                val entriesCount = items.count {
                    it is HistoryItem.DateItem && YearMonth.from(it.date) == month
                }
                holder.entriesCountText.text = "$entriesCount Einträge"

                // Set expand/collapse icon
                val isExpanded = expandedMonths[month] ?: false
                // Set initial rotation without animation
                holder.expandCollapseIcon.rotation = if (isExpanded) 180f else 0f

                // Set click listener for expand/collapse
                holder.headerLayout.setOnClickListener {
                    toggleMonthExpansion(month)
                }
            }
            is HistoryItem.DateItem -> {
                holder as DateViewHolder
                val date = item.date
                val records = item.records

                holder.dateText.text = date.format(dateFormatter)

                val content = buildString {
                    records.forEach { record ->
                        when (record) {
                            is DailyTrainingRecord -> {
                                appendLine(getTrainingTypeName(record.type))
                            }
                            is DailyQuestionRecord -> {
                                appendLine(getQuestionTypeName(record.type))
                            }
                        }
                    }
                }

                holder.typeText.visibility = View.GONE
                holder.contentText.text = content.trim()

                holder.cardView.setOnClickListener {
                    onDateSelected(date)
                }
            }
        }
    }

    // Get visible items based on expansion state
    private fun getVisibleItems(): List<HistoryItem> {
        val visibleItems = mutableListOf<HistoryItem>()

        // Group items by month
        val itemsByMonth = items.groupBy { item ->
            when (item) {
                is HistoryItem.MonthHeader -> item.month
                is HistoryItem.DateItem -> YearMonth.from(item.date)
            }
        }

        // Process each month
        val sortedMonths = itemsByMonth.keys.sortedDescending()
        sortedMonths.forEach { month ->
            // Add month header
            val monthHeader = itemsByMonth[month]?.firstOrNull { it is HistoryItem.MonthHeader }
            if (monthHeader != null) {
                visibleItems.add(monthHeader)
            }

            // Add date items if month is expanded
            val isExpanded = expandedMonths[month] ?: false
            if (isExpanded) {
                val dateItems = itemsByMonth[month]?.filter { it is HistoryItem.DateItem }?.sortedByDescending {
                    (it as HistoryItem.DateItem).date
                } ?: emptyList()
                visibleItems.addAll(dateItems)
            }
        }

        return visibleItems
    }

    override fun getItemCount(): Int {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }
        return visibleItems.size
    }

    // Reference to the RecyclerView
    private var recyclerView: RecyclerView? = null

    // Called when adapter is attached to a RecyclerView
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    // Called when adapter is detached from a RecyclerView
    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    // Helper method to find ViewHolder for adapter position
    private fun findViewHolderForAdapterPosition(position: Int): RecyclerView.ViewHolder? {
        return recyclerView?.findViewHolderForAdapterPosition(position)
    }

    private fun toggleMonthExpansion(month: YearMonth) {
        // Toggle expansion state
        val isCurrentlyExpanded = expandedMonths[month] ?: false
        expandedMonths[month] = !isCurrentlyExpanded

        // Get old list for diff calculation
        val oldList = visibleItems

        // Recalculate visible items
        updateVisibleItems()

        // Calculate diff and animate changes
        val diffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun getOldListSize() = oldList.size
            override fun getNewListSize() = visibleItems.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldList[oldItemPosition]
                val newItem = visibleItems[newItemPosition]

                return when {
                    oldItem is HistoryItem.MonthHeader && newItem is HistoryItem.MonthHeader ->
                        oldItem.month == newItem.month
                    oldItem is HistoryItem.DateItem && newItem is HistoryItem.DateItem ->
                        oldItem.date == newItem.date
                    else -> false
                }
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return areItemsTheSame(oldItemPosition, newItemPosition)
            }
        }, true)

        // Apply animations
        diffResult.dispatchUpdatesTo(this)

        // Animate the arrow rotation
        val viewHolders = (0 until itemCount).mapNotNull { position ->
            if (visibleItems[position] is HistoryItem.MonthHeader &&
                (visibleItems[position] as HistoryItem.MonthHeader).month == month) {
                return@mapNotNull findViewHolderForAdapterPosition(position) as? MonthViewHolder
            }
            null
        }

        viewHolders.forEach { holder ->
            animateArrow(holder.expandCollapseIcon, !isCurrentlyExpanded)
        }
    }

    private fun animateArrow(view: View, isExpanded: Boolean) {
        val startRotation = if (isExpanded) 0f else 180f
        val endRotation = if (isExpanded) 180f else 0f

        val animator = ValueAnimator.ofFloat(startRotation, endRotation)
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.addUpdateListener { animation ->
            view.rotation = animation.animatedValue as Float
        }
        animator.start()
    }
}