# Die Snooker App - Web Interface

This is a simple web interface for viewing history data from the Die Snooker App.

## Setup

1. Create a Firebase project at https://console.firebase.google.com/
2. Enable Authentication (Email/Password) and Firestore Database
3. Update the Firebase configuration in `app.js` with your project's configuration
4. Deploy to Firebase Hosting:

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project
firebase init

# Deploy to Firebase Hosting
firebase deploy
```

## Security Rules

Make sure to set up proper security rules in your Firestore database:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

This ensures that users can only access their own data.
