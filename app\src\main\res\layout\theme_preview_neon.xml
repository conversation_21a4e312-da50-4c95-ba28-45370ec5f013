<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/neonThemePreview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/neon_card_background_light"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Neon Theme"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:background="@color/neon_purple_3" />

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:background="@color/neon_pink_2" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/neon_purple_1" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/neon_purple_4" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/neon_purple_7" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_weight="1"
                android:background="@color/neon_pink_3" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
