package com.atom.diesnookerapp.data.firebase

import com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseRecord
import org.threeten.bp.LocalDateTime
import org.threeten.bp.format.DateTimeFormatter

/**
 * Firebase model for exercise records
 */
data class FirebaseExerciseRecord(
    var exerciseId: String = "",
    var exerciseName: String = "", // New field
    var timestamp: String = "", // ISO format timestamp
    var score: Int? = null,
    var timeInMinutes: Int? = null,
    var spelt: String? = null,
    var isCompletionMarker: Boolean = false
) : FirebaseModel() {

    companion object {
        private val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME

        fun fromExerciseRecord(record: ExerciseRecord, userId: String): FirebaseExerciseRecord {
            return FirebaseExerciseRecord(
                exerciseId = record.exerciseId,
                exerciseName = record.exerciseName ?: "", // Handle possible null value
                timestamp = record.timestamp.format(formatter),
                score = record.score,
                timeInMinutes = record.timeInMinutes,
                spelt = record.spelt,
                isCompletionMarker = record.isCompletionMarker
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }

    fun toExerciseRecord(): ExerciseRecord {
        return ExerciseRecord(
            id = id,
            exerciseId = exerciseId,
            exerciseName = exerciseName,
            timestamp = LocalDateTime.parse(timestamp, formatter),
            score = score,
            timeInMinutes = timeInMinutes,
            spelt = spelt,
            isCompletionMarker = isCompletionMarker
        )
    }
}
