"use client";

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { useAuth } from '@/context/AuthContext'; // To display user info or role

interface NavLinkProps {
  href: string;
  children: ReactNode;
}

const NavLink = ({ href, children }: NavLinkProps) => {
  const pathname = usePathname();
  const isActive = pathname === href;
  return (
    <Link href={href} className={`block px-4 py-2 rounded-md text-sm font-medium transition-colors
      ${isActive ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-600 hover:text-white'}`}>
      {children}
    </Link>
  );
};

export default function PlayerDashboardLayout({ children }: { children: ReactNode }) {
  const { currentUser, userProfile } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await auth.signOut();
      router.push('/login');
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // If auth is still loading, or no user, show loading or redirect (handled by withAuth on page level)
  // This layout assumes it's rendered for an authenticated user.
  // Adding a basic loading check here for robustness.
  if (!currentUser && !userProfile) {
      // This state should ideally be handled by page-level withAuth HOC redirecting.
      // If somehow layout is shown without user, it might be during brief transition.
      // Can show a minimal loading or empty state.
      return <div className="min-h-screen flex items-center justify-center"><p>Loading user...</p></div>;
  }


  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-gray-100">
      {/* Sidebar: Added flex flex-col to ensure logout button stays at bottom */}
      <aside className="w-full md:w-64 bg-indigo-800 text-white flex-shrink-0 flex flex-col">
        <div className="p-5 border-b border-indigo-700">
          <h1 className="text-2xl font-semibold">Die Snooker App</h1>
          {userProfile && (
            <div className="mt-2 text-sm text-indigo-300">
              <p>{userProfile.displayName || currentUser?.email}</p>
              <p>Role: {userProfile.role}</p>
            </div>
          )}
        </div>
        {/* Navigation: Added flex-grow to push logout to bottom */}
        <nav className="p-3 space-y-1 flex-grow">
          <NavLink href="/dashboard">Home</NavLink> {/* Link to the main dashboard page */}
          <NavLink href="/dashboard/self-assessment">Selbsteinschätzung</NavLink>
          <NavLink href="/dashboard/exercises">Übungen</NavLink>
          <NavLink href="/dashboard/tasks">Aufgaben</NavLink>
          <NavLink href="/dashboard/training-plan">Trainingsplan</NavLink>
          <NavLink href="/dashboard/trainer-connections">Trainer-Verbindungen</NavLink>
        </nav>
        {/* Logout Button Container: Removed mt-auto as flex-grow on nav handles spacing */}
        <div className="p-5 border-t border-indigo-700">
          <button
            onClick={handleLogout}
            className="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm transition-colors"
          >
            Logout
          </button>
        </div>
      </aside>
      <main className="flex-1 p-6 overflow-auto">
        {children}
      </main>
    </div>
  );
}
