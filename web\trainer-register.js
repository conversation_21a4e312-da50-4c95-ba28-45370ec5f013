// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY",
    authDomain: "die-snooker-app.firebaseapp.com",
    projectId: "die-snooker-app",
    storageBucket: "die-snooker-app.firebasestorage.app",
    messagingSenderId: "547283642216",
    appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
    measurementId: "G-GTFVZZ4LJ"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// DOM Elements
const registerForm = document.getElementById('trainer-register-form');
const fullNameInput = document.getElementById('fullName');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirmPassword');
const trainerTypeSelect = document.getElementById('trainerType');
const experienceInput = document.getElementById('experience');
const termsCheckbox = document.getElementById('termsAgreement');
const errorMessage = document.getElementById('register-error');
const verificationMessage = document.getElementById('verification-message');

// Event Listeners
if (registerForm) {
    registerForm.addEventListener('submit', handleRegistration);
}

// Handle trainer registration
async function handleRegistration(e) {
    e.preventDefault();
    
    // Clear previous error messages
    errorMessage.textContent = '';
    
    // Get form values
    const fullName = fullNameInput.value.trim();
    const email = emailInput.value.trim();
    const password = passwordInput.value.trim();
    const confirmPassword = confirmPasswordInput.value.trim();
    const trainerType = trainerTypeSelect.value;
    const experience = experienceInput.value.trim();
    const termsAgreed = termsCheckbox.checked;
    
    // Validate form
    if (!fullName || !email || !password || !confirmPassword || !trainerType || !termsAgreed) {
        errorMessage.textContent = 'Bitte füllen Sie alle Pflichtfelder aus.';
        return;
    }
    
    // Validate password
    if (password.length < 8) {
        errorMessage.textContent = 'Das Passwort muss mindestens 8 Zeichen lang sein.';
        return;
    }
    
    // Check if passwords match
    if (password !== confirmPassword) {
        errorMessage.textContent = 'Die Passwörter stimmen nicht überein.';
        return;
    }
    
    // Disable form during registration
    setFormEnabled(false);
    
    try {
        // Create user with email and password
        const userCredential = await auth.createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        // Send email verification
        await user.sendEmailVerification();
        
        // Create user profile in Firestore
        await createTrainerProfile(user.uid, fullName, email, trainerType, experience);
        
        // Show verification message
        registerForm.classList.add('hidden');
        verificationMessage.classList.remove('hidden');
        
    } catch (error) {
        console.error('Registration error:', error);
        
        // Handle specific errors
        switch (error.code) {
            case 'auth/email-already-in-use':
                errorMessage.textContent = 'Diese E-Mail-Adresse wird bereits verwendet.';
                break;
            case 'auth/invalid-email':
                errorMessage.textContent = 'Ungültige E-Mail-Adresse.';
                break;
            case 'auth/weak-password':
                errorMessage.textContent = 'Das Passwort ist zu schwach.';
                break;
            default:
                errorMessage.textContent = `Registrierungsfehler: ${error.message}`;
        }
        
        // Re-enable form
        setFormEnabled(true);
    }
}

// Create trainer profile in Firestore
async function createTrainerProfile(userId, fullName, email, trainerType, experience) {
    try {
        await db.collection('user_profiles').doc(userId).set({
            userId: userId,
            displayName: fullName,
            email: email,
            role: trainerType,
            experience: experience || '',
            emailVerified: false,
            registrationDate: firebase.firestore.FieldValue.serverTimestamp(),
            lastUpdated: firebase.firestore.FieldValue.serverTimestamp()
        });
        
        console.log('Trainer profile created successfully');
    } catch (error) {
        console.error('Error creating trainer profile:', error);
        throw error;
    }
}

// Helper function to enable/disable form
function setFormEnabled(enabled) {
    const formElements = registerForm.querySelectorAll('input, select, textarea, button');
    formElements.forEach(element => {
        element.disabled = !enabled;
    });
    
    // Update button text
    const submitButton = registerForm.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.textContent = enabled ? 'Registrieren' : 'Registrierung läuft...';
    }
}
