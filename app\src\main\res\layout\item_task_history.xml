<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/historyCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header section (always visible) -->
        <LinearLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/weekText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Woche 12, 2023"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/totalPointsText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Gesammelte Punkte: 42"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/dateRangeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="20.03.2023 - 26.03.2023"
                    android:textSize="14sp"
                    android:visibility="gone" />
            </LinearLayout>

            <ImageView
                android:id="@+id/expandCollapseIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/arrow_down_float" />
        </LinearLayout>

        <!-- Content section (expandable) -->
        <LinearLayout
            android:id="@+id/contentLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:background="#DDDDDD" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/taskCompletionsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>