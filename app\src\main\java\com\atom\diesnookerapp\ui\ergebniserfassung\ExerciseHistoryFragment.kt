package com.atom.diesnookerapp.ui.ergebniserfassung

import java.util.Calendar
import java.util.Locale

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.button.MaterialButton
import org.threeten.bp.LocalDate
import org.threeten.bp.LocalDateTime
import org.threeten.bp.ZoneId
import org.threeten.bp.format.DateTimeFormatter
import androidx.navigation.fragment.findNavController
import android.app.AlertDialog
import androidx.core.os.bundleOf
import com.atom.diesnookerapp.ui.settings.ThemeHelper

class ExerciseHistoryFragment : Fragment() {
    private lateinit var exercisePreferences: ExercisePreferences
    private lateinit var monthDateAdapter: MonthDateAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exercise_history, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        exercisePreferences = ExercisePreferences(requireContext())

        // Set title
        view.findViewById<TextView>(R.id.titleText).text = "Verlauf"

        // Setup dates RecyclerView
        val datesRecyclerView = view.findViewById<RecyclerView>(R.id.datesRecyclerView)
        datesRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        monthDateAdapter = MonthDateAdapter(emptyList()) { date ->
            findNavController().navigate(
                R.id.action_navigation_exercise_history_to_exercise_history_detail,
                Bundle().apply {
                    putString("date", date.toString())
                }
            )
        }
        datesRecyclerView.adapter = monthDateAdapter

        // Add graph button
        view.findViewById<MaterialButton>(R.id.graphButton).setOnClickListener {
            showCategoryDialog()
        }

        loadAvailableDates()
    }

    private fun loadAvailableDates() {
        val dates = exercisePreferences.getRecords()
            .map { it.timestamp.toLocalDate() }
            .distinct()
            .sortedDescending()
        monthDateAdapter.updateData(dates)
    }

    private fun showCategoryDialog() {
        val categories = arrayOf("Breakbuilding", "Potting", "Safeties", "Splits", "Technik", "Stellungsspiel")

        // Use ThemeHelper to create a themed AlertDialog with default animations
        ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setTitle("Kategorie auswählen")
            .setItems(categories) { _, which ->
                val category = categories[which]
                findNavController().navigate(
                    R.id.action_exerciseHistory_to_exerciseGraph,
                    bundleOf("category" to category)
                )
            }
            .show()
    }
}