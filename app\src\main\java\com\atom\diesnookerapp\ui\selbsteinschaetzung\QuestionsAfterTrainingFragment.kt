package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import org.threeten.bp.LocalDate

class QuestionsAfterTrainingFragment : Fragment() {
    private var recyclerView: RecyclerView? = null
    private lateinit var questionPreferences: QuestionPreferences
    private val questionItems = listOf(
        QuestionItem(1, "Habe ich mein Ziel erreicht?"),
        QuestionItem(2, "Habe ich mein Taktik/Technik Ziel erreicht?"),
        QuestionItem(3, "Habe ich mein Lernziel erreicht?")
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_questions_after_training, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        questionPreferences = QuestionPreferences(requireContext())

        recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        val adapter = QuestionsAdapter(questionItems) { item ->
            showAnswerDialog(item)
        }

        recyclerView?.adapter = adapter

        view.findViewById<MaterialButton>(R.id.saveButton).setOnClickListener {
            saveQuestions()
        }
    }

    private fun saveQuestions() {
        val record = DailyQuestionRecord(
            date = LocalDate.now(),
            type = QuestionType.AFTER_TRAINING,
            questions = questionItems
        )
        questionPreferences.saveDaily(record)
        Toast.makeText(requireContext(), "Antworten gespeichert", Toast.LENGTH_SHORT).show()
    }

    private fun showAnswerDialog(item: QuestionItem) {
        val editText = TextInputEditText(requireContext()).apply {
            setText(item.answer)
            minHeight = resources.getDimensionPixelSize(R.dimen.dialog_input_min_height)
        }

        // Use ThemeHelper to create a themed AlertDialog
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle(item.title)
            .setView(editText)
            .setPositiveButton("Speichern") { _, _ ->
                item.answer = editText.text?.toString() ?: ""
                recyclerView?.adapter?.notifyDataSetChanged()
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }
}