package com.atom.diesnookerapp.ui.trainingsplan

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.atom.diesnookerapp.MainActivity
import com.atom.diesnookerapp.R
import java.text.NumberFormat
import java.util.Locale

/**
 * <PERSON>les creating and showing notifications related to the training plan
 */
class TrainingsplanNotificationManager(private val context: Context) {
    
    companion object {
        const val CHANNEL_ID = "trainingsplan_reminders"
        const val NOTIFICATION_ID = 1001
        
        // Create notification channels (should be called at app startup)
        fun createNotificationChannels(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val name = "Trainingsplan Erinnerungen"
                val descriptionText = "Erinnerungen für unvollständige Übungen im Trainingsplan"
                val importance = NotificationManager.IMPORTANCE_HIGH
                
                val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                    description = descriptionText
                }
                
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    /**
     * Checks the current training plan and sends a notification if there are incomplete exercises
     * @return true if notification was sent, false otherwise
     */
    fun checkAndNotifyIncompleteExercises(): Boolean {
        val trainingsplanManager = TrainingsplanManager(context)
        val currentPlan = trainingsplanManager.getCurrentTrainingsplan()
        
        // If there are no items or all items are completed, don't send notification
        if (currentPlan.items.isEmpty()) {
            return false
        }
        
        val incompleteExercises = trainingsplanManager.getIncompleteExercises()
        
        // If there are no incomplete exercises, don't send notification
        if (incompleteExercises.isEmpty()) {
            return false
        }
        
        val totalExercises = currentPlan.items.size
        val completedExercises = totalExercises - incompleteExercises.size
        
        // Calculate completion percentage
        val completionPercentage = if (totalExercises > 0) {
            (completedExercises.toDouble() / totalExercises.toDouble()) * 100.0
        } else {
            0.0
        }
        
        // Format percentage to 1 decimal place
        val percentFormat = NumberFormat.getPercentInstance(Locale.getDefault()).apply {
            maximumFractionDigits = 1
            minimumFractionDigits = 0
        }
        val formattedPercentage = percentFormat.format(completionPercentage / 100)
        
        // Build notification content
        val notificationTitle = "Unvollständiger Trainingsplan"
        val notificationContent = buildString {
            append("Du hast $formattedPercentage deines Trainingsplans abgeschlossen")
            append(" ($completedExercises von $totalExercises Übungen).")
            
            if (incompleteExercises.isNotEmpty()) {
                append("\n\nUnvollständige Übungen:")
                
                // List up to 5 incomplete exercises
                incompleteExercises.take(5).forEach { exercise ->
                    append("\n• ${exercise.name}")
                }
                
                // If there are more than 5 incomplete exercises, add a note
                if (incompleteExercises.size > 5) {
                    append("\n... und ${incompleteExercises.size - 5} weitere")
                }
            }
        }
        
        // Send the notification
        sendNotification(notificationTitle, notificationContent)
        return true
    }
    
    /**
     * Sends a notification with the given title and content
     */
    private fun sendNotification(title: String, content: String) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // Create an intent that opens the app
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Build the notification
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(NotificationCompat.BigTextStyle().bigText(content))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(ContextCompat.getColor(context, R.color.purple_500))
        
        // Show the notification
        notificationManager.notify(NOTIFICATION_ID, builder.build())
    }
    
    /**
     * Creates a notification for foreground service
     * This is required for Android 12+ to run workers in the background
     */
    fun createProgressNotification(contentText: String): android.app.Notification {
        createNotificationChannel()
        
        val pendingIntent = android.app.PendingIntent.getActivity(
            context,
            0,
            android.content.Intent(context, com.atom.diesnookerapp.MainActivity::class.java).apply {
                flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK or android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK
            },
            android.app.PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Trainingsplan")
            .setContentText(contentText)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Trainingsplan Erinnerungen"
            val descriptionText = "Erinnerungen für unvollständige Übungen im Trainingsplan"
            val importance = NotificationManager.IMPORTANCE_HIGH
            
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
}
