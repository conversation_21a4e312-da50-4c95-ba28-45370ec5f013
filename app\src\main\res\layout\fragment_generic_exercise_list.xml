<?xml version="1.0" encoding="utf-8"?>
<!-- Keep app namespace for app:layoutManager -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".ui.ergebniserfassung.GenericExerciseListFragment">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/categoryTitleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:textAlignment="center"
        android:textSize="24sp"
        android:textStyle="bold"
        tools:text="Category Title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:padding="8dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_breakbuilding" /> <!-- Placeholder for item layout -->

</LinearLayout>
