// --- User Management Functions ---

// Check user role and adjust UI accordingly
function checkUserRole() {
    if (!auth.currentUser) {
        console.error('No user is signed in');
        return;
    }

    const userId = auth.currentUser.uid;
    console.log('Checking user role for:', userId);

    db.collection('user_profiles')
        .doc(userId)
        .get()
        .then(doc => {
            if (doc.exists) {
                console.log('User profile found');
                const profile = doc.data();
                const role = profile.role || 'PLAYER';
                console.log('User role:', role);

                // Show/hide sections based on role
                if (role === 'TRAINER' || role === 'MENTAL_TRAINER') {
                    // This is a trainer, show trainer dashboard
                    setupTrainerDashboard(role);
                }
            } else {
                console.log('User profile not found, creating default profile');
                // Create a default profile
                const defaultProfile = {
                    displayName: auth.currentUser.email.split('@')[0],
                    email: auth.currentUser.email,
                    role: 'PLAYER',
                    userId: userId,
                    lastUpdated: Date.now()
                };

                db.collection('user_profiles')
                    .doc(userId)
                    .set(defaultProfile)
                    .then(() => {
                        console.log('Default profile created successfully');
                    })
                    .catch(error => {
                        console.error('Error creating user profile:', error);
                    });
            }
        })
        .catch(error => {
            console.error('Error checking user role:', error);
        });
}

// Setup trainer dashboard
function setupTrainerDashboard(role) {
    console.log('Setting up trainer dashboard for role:', role);

    // Hide player-specific sections
    const addTrainerCard = document.getElementById('add-trainer-card');
    if (addTrainerCard) {
        addTrainerCard.style.display = 'none';
    } else {
        console.warn('add-trainer-card element not found');
    }

    // Change the title
    const titleElement = document.querySelector('#usermanagement-section h2');
    if (titleElement) {
        titleElement.textContent = 'Trainer-Dashboard';
    } else {
        console.warn('usermanagement-section h2 element not found');
    }

    const subtitleElement = document.querySelector('#usermanagement-section h3');
    if (subtitleElement) {
        subtitleElement.textContent = 'Verbundene Spieler';
    } else {
        console.warn('usermanagement-section h3 element not found');
    }

    // Load connected players
    loadConnectedPlayers(role);
}

// Load connected players for trainers
function loadConnectedPlayers(trainerRole) {
    const userId = auth.currentUser.uid;

    db.collection('user_connections')
        .where('targetId', '==', userId)
        .where('status', '==', 'ACTIVE')
        .get()
        .then(snapshot => {
            connectionsListElement.innerHTML = '';

            if (snapshot.empty) {
                emptyConnectionsElement.style.display = 'block';
                return;
            }

            emptyConnectionsElement.style.display = 'none';

            // Create player list
            const playerList = document.createElement('div');
            playerList.className = 'player-list';

            snapshot.forEach(doc => {
                const connection = doc.data();

                // Check if trainer has appropriate access
                if ((trainerRole === 'TRAINER' && connection.trainerAccess) ||
                    (trainerRole === 'MENTAL_TRAINER' && connection.mentalTrainerAccess)) {

                    const playerCard = document.createElement('div');
                    playerCard.className = 'player-card';
                    playerCard.innerHTML = `
                        <div class="player-name">${connection.initiatorName}</div>
                        <div class="player-email">${connection.initiatorEmail || connection.initiatorId}</div>
                        <div class="player-data-preview">
                            <div class="data-stat">
                                <div class="data-value">-</div>
                                <div class="data-label">Übungen</div>
                            </div>
                            <div class="data-stat">
                                <div class="data-value">-</div>
                                <div class="data-label">Trainingsplan</div>
                            </div>
                            <div class="data-stat">
                                <div class="data-value">-</div>
                                <div class="data-label">Selbsteinschätzung</div>
                            </div>
                        </div>
                    `;

                    // Add click handler to view player data
                    playerCard.addEventListener('click', () => {
                        viewPlayerData(connection.initiatorId, trainerRole);
                    });

                    playerList.appendChild(playerCard);

                    // Load player stats
                    loadPlayerStats(connection.initiatorId, playerCard, trainerRole);
                }
            });

            connectionsListElement.appendChild(playerList);
        })
        .catch(error => {
            console.error('Error loading connected players:', error);
            connectionsListElement.innerHTML = `<p>Fehler beim Laden der Spieler: ${error.message}</p>`;
        });
}

// Load player statistics
function loadPlayerStats(playerId, playerCard, trainerRole) {
    const statsElements = playerCard.querySelectorAll('.data-value');

    // Load exercise count
    if (trainerRole === 'TRAINER') {
        db.collection('exercise_records')
            .where('userId', '==', playerId)
            .get()
            .then(snapshot => {
                statsElements[0].textContent = snapshot.size;
            })
            .catch(error => {
                console.error('Error loading player exercise stats:', error);
            });

        // Load trainingsplan completion
        db.collection('trainingsplan_history')
            .where('userId', '==', playerId)
            .orderBy('lastUpdated', 'desc')
            .limit(1)
            .get()
            .then(snapshot => {
                if (!snapshot.empty) {
                    const plan = snapshot.docs[0].data();
                    const totalItems = plan.items.length;
                    const completedItems = plan.items.filter(item => item.isChecked).length;
                    const completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

                    statsElements[1].textContent = `${completionPercentage}%`;
                }
            })
            .catch(error => {
                console.error('Error loading player trainingsplan stats:', error);
            });
    }

    // Load self-assessment count
    if (trainerRole === 'MENTAL_TRAINER') {
        db.collection('training_records')
            .where('userId', '==', playerId)
            .get()
            .then(snapshot => {
                statsElements[2].textContent = snapshot.size;
            })
            .catch(error => {
                console.error('Error loading player self-assessment stats:', error);
            });
    }
}

// View player data
function viewPlayerData(playerId, trainerRole) {
    // This would load and display the player's data based on the trainer's role
    // For now, we'll just show an alert
    alert('Diese Funktion wird in einer zukünftigen Version implementiert.');
}

// Load user connections
function loadUserConnections() {
    if (!auth.currentUser) {
        console.error('No user is signed in');
        return;
    }

    const userId = auth.currentUser.uid;
    console.log('Loading connections for user:', userId);

    // Check if the elements exist
    if (!connectionsListElement || !emptyConnectionsElement) {
        console.error('Connection elements not found in the DOM');
        return;
    }

    db.collection('user_connections')
        .where('initiatorId', '==', userId)
        .get()
        .then(snapshot => {
            connectionsListElement.innerHTML = '';

            if (snapshot.empty) {
                console.log('No connections found');
                emptyConnectionsElement.style.display = 'block';
                return;
            }

            console.log(`Found ${snapshot.size} connections`);
            emptyConnectionsElement.style.display = 'none';

            snapshot.forEach(doc => {
                const connection = doc.data();
                console.log('Processing connection:', doc.id);

                const connectionItem = document.createElement('div');
                connectionItem.className = 'connection-item';

                // Get status display info
                const statusInfo = getStatusInfo(connection.status || 'PENDING');

                // Create permissions text
                const permissions = [];
                if (connection.trainerAccess) {
                    permissions.push('Übungen und Trainingsplan');
                }
                if (connection.mentalTrainerAccess) {
                    permissions.push('Selbsteinschätzung');
                }

                // Safely get values with fallbacks
                const targetName = connection.targetName || 'Unknown';
                const targetEmail = connection.targetEmail || 'No email';
                const targetRole = connection.targetRole || 'TRAINER';

                connectionItem.innerHTML = `
                    <div class="connection-header">
                        <div class="connection-name">${targetName}</div>
                        <div class="connection-status ${statusInfo.class}">${statusInfo.text}</div>
                    </div>
                    <div class="connection-email">${targetEmail}</div>
                    <div class="connection-role">${getRoleDisplayName(targetRole)}</div>
                    <div class="connection-permissions"><strong>Berechtigungen:</strong> ${permissions.join(', ') || 'Keine'}</div>
                    <div class="connection-actions">
                        <button class="btn edit-btn" ${connection.status !== 'ACTIVE' ? 'disabled' : ''}>Bearbeiten</button>
                        <button class="btn btn-danger remove-btn">Entfernen</button>
                    </div>
                `;

                // Add event listeners
                const editBtn = connectionItem.querySelector('.edit-btn');
                const removeBtn = connectionItem.querySelector('.remove-btn');

                if (editBtn) {
                    editBtn.addEventListener('click', () => {
                        showEditConnectionDialog(doc.id, connection);
                    });
                }

                if (removeBtn) {
                    removeBtn.addEventListener('click', () => {
                        showRemoveConnectionDialog(doc.id, connection);
                    });
                }

                connectionsListElement.appendChild(connectionItem);
            });
        })
        .catch(error => {
            console.error('Error loading connections:', error);
            if (connectionsListElement) {
                connectionsListElement.innerHTML = `<p>Fehler beim Laden der Verbindungen: ${error.message}</p>`;
            }
        });
}
