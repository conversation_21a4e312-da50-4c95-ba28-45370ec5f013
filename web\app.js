// Firebase configuration - Replace with your actual Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY",
  authDomain: "die-snooker-app.firebaseapp.com",
  projectId: "die-snooker-app",
  storageBucket: "die-snooker-app.firebasestorage.app",
  messagingSenderId: "547283642216",
  appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
  measurementId: "G-GTFVZZ4LJ"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// DOM Elements
const loginContainer = document.getElementById('login-container');
const appContainer = document.getElementById('app-container');
const loginForm = document.getElementById('login-form');
const userEmail = document.getElementById('user-email');
const logoutBtn = document.getElementById('logout-btn');
const navLinks = document.querySelectorAll('nav a');
const sections = document.querySelectorAll('section');

// Training list elements
const trainingList = document.getElementById('training-list');
const questionsList = document.getElementById('questions-list');
const exercisesList = document.getElementById('exercises-list');
const tasksList = document.getElementById('tasks-list');
const trainingsplanList = document.getElementById('trainingsplan-list');

// User management elements
const connectionsListElement = document.getElementById('connections-list');
const emptyConnectionsElement = document.getElementById('empty-connections');
const trainerEmailInput = document.getElementById('trainer-email');
const trainerTypeSelect = document.getElementById('trainer-type');
const exerciseAccessCheckbox = document.getElementById('exercise-access');
const selfAssessmentAccessCheckbox = document.getElementById('selfassessment-access');
const addTrainerBtn = document.getElementById('add-trainer-btn');

// --- NEW: Timeframe selector for exercises chart ---
let exercisesTimeframe = 'all'; // default
const exercisesSection = document.getElementById('exercises-section');
let exercisesTimeframeSelect = null;

// Event Listeners
if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
}
if (logoutBtn) {
    logoutBtn.addEventListener('click', handleLogout);
}
if (navLinks) {
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}
// Add event listener for the add trainer button
if (addTrainerBtn) {
    addTrainerBtn.addEventListener('click', function() {
        // The actual implementation is in user-management-dialogs.js
        if (typeof handleAddTrainer === 'function') {
            handleAddTrainer();
        } else {
            console.error("handleAddTrainer function not found");
        }
    });
}

// Check auth state
auth.onAuthStateChanged(user => {
    if (user) {
        // User is signed in
        console.log("User signed in:", user.email);

        // Ensure user profile exists
        ensureUserProfile(user)
            .then(userProfile => {
                // Check if user is a trainer
                if (userProfile && (userProfile.role === 'TRAINER' || userProfile.role === 'MENTAL_TRAINER')) {
                    console.log("User is a trainer, redirecting to trainer dashboard");
                    window.location.href = 'trainer-dashboard.html';
                    return;
                }

                // Regular player flow
                showApp(user);
                loadData();
            })
            .catch(error => {
                console.error("Error ensuring user profile:", error);
                // Show app anyway to prevent login loops
                showApp(user);
                loadData();
            });
    } else {
        // User is signed out
        console.log("User signed out");
        showLogin();
    }
});

// Ensure user profile exists
function ensureUserProfile(user) {
    return new Promise((resolve, reject) => {
        const userId = user.uid;

        db.collection('user_profiles')
            .doc(userId)
            .get()
            .then(doc => {
                if (doc.exists) {
                    console.log("User profile exists");
                    const userProfile = doc.data();
                    console.log("User role:", userProfile.role);
                    resolve(userProfile);
                } else {
                    console.log("Creating new user profile");
                    // Create default profile
                    const defaultProfile = {
                        displayName: user.email.split('@')[0],
                        email: user.email,
                        role: 'PLAYER',
                        userId: userId,
                        lastUpdated: Date.now()
                    };

                    db.collection('user_profiles')
                        .doc(userId)
                        .set(defaultProfile)
                        .then(() => {
                            console.log("User profile created successfully");
                            resolve(defaultProfile);
                        })
                        .catch(error => {
                            console.error("Error creating user profile:", error);
                            reject(error);
                        });
                }
            })
            .catch(error => {
                console.error("Error checking user profile:", error);
                reject(error);
            });
    });
}

// Functions
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    // Show loading indicator
    const loginButton = document.querySelector('#login-form button');
    const originalButtonText = loginButton.textContent;
    loginButton.textContent = "Logging in...";
    loginButton.disabled = true;

    auth.signInWithEmailAndPassword(email, password)
        .then(userCredential => {
            console.log("Login successful");
        })
        .catch(error => {
            console.error("Login failed:", error);
            alert(`Login failed: ${error.message}`);

            // Reset button
            loginButton.textContent = originalButtonText;
            loginButton.disabled = false;
        });
}

function handleLogout() {
    auth.signOut();
}

function showLogin() {
    loginContainer.classList.remove('hidden');
    appContainer.classList.add('hidden');
}

function showApp(user) {
    loginContainer.classList.add('hidden');
    appContainer.classList.remove('hidden');
    userEmail.textContent = user.email;
}

function handleNavigation(e) {
    e.preventDefault();

    // Remove active class from all links
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to clicked link
    e.target.classList.add('active');

    // Hide all sections
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // Show selected section
    const sectionId = `${e.target.dataset.section}-section`;
    document.getElementById(sectionId).classList.add('active');
}

// Chart.js chart instances
let trainingChartInstance = null;
let exercisesChartInstance = null; // NEW: for Übungen chart

function loadData() {
    loadTrainingRecords();
    loadQuestionRecords();
    loadExerciseRecords();
    loadTaskHistory();
    loadTrainingsplanHistory();
    loadUserConnections();

    // Check user role and adjust UI accordingly
    checkUserRole();
}

// --- Selbsteinschätzung (was Ergebniserfassung) ---
function loadTrainingRecords() {
    const userId = auth.currentUser.uid;

    db.collection('training_records')
        .where('userId', '==', userId)
        .orderBy('lastUpdated', 'desc')
        .get()
        .then(snapshot => {
            trainingList.innerHTML = '';

            if (snapshot.empty) {
                trainingList.innerHTML = '<p>Keine Trainingsdaten vorhanden</p>';
                if (trainingChartInstance) trainingChartInstance.destroy();
                return;
            }

            // Deduplicate records based on date and type
            const uniqueRecords = {};
            snapshot.forEach(doc => {
                const record = doc.data();
                const key = `${record.date}_${record.type}`;
                if (!uniqueRecords[key] || record.lastUpdated > uniqueRecords[key].lastUpdated) {
                    uniqueRecords[key] = record;
                }
            });

            // Prepare data for chart: average score per date
            const chartData = {};
            Object.values(uniqueRecords).forEach(record => {
                // Use record.date instead of record.lastUpdated
                const date = new Date(record.date).toLocaleDateString('de-DE');
                const avgScore = record.items && record.items.length
                    ? record.items.reduce((sum, item) => sum + (item.score || 0), 0) / record.items.length
                    : 0;
                if (!chartData[date]) chartData[date] = [];
                chartData[date].push(avgScore);
            });

            const labels = Object.keys(chartData).sort((a, b) => new Date(a) - new Date(b));
            const data = labels.map(date => {
                const scores = chartData[date];
                return scores.length ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(2) : 0;
            });

            // Render chart as a line chart
            const ctx = document.getElementById('training-chart').getContext('2d');
            if (trainingChartInstance) trainingChartInstance.destroy();
            trainingChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels,
                    datasets: [{
                        label: 'Ø Score',
                        data,
                        fill: false,
                        borderColor: '#eebbc3',
                        backgroundColor: '#eebbc3',
                        tension: 0.2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                        title: { display: true, text: 'Durchschnittlicher Score pro Tag' }
                    },
                    scales: {
                        y: { beginAtZero: true, max: 10 }
                    }
                }
            });

            // Display the unique records
            Object.values(uniqueRecords).forEach(record => {
                // Use record.date instead of record.lastUpdated
                const date = new Date(record.date).toLocaleDateString('de-DE');

                const itemDiv = document.createElement('div');
                itemDiv.className = 'history-item';

                const header = document.createElement('h3');
                header.innerHTML = `
                    <span>${date} - ${record.type}</span>
                    <button class="toggle-btn">+</button>
                `;

                const details = document.createElement('div');
                details.className = 'history-item-details';

                let itemsHtml = '<ul>';
                record.items.forEach(item => {
                    itemsHtml += `<li>${item.title}: ${item.score}/10</li>`;
                });
                itemsHtml += '</ul>';

                details.innerHTML = itemsHtml;

                itemDiv.appendChild(header);
                itemDiv.appendChild(details);

                // Toggle details
                header.querySelector('.toggle-btn').addEventListener('click', () => {
                    itemDiv.classList.toggle('expanded');
                    header.querySelector('.toggle-btn').textContent =
                        itemDiv.classList.contains('expanded') ? '-' : '+';
                });

                trainingList.appendChild(itemDiv);
            });
        })
        .catch(error => {
            console.error('Error loading training records:', error);
            trainingList.innerHTML = `<p>Fehler beim Laden der Daten: ${error.message}</p>`;
        });
}

// --- Fragen (NO chart) ---
function loadQuestionRecords() {
    const userId = auth.currentUser.uid;

    db.collection('question_records')
        .where('userId', '==', userId)
        .orderBy('lastUpdated', 'desc')
        .get()
        .then(snapshot => {
            questionsList.innerHTML = '';

            if (snapshot.empty) {
                questionsList.innerHTML = '<p>Keine Fragendaten vorhanden</p>';
                return;
            }

            // Deduplicate records based on date and type
            const uniqueRecords = {};
            snapshot.forEach(doc => {
                const record = doc.data();
                const key = `${record.date}_${record.type}`;
                if (!uniqueRecords[key] || record.lastUpdated > uniqueRecords[key].lastUpdated) {
                    uniqueRecords[key] = record;
                }
            });

            // Display the unique records (NO chart)
            Object.values(uniqueRecords).forEach(record => {
                // Use record.date instead of record.lastUpdated
                const date = new Date(record.date).toLocaleDateString('de-DE');

                const itemDiv = document.createElement('div');
                itemDiv.className = 'history-item';

                const header = document.createElement('h3');
                header.innerHTML = `
                    <span>${date} - ${record.type}</span>
                    <button class="toggle-btn">+</button>
                `;

                const details = document.createElement('div');
                details.className = 'history-item-details';

                let questionsHtml = '<ul>';
                record.questions.forEach(question => {
                    questionsHtml += `<li><strong>${question.title}</strong>: ${question.answer}</li>`;
                });
                questionsHtml += '</ul>';

                details.innerHTML = questionsHtml;

                itemDiv.appendChild(header);
                itemDiv.appendChild(details);

                // Toggle details
                header.querySelector('.toggle-btn').addEventListener('click', () => {
                    itemDiv.classList.toggle('expanded');
                    header.querySelector('.toggle-btn').textContent =
                        itemDiv.classList.contains('expanded') ? '-' : '+';
                });

                questionsList.appendChild(itemDiv);
            });
        })
        .catch(error => {
            console.error('Error loading question records:', error);
            questionsList.innerHTML = `<p>Fehler beim Laden der Daten: ${error.message}</p>`;
        });
}

// --- Übungen (with line chart) ---
function loadExerciseRecords() {
    const userId = auth.currentUser.uid;

    // ... timeframe selector for overview chart (unchanged) ...

    db.collection('exercise_records')
        .where('userId', '==', userId)
        .orderBy('lastUpdated', 'desc')
        .get()
        .then(snapshot => {
            exercisesList.innerHTML = '';

            if (snapshot.empty) {
                exercisesList.innerHTML = '<p>Keine Übungsdaten vorhanden</p>';
                if (exercisesChartInstance) exercisesChartInstance.destroy();
                return;
            }

            // --- 1. Define exercise categories and mapping ---
            const exerciseCategories = {
                "Breakbuilding": [
                    { id: "t_break", name: "T Break" },
                    { id: "y_break", name: "Y Break" },
                    { id: "line_up", name: "Line Up" },
                    { id: "eight_reds", name: "8reds Break" },
                    { id: "five_reds", name: "5reds clear" },
                    { id: "fifty_plus", name: "50+ Break" },
                    { id: "frames", name: "Frames" },
                    { id: "x_break", name: "X Break" },
                    { id: "black_routines", name: "Black Routines" },
                    { id: "brown_to_reds", name: "Brown to reds" },
                    { id: "bc_to_reds", name: "BC to reds" },
                    { id: "pj_routines", name: "PJ Routines" },
                    { id: "random_bilder", name: "Random Bilder" }
                ],
                "Potting": [
                    { id: "blue_to_pink", name: "Blau zu Pink" },
                    { id: "long_shots", name: "8 Long shots" },
                    { id: "medium_to_blue", name: "Medium zu Blau" },
                    { id: "rest", name: "Rest" }
                ],
                "Safeties": [
                    { id: "three_reds_save", name: "3reds Save" },
                    { id: "snooker_legen", name: "Snooker legen" },
                    { id: "black_to_bc", name: "Von Schwarz zu BC" },
                    { id: "blue_pink_black", name: "Blau pink Schwarz" }
                ],
                "Splits": [
                    { id: "yellow_splits", name: "Gelb splits" },
                    { id: "green", name: "Grün" },
                    { id: "brown", name: "Braun" },
                    { id: "blue", name: "Blau" },
                    { id: "black", name: "Schwarz" }
                ],
                "Technik": [
                    { id: "blue_doubletouch", name: "Blue Doubletouch" },
                    { id: "langer_stoss", name: "Langer Stoß" },
                    { id: "gerader_stoss_2_kreiden", name: "Gerader Stoß 2 Kreiden" }
                ],
                "Stellungsspiel": [
                    { id: "stellungsspiel_gelb", name: "Gelb" },
                    { id: "stellungsspiel_gruen", name: "Grün" },
                    { id: "stellungsspiel_braun", name: "Braun" },
                    { id: "blau_durch_bc", name: "Blau durch BC" },
                    { id: "hohe_schwarze", name: "Hohe Schwarze" }
                ]
            };

            // --- 2. Group records by exerciseId ---
            const exerciseGroups = {};
            snapshot.forEach(doc => {
                const record = doc.data();
                if (!exerciseGroups[record.exerciseId]) {
                    exerciseGroups[record.exerciseId] = [];
                }
                exerciseGroups[record.exerciseId].push(record);
            });

            // --- 3. Group exerciseGroups by category ---
            const categoryGroups = {};
            Object.entries(exerciseCategories).forEach(([category, exercises]) => {
                categoryGroups[category] = exercises
                    .map(ex => ({
                        ...ex,
                        records: exerciseGroups[ex.id] || []
                    }))
                    .filter(ex => ex.records.length > 0); // Only show exercises with data
            });

            // --- 4. Render categories and expandable exercises ---
            Object.entries(categoryGroups).forEach(([category, exercises]) => {
                if (exercises.length === 0) return; // Skip empty categories

                // Category container
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'exercise-category';

                // Category header
                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'exercise-category-header';
                categoryHeader.textContent = category;

                // Exercises container (hidden by default)
                const exercisesContainer = document.createElement('div');
                exercisesContainer.className = 'exercise-category-exercises';
                exercisesContainer.style.display = 'none';

                // Add exercises to container
                exercises.forEach(ex => {
                    // Reuse your existing card rendering logic, but use ex.name for display
                    const records = ex.records;
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'history-item';

                    const header = document.createElement('h3');
                    header.innerHTML = `
                        <span>${ex.name} (${records.length} Einträge)</span>
                        <button class="toggle-btn">+</button>
                    `;

                    const details = document.createElement('div');
                    details.className = 'history-item-details';

                    // Timeframe selector for this card
                    const cardTimeframeSelect = document.createElement('select');
                    cardTimeframeSelect.style.marginBottom = '12px';
                    cardTimeframeSelect.style.marginRight = '12px';
                    cardTimeframeSelect.style.padding = '4px 10px';
                    cardTimeframeSelect.style.borderRadius = '6px';
                    cardTimeframeSelect.style.border = '1px solid #e0e7ff';
                    cardTimeframeSelect.style.background = '#fff';
                    cardTimeframeSelect.innerHTML = `
                        <option value="7">Letzte 7 Tage</option>
                        <option value="30">Letzte 30 Tage</option>
                        <option value="all" selected>Alle</option>
                    `;
                    let cardTimeframe = 'all';

                    // Chart container for this card
                    const chartContainer = document.createElement('div');
                    chartContainer.style.width = '100%';
                    chartContainer.style.marginBottom = '24px';
                    chartContainer.style.display = 'none';

                    // Canvas for chart
                    const chartCanvas = document.createElement('canvas');
                    chartCanvas.height = 80;
                    chartCanvas.style.maxWidth = '100%';
                    chartCanvas.id = `exercise-chart-${ex.id}-${Math.random().toString(36).substr(2, 9)}`;
                    chartContainer.appendChild(chartCanvas);

                    // Dates with saved time
                    const datesWithTime = records
                        .filter(r => typeof r.timeInMinutes === 'number' && r.timeInMinutes > 0)
                        .map(r => ({
                            date: new Date(r.timestamp).toLocaleDateString('de-DE'),
                            time: r.timeInMinutes
                        }));

                    // Dates with saved time (as DOM)
                    let timesDiv = null;
                    if (datesWithTime.length > 0) {
                        timesDiv = document.createElement('div');
                        timesDiv.style.marginTop = '24px';
                        timesDiv.style.background = '#fff';
                        timesDiv.style.borderRadius = '10px';
                        timesDiv.style.boxShadow = '0 2px 12px rgba(35,41,70,0.04)';
                        timesDiv.style.padding = '16px';
                        timesDiv.style.maxWidth = '100%';

                        timesDiv.innerHTML = '<strong>Alle gespeicherten Zeiten:</strong>';
                        const timesUl = document.createElement('ul');
                        timesUl.style.paddingLeft = '20px';
                        timesUl.style.marginTop = '8px';
                        datesWithTime.forEach(entry => {
                            const li = document.createElement('li');
                            li.textContent = `${entry.date}: ${entry.time} min`;
                            timesUl.appendChild(li);
                        });
                        timesDiv.appendChild(timesUl);
                    }

                    // Append all to details (order: timeframe, chart, times)
                    details.appendChild(cardTimeframeSelect);
                    details.appendChild(chartContainer);
                    if (timesDiv) details.appendChild(timesDiv);

                    itemDiv.appendChild(header);
                    itemDiv.appendChild(details);

                    // Chart rendering logic for this card
                    let cardChartInstance = null;
                    function renderCardChart() {
                        let filteredRecords = records.filter(r => typeof r.score === 'number');
                        if (cardTimeframe !== 'all') {
                            const days = parseInt(cardTimeframe, 10);
                            const cutoff = new Date();
                            cutoff.setDate(cutoff.getDate() - days + 1);
                            filteredRecords = filteredRecords.filter(r => {
                                const dateObj = new Date(new Date(r.timestamp).toLocaleDateString('de-DE').split('.').reverse().join('-'));
                                return dateObj >= cutoff;
                            });
                        }
                        const chartLabels = filteredRecords.map(r => new Date(r.timestamp).toLocaleDateString('de-DE'));
                        const chartScores = filteredRecords.map(r => r.score);

                        const cardMaxScore = Math.max(...chartScores, 1);

                        if (cardChartInstance) cardChartInstance.destroy();

                        cardChartInstance = new Chart(chartCanvas.getContext('2d'), {
                            type: 'line',
                            data: {
                                labels: chartLabels,
                                datasets: [{
                                    label: 'Score',
                                    data: chartScores,
                                    fill: false,
                                    borderColor: '#eebbc3',
                                    backgroundColor: '#eebbc3',
                                    tension: 0.2
                                }]
                            },
                            options: {
                                responsive: true,
                                plugins: {
                                    legend: { display: false },
                                    title: { display: true, text: 'Score Verlauf' }
                                },
                                scales: {
                                    y: { beginAtZero: true, max: Math.ceil(cardMaxScore) }
                                }
                            }
                        });
                    }

                    cardTimeframeSelect.addEventListener('change', () => {
                        cardTimeframe = cardTimeframeSelect.value;
                        renderCardChart();
                    });

                    // --- Make the whole card clickable for expand/collapse ---
                    itemDiv.addEventListener('click', (e) => {
                        // Prevent toggling when clicking inside the details (e.g., select)
                        if (e.target.tagName === 'SELECT' || e.target.tagName === 'OPTION') return;
                        const expanded = itemDiv.classList.toggle('expanded');
                        if (expanded) {
                            chartContainer.style.display = 'block';
                            renderCardChart();
                        } else {
                            chartContainer.style.display = 'none';
                            if (cardChartInstance) {
                                cardChartInstance.destroy();
                                cardChartInstance = null;
                            }
                        }
                    });

                    exercisesContainer.appendChild(itemDiv);
                });

                // Expand/collapse logic for category
                let categoryExpanded = false;
                categoryHeader.addEventListener('click', () => {
                    categoryExpanded = !categoryExpanded;
                    exercisesContainer.style.display = categoryExpanded ? 'block' : 'none';
                    categoryHeader.classList.toggle('expanded', categoryExpanded);
                });

                categoryDiv.appendChild(categoryHeader);
                categoryDiv.appendChild(exercisesContainer);
                exercisesList.appendChild(categoryDiv);
            });

        })
        .catch(error => {
            console.error('Error loading exercise records:', error);
            exercisesList.innerHTML = `<p>Fehler beim Laden der Daten: ${error.message}</p>`;
        });
}

function loadTaskHistory() {
    const userId = auth.currentUser.uid;

    db.collection('task_history')
        .where('userId', '==', userId)
        .orderBy('lastUpdated', 'desc')
        .get()
        .then(snapshot => {
            tasksList.innerHTML = '';

            if (snapshot.empty) {
                tasksList.innerHTML = '<p>Keine Aufgabendaten vorhanden</p>';
                return;
            }

            snapshot.forEach(doc => {
                const entry = doc.data();

                const itemDiv = document.createElement('div');
                itemDiv.className = 'history-item';

                const header = document.createElement('h3');
                header.innerHTML = `
                    <span>Woche: ${entry.weekKey} - ${entry.totalPoints} Punkte</span>
                    <button class="toggle-btn">+</button>
                `;

                const details = document.createElement('div');
                details.className = 'history-item-details';

                let tasksHtml = '<ul>';
                entry.taskCompletions.forEach(task => {
                    tasksHtml += `<li>${task.title} (${task.category}): ${task.completions}/${task.maxCompletions} - ${task.points} Punkte</li>`;
                });
                tasksHtml += '</ul>';

                details.innerHTML = tasksHtml;

                itemDiv.appendChild(header);
                itemDiv.appendChild(details);

                // Toggle details
                header.querySelector('.toggle-btn').addEventListener('click', () => {
                    itemDiv.classList.toggle('expanded');
                    header.querySelector('.toggle-btn').textContent =
                        itemDiv.classList.contains('expanded') ? '-' : '+';
                });

                tasksList.appendChild(itemDiv);
            });
        })
        .catch(error => {
            console.error('Error loading task history:', error);
            tasksList.innerHTML = `<p>Fehler beim Laden der Daten: ${error.message}</p>`;
        });
}

function loadTrainingsplanHistory() {
    const userId = auth.currentUser.uid;

    db.collection('trainingsplan_history')
        .where('userId', '==', userId)
        .orderBy('lastUpdated', 'desc')
        .get()
        .then(snapshot => {
            trainingsplanList.innerHTML = '';

            if (snapshot.empty) {
                trainingsplanList.innerHTML = '<p>Keine Trainingsplan-Daten vorhanden</p>';
                return;
            }

            // Group plans by year and month to handle the transition from weekly to monthly plans
            const monthlyPlans = {};

            snapshot.forEach(doc => {
                const plan = doc.data();
                if (!plan.weekStartDate) return; // Skip invalid data

                // Parse the date and extract year and month
                const dateParts = plan.weekStartDate.split('-');
                if (dateParts.length < 2) return; // Skip invalid date format

                const year = dateParts[0];
                const month = dateParts[1];
                const monthKey = `${year}-${month}`;

                // If this month already exists, merge items or use the one with more items
                if (monthlyPlans[monthKey]) {
                    // If the current plan has more items, use it instead
                    if (plan.items && plan.items.length > monthlyPlans[monthKey].items.length) {
                        monthlyPlans[monthKey] = plan;
                    }
                } else {
                    monthlyPlans[monthKey] = plan;
                }
            });

            // Format month names in German
            const monthNames = [
                'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
                'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
            ];

            // Display each monthly plan
            Object.keys(monthlyPlans).sort().reverse().forEach(monthKey => {
                const plan = monthlyPlans[monthKey];
                const dateParts = plan.weekStartDate.split('-');
                const year = dateParts[0];
                const month = parseInt(dateParts[1], 10);

                const itemDiv = document.createElement('div');
                itemDiv.className = 'history-item';

                const header = document.createElement('h3');
                header.innerHTML = `
                    <span>${monthNames[month-1]} ${year} (${plan.items.length} Übungen)</span>
                    <button class="toggle-btn">+</button>
                `;

                const details = document.createElement('div');
                details.className = 'history-item-details';

                let itemsHtml = '<ul>';
                plan.items.forEach(item => {
                    const status = item.isChecked ? '✓' : '✗';
                    itemsHtml += `<li>${status} ${item.name} (${item.completionCount}/${item.targetCount})</li>`;
                });
                itemsHtml += '</ul>';

                details.innerHTML = itemsHtml;

                itemDiv.appendChild(header);
                itemDiv.appendChild(details);

                // Toggle details
                header.querySelector('.toggle-btn').addEventListener('click', () => {
                    itemDiv.classList.toggle('expanded');
                    header.querySelector('.toggle-btn').textContent =
                        itemDiv.classList.contains('expanded') ? '-' : '+';
                });

                trainingsplanList.appendChild(itemDiv);
            });
        })
        .catch(error => {
            console.error('Error loading trainingsplan history:', error);
            trainingsplanList.innerHTML = `<p>Fehler beim Laden der Daten: ${error.message}</p>`;
        });
}
