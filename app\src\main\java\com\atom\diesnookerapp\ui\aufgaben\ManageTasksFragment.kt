package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.content.Context
// Removed duplicate android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.atom.diesnookerapp.databinding.DialogAddEditTaskBinding
import com.atom.diesnookerapp.databinding.FragmentManageTasksBinding
import java.util.UUID

class ManageTasksFragment : Fragment() {

    private var _binding: FragmentManageTasksBinding? = null
    private val binding get() = _binding!!

    // private companion object { // CREATE_NEW_CATEGORY will be initialized in onViewCreated
    //     private const val CREATE_NEW_CATEGORY = "[Neue Kategorie erstellen]"
    // }
    private lateinit var CREATE_NEW_CATEGORY: String


    private lateinit var taskManager: TaskManager
    private lateinit var manageTasksAdapter: ManageTasksAdapter
    private val categoryExpansionState = mutableMapOf<String, Boolean>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentManageTasksBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        CREATE_NEW_CATEGORY = getString(R.string.create_new_category_option)
        taskManager = TaskManager(requireContext())

        setupRecyclerView()

        binding.addTaskFab.setOnClickListener {
            showAddEditTaskDialog(null)
        }

        loadTasks()
    }

    private fun setupRecyclerView() {
        manageTasksAdapter = ManageTasksAdapter(
            onHeaderClick = { headerItem ->
                // Toggle expansion state
                val currentExpansion = categoryExpansionState[headerItem.categoryId] ?: true // Default to expanded if not found
                categoryExpansionState[headerItem.categoryId] = !currentExpansion
                loadTasks() // Reload and reconstruct the list
            },
            onEditClick = { task ->
                showAddEditTaskDialog(task)
            },
            onDeleteClick = { task ->
                handleDeleteTask(task)
            }
        )
        binding.tasksRecyclerView.apply {
            adapter = manageTasksAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    private fun loadTasks() {
        val allTasks = taskManager.getAllTasks()
        val groupedTasks = allTasks.groupBy { it.category }

        // Sort categories by display name for consistent order
        val sortedCategoryIds = groupedTasks.keys.sortedBy { TaskCategoryHelper.getDisplayName(it) }

        val listItems = mutableListOf<ManageTaskListItem>()
        for (categoryId in sortedCategoryIds) {
            val categoryDisplayName = TaskCategoryHelper.getDisplayName(categoryId)
            // Get current expansion state, default to false (collapsed) if not set
            val isExpanded = categoryExpansionState.getOrPut(categoryId) { false }

            listItems.add(ManageTaskListItem.HeaderItem(categoryId, categoryDisplayName, isExpanded))
            if (isExpanded) {
                groupedTasks[categoryId]?.sortedBy { it.title }?.forEach { task -> // Sort tasks within category
                    listItems.add(ManageTaskListItem.TaskDataItem(task))
                }
            }
        }

        manageTasksAdapter.submitList(listItems)
        binding.emptyViewTasks.isVisible = allTasks.isEmpty()
    }

    private fun showAddEditTaskDialog(task: Task?) {
        val dialogBinding = DialogAddEditTaskBinding.inflate(LayoutInflater.from(requireContext()))
        val dialogBuilder = ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .setTitle(if (task == null) getString(R.string.add_task_dialog_title) else getString(R.string.edit_task_dialog_title))

        // Initial State Setup for category fields
        dialogBinding.taskCategoryInputLayout.visibility = View.VISIBLE
        dialogBinding.newCategoryNameInputLayout.visibility = View.GONE
        dialogBinding.taskCategoryAutoCompleteTextView.setText("", false)
        dialogBinding.newCategoryNameEditText.setText("")

        // Populate Spinners/AutoCompleteTextViews
        // Categories
        val categoryItems = TaskCategoryHelper.getAllDisplayNames().sorted().toMutableList()
        categoryItems.add(CREATE_NEW_CATEGORY) // Add option to create new
        val categoryAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, categoryItems)
        val categoryAutoCompleteTextView = (dialogBinding.taskCategoryInputLayout.editText as? AutoCompleteTextView)
        categoryAutoCompleteTextView?.setAdapter(categoryAdapter)
        categoryAutoCompleteTextView?.onItemClickListener =
            AdapterView.OnItemClickListener { parent: AdapterView<*>, view: View?, position: Int, id: Long ->
                val selectedItemString = parent.getItemAtPosition(position) as String
                if (selectedItemString == CREATE_NEW_CATEGORY) {
                    dialogBinding.taskCategoryInputLayout.visibility = View.GONE
                    dialogBinding.taskCategoryAutoCompleteTextView.setText("", false) // Clear selection in dropdown
                    dialogBinding.newCategoryNameInputLayout.visibility = View.VISIBLE
                    dialogBinding.newCategoryNameEditText.setText("") // Clear new category field
                    dialogBinding.newCategoryNameEditText.requestFocus()
                    // Attempt to show keyboard
                    // val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
                    // imm?.showSoftInput(dialogBinding.newCategoryNameEditText, InputMethodManager.SHOW_IMPLICIT)
                } else {
                    // User selected an existing category from the dropdown
                    dialogBinding.taskCategoryInputLayout.visibility = View.VISIBLE
                    dialogBinding.newCategoryNameInputLayout.visibility = View.GONE
                    // The AutoCompleteTextView's text is already set by the click
                }
            }

        // Frequency
        val frequencyDisplayMap = mapOf(TaskFrequency.DAILY to "Täglich", TaskFrequency.WEEKLY to "Wöchentlich")
        val frequencyOptions = frequencyDisplayMap.values.toList()
        val frequencyAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, frequencyOptions)
        val frequencyAutoCompleteTextView = (dialogBinding.taskFrequencyInputLayout.editText as? AutoCompleteTextView)
        frequencyAutoCompleteTextView?.setAdapter(frequencyAdapter)
        frequencyAutoCompleteTextView?.onItemClickListener = AdapterView.OnItemClickListener { parent: AdapterView<*>, view: View?, position: Int, id: Long ->
            // val selectedDisplayName = frequencyOptions[position] // This would require parent.getItemAtPosition(position)
            val selectedDisplayName = parent.getItemAtPosition(position) as String // Corrected to get from parent
            if (selectedDisplayName == frequencyDisplayMap[TaskFrequency.WEEKLY]) {
                dialogBinding.taskWeeklyFrequencyCountLayout.visibility = View.VISIBLE
            } else {
                dialogBinding.taskWeeklyFrequencyCountLayout.visibility = View.GONE
            }
        }

        if (task != null) { // Editing mode
            dialogBinding.taskTitleEditText.setText(task.title)
            dialogBinding.taskDescriptionEditText.setText(task.description ?: "")

            val categoryId = task.category
            val displayName = TaskCategoryHelper.getDisplayName(categoryId)

            if (displayName == categoryId && !TaskCategoryHelper.categoryDisplayMap.containsKey(categoryId)) {
                // This implies it's a custom category not in the predefined map.
                dialogBinding.taskCategoryInputLayout.visibility = View.GONE
                categoryAutoCompleteTextView?.setText("", false) // Clear dropdown
                dialogBinding.newCategoryNameInputLayout.visibility = View.VISIBLE
                dialogBinding.newCategoryNameEditText.setText(categoryId) // Show the custom category name in the EditText
            } else {
                // Predefined category
                dialogBinding.taskCategoryInputLayout.visibility = View.VISIBLE
                dialogBinding.newCategoryNameInputLayout.visibility = View.GONE
                categoryAutoCompleteTextView?.setText(displayName, false) // Set dropdown to display name
                dialogBinding.newCategoryNameEditText.setText("") // Clear new category field
            }

            frequencyAutoCompleteTextView?.setText(frequencyDisplayMap[task.frequency], false)
            if (task.frequency == TaskFrequency.WEEKLY) {
                dialogBinding.taskWeeklyFrequencyCountLayout.visibility = View.VISIBLE
                dialogBinding.taskWeeklyFrequencyCountEditText.setText(task.weeklyFrequencyCount?.toString() ?: "1")
            } else {
                dialogBinding.taskWeeklyFrequencyCountLayout.visibility = View.GONE
            }
            dialogBinding.taskPointsEditText.setText(task.points.toString())
        } else {
            // Default to Daily for new tasks, and hide weekly count
            // This is already handled by the "Initial State Setup" part for taskCategory fields.
            frequencyAutoCompleteTextView?.setText(frequencyDisplayMap[TaskFrequency.DAILY], false)
            dialogBinding.taskWeeklyFrequencyCountLayout.visibility = View.GONE
        }

        val alertDialog = dialogBuilder.show()

        dialogBinding.saveButton.setOnClickListener {
            val title = dialogBinding.taskTitleEditText.text.toString().trim()
            val description = dialogBinding.taskDescriptionEditText.text.toString().trim()
            val selectedFrequencyDisplayName = (dialogBinding.taskFrequencyInputLayout.editText as? AutoCompleteTextView)?.text.toString()
            val pointsString = dialogBinding.taskPointsEditText.text.toString().trim()
            val weeklyCountString = dialogBinding.taskWeeklyFrequencyCountEditText.text.toString()

            var finalCategoryNameId: String = "" // Initialize with a default empty string
            var isNewCategoryEntryValid = true

            if (dialogBinding.newCategoryNameInputLayout.visibility == View.VISIBLE) {
                finalCategoryNameId = dialogBinding.newCategoryNameEditText.text.toString().trim()
                if (finalCategoryNameId.isEmpty()) {
                    dialogBinding.newCategoryNameInputLayout.error = getString(R.string.toast_category_info_invalid) // Or a more specific "cannot be empty" string
                    isNewCategoryEntryValid = false // Mark as invalid
                } else {
                    dialogBinding.newCategoryNameInputLayout.error = null // Clear error
                }
            } else { // Dropdown was visible
                val selectedCategoryDisplayNameFromDropdown = categoryAutoCompleteTextView?.text.toString().trim()
                if (selectedCategoryDisplayNameFromDropdown.isEmpty() || selectedCategoryDisplayNameFromDropdown == CREATE_NEW_CATEGORY) {
                    dialogBinding.taskCategoryInputLayout.error = getString(R.string.toast_category_info_invalid) // Or a more specific "select or create"
                    isNewCategoryEntryValid = false // Mark as invalid
                } else {
                    dialogBinding.taskCategoryInputLayout.error = null // Clear error
                    // If it's from dropdown, it could be a display name or a custom name typed into dropdown
                    // if user didn't click "[Create New...]" but typed something not in list.
                    finalCategoryNameId = TaskCategoryHelper.getCategoryId(selectedCategoryDisplayNameFromDropdown) ?: selectedCategoryDisplayNameFromDropdown
                }
            }

            if (!isNewCategoryEntryValid) { // Check the flag
                Toast.makeText(requireContext(), getString(R.string.toast_category_info_invalid), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (title.isEmpty() || pointsString.isEmpty() || selectedFrequencyDisplayName.isEmpty()) {
                Toast.makeText(requireContext(), getString(R.string.toast_required_fields_missing), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            // Category emptiness is handled by isNewCategoryEntryValid logic

            val points = pointsString.toIntOrNull()
            if (points == null) {
                Toast.makeText(requireContext(), getString(R.string.toast_invalid_points_value), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            val frequency = frequencyDisplayMap.entries.find { it.value == selectedFrequencyDisplayName }?.key
            if (frequency == null) {
                Toast.makeText(requireContext(), getString(R.string.toast_invalid_frequency_selected), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            var weeklyFrequencyCount: Int? = null
            if (frequency == TaskFrequency.WEEKLY) {
                weeklyFrequencyCount = weeklyCountString.toIntOrNull() ?: 1
                if (weeklyFrequencyCount <= 0) weeklyFrequencyCount = 1 // Ensure positive
            }

            if (task == null) { // Add new task
                val newTask = Task(
                    id = UUID.randomUUID().toString(),
                    title = title,
                    description = description.ifEmpty { null },
                    category = finalCategoryNameId, // Use the ID (duplicate removed)
                    frequency = frequency,
                    points = points,
                    weeklyFrequencyCount = weeklyFrequencyCount,
                    completions = mutableListOf(),
                    customPoints = mutableMapOf()
                )
                taskManager.addTask(newTask)
                Toast.makeText(requireContext(), getString(R.string.toast_task_added), Toast.LENGTH_SHORT).show()
            } else { // Update existing task
                val updatedTask = task.copy(
                    title = title,
                    description = description.ifEmpty { null },
                    category = finalCategoryNameId, // Corrected: Use the ID
                    frequency = frequency,
                    points = points,
                    weeklyFrequencyCount = weeklyFrequencyCount
                    // completions and customPoints are preserved from the original task
                )
                taskManager.updateTask(updatedTask)
                Toast.makeText(requireContext(), getString(R.string.toast_task_updated), Toast.LENGTH_SHORT).show()
            }
            loadTasks()
            alertDialog.dismiss()
        }

        dialogBinding.cancelButton.setOnClickListener {
            alertDialog.dismiss()
        }
    }

    private fun handleDeleteTask(task: Task) {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.delete_task_dialog_title))
            .setMessage(getString(R.string.delete_task_dialog_message, task.title))
            .setPositiveButton(getString(R.string.button_delete)) { _, _ ->
                taskManager.removeTask(task.id)
                loadTasks()
                Toast.makeText(requireContext(), getString(R.string.toast_task_deleted, task.title), Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton(getString(R.string.button_cancel), null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
