package com.atom.diesnookerapp.data.firebase

import org.threeten.bp.LocalDate // Changed to org.threeten.bp.LocalDate

data class WeeklyTrainingSummary(
    // id and userId are inherited from FirebaseModel.
    // weekStartDate and totalDurationMillis are specific to this class.
    var weekStartDate: String = "",
    var totalDurationMillis: Long = 0L
) : FirebaseModel() { // Extends FirebaseModel abstract class, calling its constructor

    companion object {
        fun fromData(userIdStr: String, startDate: org.threeten.bp.LocalDate, durationMillis: Long): WeeklyTrainingSummary {
            val summary = WeeklyTrainingSummary(
                weekStartDate = startDate.toString(), // Uses org.threeten.bp.LocalDate.toString()
                totalDurationMillis = durationMillis
            )
            summary.userId = userIdStr // Set the inherited userId property
            // summary.id is automatically set by Firestore via @DocumentId in FirebaseModel
            // summary.lastUpdated is set by FirebaseModel's default initializer
            return summary
        }
    }
}
