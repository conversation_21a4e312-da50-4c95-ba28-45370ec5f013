package com.atom.diesnookerapp.ui.aufgaben

import org.threeten.bp.LocalDate

/**
 * Represents a weekly summary of task completions
 */
data class TaskHistoryEntry(
    val weekKey: String,
    val endDate: LocalDate? = null,
    val totalPoints: Int,
    val taskCompletions: List<TaskCompletionSummary>
)

/**
 * Summary of completions for a single task
 */
data class TaskCompletionSummary(
    val taskId: String,
    val category: String,
    val title: String,
    val completions: Int,
    val maxCompletions: Int,
    val points: Int
)