package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.WorkerParameters
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate

/**
 * Worker that checks if today is Friday and sends a notification if there are incomplete exercises
 */
class TrainingsplanReminderWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        const val WORK_NAME = "trainingsplan_reminder_work"
        private const val TAG = "TrainingsplanReminder"
        private const val NOTIFICATION_ID = 1337
    }

    override suspend fun doWork(): Result {
        Log.d(TAG, "TrainingsplanReminderWorker running check")
        
        try {
            // Run in IO dispatcher to avoid blocking the main thread
            return withContext(Dispatchers.IO) {
                val today = LocalDate.now()
                val dayOfWeek = today.dayOfWeek
                
                Log.d(TAG, "Current day is: $dayOfWeek (${dayOfWeek.value}), checking if it's Friday (${DayOfWeek.FRIDAY.value})")
                
                // Check if today is Friday (or force notification for testing)
                val isFriday = dayOfWeek == DayOfWeek.FRIDAY
                val isTestRun = inputData.getBoolean("test_run", false)
                
                if (isFriday || isTestRun) {
                    if (isTestRun) {
                        Log.d(TAG, "This is a test run, forcing notification check")
                    } else {
                        Log.d(TAG, "Today is Friday, checking for incomplete exercises")
                    }
                    
                    val notificationManager = TrainingsplanNotificationManager(applicationContext)
                    val notificationSent = notificationManager.checkAndNotifyIncompleteExercises()
                    
                    if (notificationSent) {
                        Log.d(TAG, "Notification sent successfully")
                    } else {
                        Log.d(TAG, "No notification sent - no incomplete exercises or empty plan")
                    }
                } else {
                    Log.d(TAG, "Today is not Friday, skipping notification check")
                }
                
                // Always return success, even if no notification was sent
                Result.success()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in TrainingsplanReminderWorker", e)
            return Result.failure()
        }
    }
    
    /**
     * Create foreground info for the worker to run in the background
     * This is required for Android 12+ to show notifications when the app is not in foreground
     */
    override suspend fun getForegroundInfo(): ForegroundInfo {
        val notificationManager = TrainingsplanNotificationManager(applicationContext)
        return ForegroundInfo(
            NOTIFICATION_ID,
            notificationManager.createProgressNotification("Checking training plan...")
        )
    }
}
