package com.atom.diesnookerapp.data.firebase

import com.atom.diesnookerapp.ui.trainingsplan.Trainingsplan
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanItem
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

/**
 * Firebase model for training plans
 * Note: weekStartDate property name is kept for backward compatibility, but it now represents the month start date
 */
data class FirebaseTrainingsplan(
    var weekStartDate: String = "", // ISO format date (represents month start date)
    var items: List<FirebaseTrainingsplanItem> = emptyList()
) : FirebaseModel() {

    companion object {
        private val formatter = DateTimeFormatter.ISO_LOCAL_DATE

        fun fromTrainingsplan(trainingsplan: Trainingsplan, userId: String): FirebaseTrainingsplan {
            return FirebaseTrainingsplan(
                weekStartDate = trainingsplan.weekStartDate.format(formatter),
                items = trainingsplan.items.map { FirebaseTrainingsplanItem.fromTrainingsplanItem(it) }
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }

    fun toTrainingsplan(): Trainingsplan {
        return Trainingsplan(
            weekStartDate = LocalDate.parse(weekStartDate, formatter),
            items = items.map { it.toTrainingsplanItem() }.toMutableList()
        )
    }
}

/**
 * Firebase model for training plan items
 */
data class FirebaseTrainingsplanItem(
    var id: String = "",
    var name: String = "",
    var isChecked: Boolean = false,
    var isUserDefined: Boolean = false,
    var date: String? = null, // ISO format date, nullable
    var exerciseType: String? = null,
    var exerciseId: String? = null,
    var completionCount: Int = 0,
    var targetCount: Int = 1
) {
    companion object {
        private val formatter = DateTimeFormatter.ISO_LOCAL_DATE

        fun fromTrainingsplanItem(item: TrainingsplanItem): FirebaseTrainingsplanItem {
            return FirebaseTrainingsplanItem(
                id = item.id,
                name = item.name,
                isChecked = item.isChecked,
                isUserDefined = item.isUserDefined,
                date = item.date?.format(formatter),
                exerciseType = item.exerciseType,
                exerciseId = item.exerciseId,
                completionCount = item.completionCount,
                targetCount = item.targetCount
            )
        }
    }

    fun toTrainingsplanItem(): TrainingsplanItem {
        return TrainingsplanItem(
            id = id,
            name = name,
            isChecked = isChecked,
            isUserDefined = isUserDefined,
            date = date?.let { LocalDate.parse(it, DateTimeFormatter.ISO_LOCAL_DATE) },
            exerciseType = exerciseType,
            exerciseId = exerciseId,
            completionCount = completionCount,
            targetCount = targetCount
        )
    }
}
