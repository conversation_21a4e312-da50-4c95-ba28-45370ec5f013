package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.google.android.material.floatingactionbutton.FloatingActionButton
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class AufgabenFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var taskManager: TaskManager
    private lateinit var taskCategoryAdapter: TaskCategoryAdapter // Added for class-level access

    // Removed hardcoded taskCategories

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_aufgaben, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Check if tasks need to be reset for the new week
        val wasReset = taskManager.resetTasksForNewWeek()
        if (wasReset) {
            Toast.makeText(
                requireContext(),
                "Neue Woche begonnen! Aufgaben wurden zurückgesetzt.",
                Toast.LENGTH_LONG
            ).show()
        }

        // Initialize RecyclerView
        recyclerView = view.findViewById(R.id.taskCategoriesRecyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Check if historyButton exists before trying to set a listener
        val historyButton = view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton)
        historyButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_aufgaben_to_taskHistoryFragment)
        }

        // Setup Manage Tasks Button
        val manageTasksButton = view.findViewById<com.google.android.material.floatingactionbutton.FloatingActionButton>(R.id.manageTasksButton)
        manageTasksButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_aufgaben_to_manage_tasks)
        }

        // Set up adapter
        taskCategoryAdapter = TaskCategoryAdapter(mutableListOf()) { clickedCategory -> // Renamed 'category' to 'clickedCategory' for clarity
            try {
                val bundle = Bundle().apply {
                    putString("categoryId", clickedCategory.id)
                    putString("categoryDisplayName", clickedCategory.name) // 'name' is the display name from TaskCategory
                }
                findNavController().navigate(R.id.action_aufgaben_to_category_tasks, bundle)
            } catch (e: Exception) {
                Log.e("AufgabenFragment", "Navigation error: ${e.message}", e)
                Toast.makeText(
                    requireContext(),
                    "Navigation error: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
        recyclerView.adapter = taskCategoryAdapter
        loadAndDisplayCategories() // Load categories after adapter is set
    }

    override fun onResume() {
        super.onResume()
        if (::taskManager.isInitialized && ::taskCategoryAdapter.isInitialized) { // Ensure they are init
            loadAndDisplayCategories()
        }
    }

    private fun loadAndDisplayCategories() {
        val allTasks = taskManager.getAllTasks()
        val uniqueCategoryIds = allTasks.map { it.category }.distinct().sorted()
        val dynamicCategories = uniqueCategoryIds.map { categoryId ->
            TaskCategory(TaskCategoryHelper.getDisplayName(categoryId), categoryId)
        }
        taskCategoryAdapter.updateCategories(dynamicCategories)
    }
}

// Data class to represent a task category
data class TaskCategory(
    val name: String,
    val id: String
)