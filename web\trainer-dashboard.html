<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Die Snooker App - Trainer Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="trainer-styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Date Range Picker -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <h1>Die Snooker App</h1>
            <nav>
                <ul>
                    <li><a href="#" data-section="athletes" class="active">Meine Athleten</a></li>
                    <li><a href="#" data-section="profile">Mein Profil</a></li>
                    <li><a href="#" data-section="raw-data">Rohdaten</a></li>
                </ul>
            </nav>
            <div id="user-info">
                <span id="user-email"></span>
                <span id="user-role"></span>
                <button id="logout-btn" class="btn">Abmelden</button>
            </div>
        </aside>
        
        <main>
            <div id="email-verification-alert" class="email-alert hidden" style="background-color: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeeba; margin-bottom: 20px; text-align: center;">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div id="login-container">
                <h2>Anmelden</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="email">E-Mail</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Passwort</label>
                        <input type="password" id="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Anmelden</button>
                    <div class="form-footer">
                        <p>Noch kein Konto? <a href="trainer-register.html">Als Trainer registrieren</a></p>
                    </div>
                </form>
            </div>
            
            <div id="app-container" class="hidden">
                <!-- Athletes Section -->
                <section id="athletes-section" class="active">
                    <div class="dashboard-header">
                        <div class="dashboard-title">
                            <h2>Meine Athleten</h2>
                            <p id="trainer-type-display">Trainer-Typ: <span></span></p>
                        </div>
                        <div class="dashboard-actions">
                            <button id="refresh-btn" class="btn">
                                <i class="fas fa-sync-alt"></i> Aktualisieren
                            </button>
                        </div>
                    </div>
                    
                    <div id="athletes-list" class="athlete-list">
                        <!-- Athletes will be loaded here -->
                    </div>
                    
                    <div id="empty-athletes" class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="empty-state-message">Keine Athleten gefunden</h3>
                        <p class="empty-state-description">
                            Sie haben noch keine Verbindungen zu Athleten. Athleten können Sie als Trainer hinzufügen, indem sie in der App auf "Trainer-Verbindungen" gehen.
                        </p>
                    </div>
                </section>
                
                <!-- Athlete Detail Section -->
                <section id="athlete-detail-section" class="hidden">
                    <div class="athlete-detail">
                        <div class="athlete-detail-header">
                            <div>
                                <h3 class="athlete-detail-name" id="detail-athlete-name"></h3>
                                <p class="athlete-detail-email" id="detail-athlete-email"></p>
                                <button id="view-raw-data-btn" class="btn btn-secondary" style="margin-top: 10px;">
                                    <i class="fas fa-database"></i> Rohdaten anzeigen
                                </button>
                            </div>
                            <a href="#" class="back-button" id="back-to-athletes">
                                <i class="fas fa-arrow-left"></i> Zurück zur Übersicht
                            </a>
                        </div>
                        
                        <div class="data-filters">
                            <div class="filter-group">
                                <span class="filter-label">Zeitraum:</span>
                                <input type="text" id="date-range-picker" class="date-range-input">
                            </div>
                            <div class="filter-group" id="exercise-type-filter">
                                <span class="filter-label">Übungstyp:</span>
                                <select id="exercise-type-select">
                                    <option value="all">Alle Übungen</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Charts for Regular Trainer -->
                        <div id="trainer-charts" class="charts-container">
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Übungsabschlussrate</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="completion-rate">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="completion-rate-chart" height="300"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Trainingsplan-Einhaltung</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="trainingsplan-adherence">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="trainingsplan-adherence-chart" height="300"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Leistungstrends</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="performance-trends">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="performance-trends-chart" height="300"></canvas>
                            </div>
                        </div>
                        
                        <!-- Charts for Mental Trainer -->
                        <div id="mental-trainer-charts" class="charts-container">
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Selbsteinschätzung-Trends</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="self-assessment">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="self-assessment-chart" height="300"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Fragenanalyse</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="question-analysis">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="question-analysis-chart" height="300"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4 class="chart-title">Emotionaler Zustand</h4>
                                    <div class="chart-actions">
                                        <button class="export-btn" data-chart="emotional-state">
                                            <i class="fas fa-download"></i> Exportieren
                                        </button>
                                    </div>
                                </div>
                                <canvas id="emotional-state-chart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Raw Data Section -->
                <section id="raw-data-section" class="hidden">
                    <div class="athlete-detail-header"> <!-- Reusing similar styling for header -->
                        <div>
                            <h2>Rohdaten Ansicht</h2>
                        </div>
                        <a href="#" class="back-button" id="back-to-athletes-from-raw-data"> <!-- Unique ID for this back button -->
                            <i class="fas fa-arrow-left"></i> Zurück zur Übersicht
                        </a>
                    </div>
                    <div class="raw-data-controls">
                        <label for="group-by-toggle">Group Exercise Records by:</label>
                        <select id="group-by-toggle">
                            <option value="date" selected>Date</option>
                            <option value="category">Category</option>
                        </select>
                    </div>
                    <div id="raw-data-container">
                        <!-- Raw data will be displayed here -->
                    </div>
                </section>

                <!-- Profile Section -->
                <section id="profile-section">
                    <h2>Mein Profil</h2>
                    <div class="card">
                        <div class="form-group">
                            <label for="profile-name">Name</label>
                            <input type="text" id="profile-name" disabled>
                        </div>
                        <div class="form-group">
                            <label for="profile-email">E-Mail</label>
                            <input type="email" id="profile-email" disabled>
                        </div>
                        <div class="form-group">
                            <label for="profile-role">Trainer-Typ</label>
                            <input type="text" id="profile-role" disabled>
                        </div>
                        <div class="form-group">
                            <label for="profile-experience">Erfahrung & Qualifikationen</label>
                            <textarea id="profile-experience" rows="4"></textarea>
                        </div>
                        <button id="save-profile-btn" class="btn btn-primary">Speichern</button>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <script src="trainer-dashboard.js"></script>
</body>
</html>
