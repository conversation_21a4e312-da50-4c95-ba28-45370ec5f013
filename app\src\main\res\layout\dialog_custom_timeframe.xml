<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/startDateButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Startdatum"
        app:icon="@drawable/ic_calendar"
        app:iconGravity="textStart" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/endDateButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Enddatum"
        app:icon="@drawable/ic_calendar"
        app:iconGravity="textStart" />

</LinearLayout> 