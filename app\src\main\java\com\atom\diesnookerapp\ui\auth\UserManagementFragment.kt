package com.atom.diesnookerapp.ui.auth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Button
import android.widget.CheckBox
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.ConnectionStatus
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.FirebaseUserConnection
import com.atom.diesnookerapp.data.firebase.FirebaseUserProfile
import com.atom.diesnookerapp.data.firebase.UserConnectionRepository
import com.atom.diesnookerapp.data.firebase.UserProfileRepository
import com.atom.diesnookerapp.data.firebase.UserRole
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

class UserManagementFragment : Fragment() {

    private lateinit var emailEditText: TextInputEditText
    private lateinit var trainerTypeDropdown: AutoCompleteTextView
    private lateinit var exerciseAccessCheckbox: CheckBox
    private lateinit var selfAssessmentAccessCheckbox: CheckBox
    private lateinit var addTrainerButton: Button
    private lateinit var connectionsRecyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var emptyStateTextView: TextView

    private val authManager = FirebaseAuthManager()
    private lateinit var userProfileRepository: UserProfileRepository
    private lateinit var userConnectionRepository: UserConnectionRepository
    private lateinit var connectionsAdapter: UserConnectionAdapter

    private var selectedTrainerRole: UserRole = UserRole.TRAINER

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_user_management, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        userProfileRepository = UserProfileRepository(requireContext())
        userConnectionRepository = UserConnectionRepository(requireContext())

        // Initialize views
        emailEditText = view.findViewById(R.id.emailEditText)
        trainerTypeDropdown = view.findViewById(R.id.trainerTypeDropdown)
        exerciseAccessCheckbox = view.findViewById(R.id.exerciseAccessCheckbox)
        selfAssessmentAccessCheckbox = view.findViewById(R.id.selfAssessmentAccessCheckbox)
        addTrainerButton = view.findViewById(R.id.addTrainerButton)
        connectionsRecyclerView = view.findViewById(R.id.connectionsRecyclerView)
        progressBar = view.findViewById(R.id.progressBar)
        emptyStateTextView = view.findViewById(R.id.emptyStateTextView)

        // Set up trainer type dropdown
        setupTrainerTypeDropdown()

        // Set up connections recycler view
        setupConnectionsRecyclerView()

        // Set up add trainer button
        addTrainerButton.setOnClickListener {
            addTrainer()
        }

        // Load connections
        loadConnections()
    }

    private fun setupTrainerTypeDropdown() {
        val trainerRoles = listOf(UserRole.TRAINER, UserRole.MENTAL_TRAINER)
        val roleNames = trainerRoles.map { getRoleDisplayName(it) }

        val adapter = ArrayAdapter(requireContext(), R.layout.dropdown_item, roleNames)
        trainerTypeDropdown.setAdapter(adapter)

        // Set default selection to TRAINER
        trainerTypeDropdown.setText(getRoleDisplayName(UserRole.TRAINER), false)

        trainerTypeDropdown.setOnItemClickListener { _, _, position, _ ->
            selectedTrainerRole = trainerRoles[position]
            
            // Update checkboxes based on selected role
            when (selectedTrainerRole) {
                UserRole.TRAINER -> {
                    exerciseAccessCheckbox.isChecked = true
                    selfAssessmentAccessCheckbox.isChecked = false
                }
                UserRole.MENTAL_TRAINER -> {
                    exerciseAccessCheckbox.isChecked = false
                    selfAssessmentAccessCheckbox.isChecked = true
                }
                else -> {
                    // Should not happen
                    exerciseAccessCheckbox.isChecked = false
                    selfAssessmentAccessCheckbox.isChecked = false
                }
            }
        }
    }

    private fun getRoleDisplayName(role: UserRole): String {
        return when (role) {
            UserRole.PLAYER -> "Spieler"
            UserRole.TRAINER -> "Trainer"
            UserRole.MENTAL_TRAINER -> "Mental Trainer"
        }
    }

    private fun setupConnectionsRecyclerView() {
        connectionsAdapter = UserConnectionAdapter(
            onEditClick = { connection ->
                showEditConnectionDialog(connection)
            },
            onRemoveClick = { connection ->
                showRemoveConnectionDialog(connection)
            }
        )

        connectionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = connectionsAdapter
        }
    }

    private fun loadConnections() {
        setLoading(true)

        lifecycleScope.launch {
            val result = userConnectionRepository.getConnections()

            if (result.isSuccess) {
                val connections = result.getOrNull() ?: emptyList()
                
                // Filter connections where current user is the initiator (player)
                val initiatedConnections = connections.filter { it.initiatorId == authManager.getCurrentUserId() }
                
                connectionsAdapter.submitList(initiatedConnections)
                
                // Show empty state if no connections
                emptyStateTextView.visibility = if (initiatedConnections.isEmpty()) View.VISIBLE else View.GONE
            } else {
                Toast.makeText(
                    requireContext(),
                    "Fehler beim Laden der Verbindungen: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
            }

            setLoading(false)
        }
    }

    private fun addTrainer() {
        val email = emailEditText.text.toString().trim()
        
        if (email.isEmpty()) {
            Toast.makeText(
                requireContext(),
                "Bitte gib eine E-Mail-Adresse ein",
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        
        val trainerAccess = exerciseAccessCheckbox.isChecked
        val mentalTrainerAccess = selfAssessmentAccessCheckbox.isChecked
        
        if (!trainerAccess && !mentalTrainerAccess) {
            Toast.makeText(
                requireContext(),
                "Bitte wähle mindestens eine Berechtigung aus",
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        
        setLoading(true)
        
        lifecycleScope.launch {
            val result = userConnectionRepository.createConnection(
                targetEmail = email,
                targetRole = selectedTrainerRole,
                trainerAccess = trainerAccess,
                mentalTrainerAccess = mentalTrainerAccess
            )
            
            if (result.isSuccess) {
                Toast.makeText(
                    requireContext(),
                    "Trainer erfolgreich hinzugefügt",
                    Toast.LENGTH_SHORT
                ).show()
                
                // Clear input fields
                emailEditText.text?.clear()
                
                // Reload connections
                loadConnections()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Fehler beim Hinzufügen des Trainers: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
                setLoading(false)
            }
        }
    }

    private fun showEditConnectionDialog(connection: FirebaseUserConnection) {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_edit_connection, null)
        
        val exerciseAccessCheckbox = dialogView.findViewById<CheckBox>(R.id.exerciseAccessCheckbox)
        val selfAssessmentAccessCheckbox = dialogView.findViewById<CheckBox>(R.id.selfAssessmentAccessCheckbox)
        
        // Set current values
        exerciseAccessCheckbox.isChecked = connection.trainerAccess
        selfAssessmentAccessCheckbox.isChecked = connection.mentalTrainerAccess
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("Berechtigungen bearbeiten")
            .setView(dialogView)
            .setPositiveButton("Speichern") { _, _ ->
                val trainerAccess = exerciseAccessCheckbox.isChecked
                val mentalTrainerAccess = selfAssessmentAccessCheckbox.isChecked
                
                if (!trainerAccess && !mentalTrainerAccess) {
                    Toast.makeText(
                        requireContext(),
                        "Bitte wähle mindestens eine Berechtigung aus",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setPositiveButton
                }
                
                updateConnectionPermissions(
                    connection.id,
                    trainerAccess,
                    mentalTrainerAccess
                )
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun updateConnectionPermissions(
        connectionId: String,
        trainerAccess: Boolean,
        mentalTrainerAccess: Boolean
    ) {
        setLoading(true)
        
        lifecycleScope.launch {
            val result = userConnectionRepository.updateConnectionPermissions(
                connectionId = connectionId,
                trainerAccess = trainerAccess,
                mentalTrainerAccess = mentalTrainerAccess
            )
            
            if (result.isSuccess) {
                Toast.makeText(
                    requireContext(),
                    "Berechtigungen erfolgreich aktualisiert",
                    Toast.LENGTH_SHORT
                ).show()
                
                // Reload connections
                loadConnections()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Fehler beim Aktualisieren der Berechtigungen: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
                setLoading(false)
            }
        }
    }

    private fun showRemoveConnectionDialog(connection: FirebaseUserConnection) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("Verbindung entfernen")
            .setMessage("Möchtest du die Verbindung zu ${connection.targetName} wirklich entfernen?")
            .setPositiveButton("Entfernen") { _, _ ->
                removeConnection(connection.id)
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun removeConnection(connectionId: String) {
        setLoading(true)
        
        lifecycleScope.launch {
            val result = userConnectionRepository.deleteConnection(connectionId)
            
            if (result.isSuccess) {
                Toast.makeText(
                    requireContext(),
                    "Verbindung erfolgreich entfernt",
                    Toast.LENGTH_SHORT
                ).show()
                
                // Reload connections
                loadConnections()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Fehler beim Entfernen der Verbindung: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
                setLoading(false)
            }
        }
    }

    private fun setLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        addTrainerButton.isEnabled = !isLoading
    }
}
