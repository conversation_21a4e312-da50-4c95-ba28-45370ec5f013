<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Die Snooker App - Trainer Registration</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="trainer-styles.css">
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
</head>
<body class="trainer-registration-page">
    <div class="container">
        <main>
            <div id="trainer-register-container">
                <div class="registration-header">
                    <h1>Die Snooker App</h1>
                    <h2>Trainer-Registrierung</h2>
                    <p><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> ein Trainer-<PERSON>, um Athleten zu unterstützen und ihre Leistungsdaten zu analysieren.</p>
                </div>
                
                <form id="trainer-register-form">
                    <div class="form-group">
                        <label for="fullName">Vollständiger Name</label>
                        <input type="text" id="fullName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">E-Mail</label>
                        <input type="email" id="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Passwort</label>
                        <input type="password" id="password" required>
                        <small>Mindestens 8 Zeichen, mit Zahlen und Sonderzeichen</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">Passwort bestätigen</label>
                        <input type="password" id="confirmPassword" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="trainerType">Trainer-Typ</label>
                        <select id="trainerType" required>
                            <option value="TRAINER">Trainer (Übungen & Trainingsplan)</option>
                            <option value="MENTAL_TRAINER">Mental Trainer (Selbsteinschätzung)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="experience">Erfahrung & Qualifikationen (optional)</label>
                        <textarea id="experience" rows="4"></textarea>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="termsAgreement" required>
                            Ich stimme den <a href="#" class="terms-link">Nutzungsbedingungen</a> zu
                        </label>
                    </div>
                    
                    <div id="register-error" class="error-message"></div>
                    
                    <button type="submit" class="btn btn-primary">Registrieren</button>
                    
                    <div class="form-footer">
                        <p>Bereits registriert? <a href="index.html">Hier anmelden</a></p>
                    </div>
                </form>
                
                <div id="verification-message" class="hidden">
                    <h3>Bestätigen Sie Ihre E-Mail-Adresse</h3>
                    <p>Wir haben eine Bestätigungs-E-Mail an Ihre Adresse gesendet. Bitte klicken Sie auf den Link in der E-Mail, um Ihre Registrierung abzuschließen.</p>
                    <p>Nachdem Sie Ihre E-Mail bestätigt haben, können Sie sich <a href="trainer-dashboard.html">hier anmelden</a>.</p>
                </div>
            </div>
        </main>
    </div>
    
    <script src="trainer-register.js"></script>
</body>
</html>
