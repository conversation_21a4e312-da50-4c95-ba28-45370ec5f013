package com.atom.diesnookerapp.ui.trainingsplan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import android.util.Log
import android.widget.Toast

class ExerciseSelectionFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var exerciseAdapter: ExerciseAdapter
    private lateinit var recyclerView: RecyclerView
    private var selectedCategory: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            selectedCategory = it.getString("category")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exercise_selection, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingsplanManager = TrainingsplanManager(requireContext())
        val selectedExercises = mutableListOf<ExerciseItem>()
        recyclerView = view.findViewById(R.id.exerciseRecyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        exerciseAdapter = ExerciseAdapter(emptyList(), ::onExerciseClick, selectedExercises) // Pass selectedExercises to the adapter

        recyclerView.adapter = exerciseAdapter
        updateUI()
        showAddExercisesDialog()
    }

    private fun updateUI() {
        selectedCategory?.let { category ->
            viewLifecycleOwner.lifecycleScope.launch {
                try {
                    val exercises = trainingsplanManager.getExercisesForCategory(category)
                    exerciseAdapter.updateData(exercises)
                    if (exercises.isEmpty()) {
                        Toast.makeText(context, "No exercises found for $category.", Toast.LENGTH_SHORT).show()
                    }
                } catch (e: Exception) {
                    Log.e("ExerciseSelection", "Error loading exercises for category: $category", e)
                    Toast.makeText(context, "Failed to load exercises.", Toast.LENGTH_LONG).show()
                    exerciseAdapter.updateData(emptyList()) // Clear adapter on error
                }
            }
        } ?: run {
            Log.d("ExerciseSelection", "No category selected, clearing adapter.")
            exerciseAdapter.updateData(emptyList())
        }
    }

    private fun onExerciseClick(exercise: ExerciseItem){
         if(!exerciseAdapter.selectedExercisesList.contains(exercise)){
            exerciseAdapter.selectedExercisesList.add(exercise)
        }else{
            exerciseAdapter.selectedExercisesList.remove(exercise)
        }
    }

    private fun showAddExercisesDialog() {
        val selectedExercises = exerciseAdapter.selectedExercisesList
        if(selectedExercises.isNotEmpty()) {
            // Use ThemeHelper to create a themed AlertDialog
            ThemeHelper.createThemedAlertDialogBuilder(requireContext())
                .setTitle("Übungen hinzufügen?")
                .setMessage("Möchtest du die ausgewählten Übungen zum Trainingsplan hinzufügen?")
                .setPositiveButton("Ja") { _, _ ->
                    trainingsplanManager.addExercisesToPlan(selectedExercises)
                    findNavController().popBackStack()
                }
                .setNegativeButton("Nein") {_,_ ->
                    selectedExercises.clear()
                    findNavController().popBackStack()
                }
                .show()
        } else {
            findNavController().popBackStack()
        }
    }
}
