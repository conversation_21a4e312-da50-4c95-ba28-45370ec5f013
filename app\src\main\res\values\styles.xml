<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base dropdown dialog style -->
    <style name="DropdownDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowBackground">@drawable/dropdown_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@style/DropdownAnimation</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Snooker theme dropdown dialog style -->
    <style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style>

    <!-- Button styles for Snooker theme -->
    <style name="SnookerPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_red_500</item>
    </style>

    <style name="SnookerNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_green_500</item>
    </style>

    <!-- Blue theme dropdown dialog style -->
    <style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style>

    <!-- Button styles for Blue theme -->
    <style name="BluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/blue_500</item>
    </style>

    <style name="BlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/orange_500</item>
    </style>

    <!-- Dark Blue theme dropdown dialog style -->
    <style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style>

    <!-- Button styles for Dark Blue theme -->
    <style name="DarkBluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/dark_blue_500</item>
    </style>

    <style name="DarkBlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyan_500</item>
    </style>

    <!-- Dark theme dropdown dialog style -->
    <style name="DropdownDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>

    <!-- Button styles for Dark theme -->
    <style name="DarkPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/purple_200</item>
    </style>

    <style name="DarkNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/teal_200</item>
    </style>

    <style name="DropdownAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>
</resources>