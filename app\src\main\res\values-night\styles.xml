<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Snooker theme dropdown dialog style for night mode -->
    <style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style>

    <!-- Button styles for Snooker theme in night mode -->
    <style name="SnookerPositiveButtonStyle.Night">
        <item name="android:textColor">@color/snooker_red_200</item>
    </style>

    <style name="SnookerNegativeButtonStyle.Night">
        <item name="android:textColor">@color/snooker_green_200</item>
    </style>

    <!-- Blue theme dropdown dialog style for night mode -->
    <style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style>

    <!-- Button styles for Blue theme in night mode -->
    <style name="BluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/blue_200</item>
    </style>

    <style name="BlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/orange_200</item>
    </style>

    <!-- Dark Blue theme dropdown dialog style for night mode -->
    <style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style>

    <!-- Button styles for Dark Blue theme in night mode -->
    <style name="DarkBluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/dark_blue_200</item>
    </style>

    <style name="DarkBlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/cyan_200</item>
    </style>
</resources>
