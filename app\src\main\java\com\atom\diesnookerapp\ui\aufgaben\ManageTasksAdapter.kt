package com.atom.diesnookerapp.ui.aufgaben

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.databinding.ItemManageTaskBinding
import com.atom.diesnookerapp.databinding.ItemManageTaskCategoryHeaderBinding

class ManageTasksAdapter(
    private val onHeaderClick: (ManageTaskListItem.HeaderItem) -> Unit,
    private val onEditClick: (Task) -> Unit,
    private val onDeleteClick: (Task) -> Unit
) : ListAdapter<ManageTaskListItem, RecyclerView.ViewHolder>(DiffCallback) {

    companion object {
        private const val VIEW_TYPE_HEADER = 0
        private const val VIEW_TYPE_TASK = 1

        private val DiffCallback = object : DiffUtil.ItemCallback<ManageTaskListItem>() {
            override fun areItemsTheSame(oldItem: ManageTaskListItem, newItem: ManageTaskListItem): Boolean {
                return when {
                    oldItem is ManageTaskListItem.HeaderItem && newItem is ManageTaskListItem.HeaderItem ->
                        oldItem.categoryId == newItem.categoryId
                    oldItem is ManageTaskListItem.TaskDataItem && newItem is ManageTaskListItem.TaskDataItem ->
                        oldItem.task.id == newItem.task.id
                    else -> false
                }
            }

            override fun areContentsTheSame(oldItem: ManageTaskListItem, newItem: ManageTaskListItem): Boolean {
                return oldItem == newItem // Relies on data class equals()
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is ManageTaskListItem.HeaderItem -> VIEW_TYPE_HEADER
            is ManageTaskListItem.TaskDataItem -> VIEW_TYPE_TASK
            // null -> throw IllegalStateException("Item at position $position is null") // ListAdapter shouldn't have nulls
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_HEADER -> {
                val binding = ItemManageTaskCategoryHeaderBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                CategoryHeaderViewHolder(binding)
            }
            VIEW_TYPE_TASK -> {
                val binding = ItemManageTaskBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                TaskViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)
        when (holder) {
            is CategoryHeaderViewHolder -> holder.bind(item as ManageTaskListItem.HeaderItem, onHeaderClick)
            is TaskViewHolder -> holder.bind(item as ManageTaskListItem.TaskDataItem, onEditClick, onDeleteClick)
        }
    }

    inner class CategoryHeaderViewHolder(private val binding: ItemManageTaskCategoryHeaderBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(headerItem: ManageTaskListItem.HeaderItem, onHeaderClick: (ManageTaskListItem.HeaderItem) -> Unit) {
            binding.categoryHeaderNameTextView.text = headerItem.categoryDisplayName
            binding.categoryHeaderExpandIcon.setImageResource(
                if (headerItem.isExpanded) R.drawable.ic_expand_less else R.drawable.ic_expand_more
            )
            itemView.setOnClickListener {
                onHeaderClick(headerItem)
            }
        }
    }

    inner class TaskViewHolder(private val binding: ItemManageTaskBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(
            taskDataItem: ManageTaskListItem.TaskDataItem,
            onEditClick: (Task) -> Unit,
            onDeleteClick: (Task) -> Unit
        ) {
            val task = taskDataItem.task // Extract the actual Task object
            binding.taskTitleTextView.text = task.title
            binding.taskCategoryTextView.text = TaskCategoryHelper.getDisplayName(task.category)
            binding.taskFrequencyTextView.text = when (task.frequency) {
                TaskFrequency.DAILY -> "Täglich"
                TaskFrequency.WEEKLY -> {
                    val count = task.weeklyFrequencyCount ?: 1
                    "Wöchentlich (${count}x)"
                }
            }
            binding.taskPointsTextView.text = "${task.points} Punkte" // Ensure points is String

            binding.editTaskButton.setOnClickListener { onEditClick(task) }
            binding.deleteTaskButton.visibility = View.VISIBLE
            binding.deleteTaskButton.setOnClickListener { onDeleteClick(task) }
        }
    }
}
