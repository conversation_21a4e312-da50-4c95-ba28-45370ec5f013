"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import { collection, query, where, orderBy, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface TaskCompletion {
  title: string;
  category: string;
  completions: number;
  maxCompletions: number;
  points: number;
}

interface TaskHistoryEntry {
  id: string;
  userId: string;
  weekKey: string; // e.g., "2023-W34"
  totalPoints: number;
  taskCompletions: TaskCompletion[];
  lastUpdated: Timestamp;
}

function TasksPage() {
  const { currentUser } = useAuth();
  const [taskHistory, setTaskHistory] = useState<TaskHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const q = query(
          collection(db, "task_history"),
          where("userId", "==", currentUser.uid),
          orderBy("lastUpdated", "desc") // Or orderBy('weekKey', 'desc') if that makes more sense
        );
        const querySnapshot = await getDocs(q);
        const fetchedTaskHistory = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as TaskHistoryEntry));
        setTaskHistory(fetchedTaskHistory);
      } catch (err: any) {
        console.error("Error fetching task history:", err);
        setError("Failed to load task history. " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentUser]);

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  const formatDateTimestamp = (timestamp: any): string => {
    if (timestamp && typeof timestamp.toDate === 'function') { // Firestore Timestamp
      return new Date(timestamp.toDate()).toLocaleDateString('de-DE');
    }
    if (typeof timestamp === 'number') { // Milliseconds epoch
      return new Date(timestamp).toLocaleDateString('de-DE');
    }
    if (timestamp instanceof Date) { // JavaScript Date object
      return timestamp.toLocaleDateString('de-DE');
    }
    return 'N/A'; // Fallback for unknown or invalid types
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Aufgaben History</h1>
      {taskHistory.length === 0 && !loading && (
        <p className="text-gray-600 bg-white p-6 rounded-lg shadow-md">No task history found.</p>
      )}
      <div className="space-y-6">
        {taskHistory.map(entry => (
          <details key={entry.id} className="bg-white p-4 rounded-lg shadow-md">
            <summary className="font-medium text-lg cursor-pointer text-gray-800 hover:text-indigo-600">
              Woche: {entry.weekKey} - <span className="font-semibold">{entry.totalPoints} Punkte</span>
              <span className="text-sm text-gray-500 ml-2">
                (Updated: {formatDateTimestamp(entry.lastUpdated)})
              </span>
            </summary>
            <div className="mt-4">
              <h4 className="font-semibold text-md text-gray-700 mb-2">Completed Tasks:</h4>
              {entry.taskCompletions.length > 0 ? (
                <ul className="list-none pl-0 space-y-2">
                  {entry.taskCompletions.map((task, index) => (
                    <li key={index} className="p-3 border rounded-md bg-gray-50 text-sm">
                      <div className="font-medium text-gray-700">{task.title} ({task.category})</div>
                      <div className="text-gray-600">
                        Completions: {task.completions}/{task.maxCompletions}
                      </div>
                      <div className="text-gray-600">Points: {task.points}</div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">No tasks completed this week.</p>
              )}
            </div>
          </details>
        ))}
      </div>
    </div>
  );
}

export default withAuth(TasksPage, { redirectTo: '/login' });
