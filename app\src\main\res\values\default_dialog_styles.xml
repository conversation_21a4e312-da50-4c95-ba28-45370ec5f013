<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base dialog style with default animations -->
    <style name="DefaultAnimationDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <!-- Use default Android dialog animation by not specifying windowAnimationStyle -->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Snooker theme dialog style with default animations -->
    <style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style>

    <!-- Blue theme dialog style with default animations -->
    <style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style>

    <!-- Dark Blue theme dialog style with default animations -->
    <style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style>

    <!-- Dark theme dialog style with default animations -->
    <style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>
</resources>
