package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.threeten.bp.LocalDate

class TrainingPreferences(context: Context) {
    private val prefs = context.getSharedPreferences("training_prefs", Context.MODE_PRIVATE)
    private val gson = Gson()

    fun saveDaily(record: DailyTrainingRecord) {
        val records = getRecords().toMutableList()
        
        // Find existing record for the same date and type
        val existingIndex = records.indexOfFirst { 
            it.date == record.date && it.type == record.type 
        }
        
        if (existingIndex >= 0) {
            // Update existing record
            records[existingIndex] = record
        } else {
            // Add new record
            records.add(record)
        }
        
        prefs.edit().putString("daily_records", gson.toJson(records)).apply()
    }

    fun getRecords(): List<DailyTrainingRecord> {
        val json = prefs.getString("daily_records", "[]")
        val type = object : TypeToken<List<DailyTrainingRecord>>() {}.type
        return gson.fromJson(json, type) ?: emptyList()
    }

    fun clearAll() {
        prefs.edit().remove("daily_records").apply()
    }
} 