package com.atom.diesnookerapp.ui.manageexercises

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import android.content.Context
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.DefaultExercises
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinitionRepository
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager // Assuming this class exists
import com.atom.diesnookerapp.databinding.DialogAddEditExerciseBinding
import com.atom.diesnookerapp.ui.manageexercises.ManageExerciseListItem // Added import
import com.atom.diesnookerapp.databinding.FragmentManageExercisesBinding
import kotlinx.coroutines.launch
import android.util.Log
import android.widget.ArrayAdapter
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import com.atom.diesnookerapp.ui.settings.ThemeHelper // Added
import com.atom.diesnookerapp.ui.settings.ThemePreferences // Added
import android.content.res.ColorStateList // Added
import com.google.android.material.textfield.TextInputLayout // Added
import com.atom.diesnookerapp.ui.utils.ThemedArrayAdapter



class ManageExercisesFragment : Fragment() {

    private var _binding: FragmentManageExercisesBinding? = null
    private val binding get() = _binding!!

    private companion object {
        private const val PREF_KEY_MIGRATED_DEFAULT_EXERCISES = "migrated_default_exercises_" // Renamed for clarity
        private const val USER_PREFS_NAME = "user_prefs"
        private const val TAG = "ManageExercisesFragment"
    }

    private lateinit var exerciseRepository: UserExerciseDefinitionRepository
    private lateinit var authManager: FirebaseAuthManager
    private lateinit var manageExercisesAdapter: ManageExercisesAdapter
    private lateinit var trainingsplanManager: TrainingsplanManager

    // Class properties for managing category expansion and caching exercises
    private val categoryExpansionState = mutableMapOf<String, Boolean>()
    private var currentExercises: List<UserExerciseDefinition> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentManageExercisesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        exerciseRepository = UserExerciseDefinitionRepository()
        authManager = FirebaseAuthManager() // Ensure this is correctly initialized
        trainingsplanManager = TrainingsplanManager(requireContext())

        setupRecyclerView()

        binding.addExerciseFab.setOnClickListener {
            showAddEditExerciseDialog(null)
        }

        loadUserExercises()
    }

    private fun setupRecyclerView() {
        manageExercisesAdapter = ManageExercisesAdapter(
            onHeaderClick = { headerItem ->
                // Toggle expansion state
                val currentExpansion = categoryExpansionState[headerItem.categoryId] ?: false
                categoryExpansionState[headerItem.categoryId] = !currentExpansion
                // Rebuild and submit the list using the cached exercises
                rebuildAndSubmitList()
            },
            onEditClick = { exercise -> handleEditExercise(exercise) },
            onDeleteClick = { exercise -> handleDeleteExercise(exercise) }
        )
        binding.exercisesRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = manageExercisesAdapter
        }
    }

    private fun loadUserExercises() {
        if (!authManager.isLoggedIn()) {
            Toast.makeText(context, getString(R.string.please_log_in_to_manage_exercises_toast), Toast.LENGTH_SHORT).show()
            binding.emptyView.visibility = View.VISIBLE
            binding.emptyView.text = getString(R.string.please_log_in_to_manage_exercises_toast)
            currentExercises = emptyList() // Clear cache
            manageExercisesAdapter.submitList(emptyList()) // Clear existing list
            return
        }

        val userId = authManager.getCurrentUserId()
        if (userId == null) {
            Toast.makeText(context, getString(R.string.error_user_id_not_found), Toast.LENGTH_SHORT).show() // Assume R.string.error_user_id_not_found exists or use generic
            binding.emptyView.visibility = View.VISIBLE
            binding.emptyView.text = getString(R.string.error_user_id_not_found_info) // Assume R.string.error_user_id_not_found_info exists
            currentExercises = emptyList() // Clear cache
            manageExercisesAdapter.submitList(emptyList())
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            val result = exerciseRepository.getExercisesForUser(userId)
            result.fold(
                onSuccess = { userExercises ->
                    currentExercises = userExercises // Cache the fetched exercises

                    val prefs = requireContext().getSharedPreferences(USER_PREFS_NAME, Context.MODE_PRIVATE)
                    val alreadyMigrated = prefs.getBoolean(PREF_KEY_MIGRATED_DEFAULT_EXERCISES + userId, false)

                    if (!alreadyMigrated) {
                        launch { // Nested launch for migration
                            Log.d(TAG, "User $userId not migrated yet. Starting migration of default exercises.")
                            seedDefaultExercisesForUser(userId) // This now acts as migrateDefaultExercisesForUser
                            prefs.edit().putBoolean(PREF_KEY_MIGRATED_DEFAULT_EXERCISES + userId, true).apply()
                            Log.d(TAG, "Migration complete for user $userId. Reloading exercises.")
                            loadUserExercises() // Reload to display newly migrated exercises and existing custom ones
                        }
                    } else {
                        Log.d(TAG, "User $userId already migrated. Displaying exercises.")
                        rebuildAndSubmitList() // Build and submit the structured list
                    }
                },
                onFailure = { exception ->
                    Toast.makeText(context, getString(R.string.error_loading_exercises_toast) + ": ${exception.message}", Toast.LENGTH_LONG).show()
                    binding.emptyView.visibility = View.VISIBLE
                    binding.emptyView.text = getString(R.string.error_loading_exercises_toast)
                    currentExercises = emptyList() // Clear cache
                    manageExercisesAdapter.submitList(emptyList())
                }
            )
        }
    }

    // New method to rebuild the list from cached data
    private fun rebuildAndSubmitList() {
        val listItems = buildExerciseListItems(currentExercises)
        manageExercisesAdapter.submitList(listItems)

        if (!authManager.isLoggedIn()) {
            binding.emptyView.text = getString(R.string.please_log_in_to_manage_exercises_toast)
            binding.emptyView.visibility = View.VISIBLE
        } else if (currentExercises.isEmpty()) {
            binding.emptyView.text = getString(R.string.no_exercises_found_add_some_toast) // Corrected string resource
            binding.emptyView.visibility = View.VISIBLE
        } else {
            binding.emptyView.visibility = View.GONE
        }
    }

    // New private function to build the list items
    private fun buildExerciseListItems(exercises: List<UserExerciseDefinition>): List<ManageExerciseListItem> {
        if (exercises.isEmpty()) {
            return emptyList()
        }

        val listItems = mutableListOf<ManageExerciseListItem>()
        val groupedExercises = exercises.groupBy { it.category }

        // Sort categories by name for consistent order.
        // Using categoryId directly as displayName for now.
        val sortedCategoryIds = groupedExercises.keys.sorted()

        for (categoryId in sortedCategoryIds) {
            val categoryDisplayName = categoryId // Or use a helper if display names are different
            val isExpanded = categoryExpansionState.getOrPut(categoryId) { false } // Default to collapsed

            listItems.add(ManageExerciseListItem.HeaderItem(categoryId, categoryDisplayName, isExpanded))

            if (isExpanded) {
                groupedExercises[categoryId]?.sortedBy { it.name }?.forEach { exercise ->
                    listItems.add(ManageExerciseListItem.ExerciseDataItem(exercise))
                }
            }
        }
        return listItems
    }

    private suspend fun seedDefaultExercisesForUser(userId: String) {
        val defaultExercises = DefaultExercises.getDefaults()
        var successCount = 0
        var errorCount = 0

        for (template in defaultExercises) {
            val userExercise = template.copy(userId = userId) // Assign current user's ID
            try {
                // Assuming saveExercise can handle ID generation if template.id is not unique or suitable
                // Or ensure DefaultExercises provides unique, final IDs if repository doesn't auto-generate for non-blank IDs.
                // For this implementation, we assume template IDs are fine as is, or saveExercise handles it.
                val saveResult = exerciseRepository.saveExercise(userExercise)
                saveResult.fold(
                    onSuccess = { successCount++ },
                    onFailure = { 
                        Log.e(TAG, "Failed to save default exercise ${template.name}: ${it.message}")
                        errorCount++ 
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Exception while saving default exercise ${template.name}", e)
                errorCount++
            }
        }

        if (isAdded && context != null) { // Ensure fragment is still added and context is available
            if (errorCount == 0 && successCount > 0) {
                Toast.makeText(context, getString(R.string.default_exercises_migrated_toast), Toast.LENGTH_LONG).show()
            } else if (successCount > 0 && errorCount > 0) {
                Toast.makeText(context, getString(R.string.default_exercises_partial_migration_toast), Toast.LENGTH_LONG).show()
            } else if (errorCount > 0 && successCount == 0) {
                Toast.makeText(context, getString(R.string.default_exercises_migration_failed_toast), Toast.LENGTH_LONG).show()
            }
        }
    }


    private fun showAddEditExerciseDialog(exercise: UserExerciseDefinition?) {
        val dialogBinding = DialogAddEditExerciseBinding.inflate(LayoutInflater.from(requireContext()))
        // Use ThemeHelper for dialog builder
        val dialogBuilder = ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .setTitle(if (exercise == null) getString(R.string.dialog_title_add_exercise) else getString(R.string.dialog_title_edit_exercise))

        val dialog = dialogBuilder.create()

        val createNewCategoryTrigger = getString(R.string.action_create_new_category) // Use string resource

        // Apply theme to the TextInputLayouts manually (like in TrainingsplanFragment)
        val themePreferences = ThemePreferences(requireContext())
        val currentTheme = themePreferences.getThemeMode()

        // Apply theme colors to all input layouts
        val inputLayouts = listOf(
            dialogBinding.exerciseNameInputLayout,
            dialogBinding.newCategoryInputLayout,
            dialogBinding.exerciseDescriptionInputLayout
        )

        // Also include the exercise type input layout
        val dropdownLayouts = listOf(
            dialogBinding.exerciseCategoryInputLayout,
            dialogBinding.exerciseTypeInputLayout
        )

        when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.snooker_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.snooker_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
                }
            }
            ThemePreferences.THEME_BLUE -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.blue_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.blue_500, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.blue_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.blue_500, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
                }
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.dark_blue_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.dark_blue_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
                }
            }
            ThemePreferences.THEME_OCEAN -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.ocean_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.ocean_medium, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.ocean_medium, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.ocean_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.ocean_medium, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.ocean_medium, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.ocean_medium, null)))
                }
            }
            ThemePreferences.THEME_CRIMSON -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.crimson_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.crimson_medium, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.crimson_medium, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.crimson_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.crimson_medium, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.crimson_medium, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.crimson_medium, null)))
                }
            }
            ThemePreferences.THEME_NEON -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.neon_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.neon_purple_3, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.neon_purple_3, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.neon_card_background_light, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.neon_purple_3, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.neon_purple_3, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.neon_purple_3, null)))
                }
            }
            ThemePreferences.THEME_DARK -> {
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.dark_gray, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_200, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.dark_gray, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_200, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
                }
            }
            else -> {
                // Default theme
                inputLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.white, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_500, null))
                }
                // Special handling for dropdowns
                dropdownLayouts.forEach { layout ->
                    layout.boxBackgroundColor = resources.getColor(R.color.white, null)
                    layout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
                    layout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_500, null))
                    layout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
                }
            }
        }

        // Populate Exercise Type Dropdown
        val exerciseTypes = listOf("normal", "timeonly", "splits", "stellungsspiel")
        val exerciseTypeDisplayNames = listOf(
            getString(R.string.exercise_type_normal),
            getString(R.string.exercise_type_timeonly),
            getString(R.string.exercise_type_splits),
            getString(R.string.exercise_type_stellungsspiel)
        )

        val exerciseTypeAdapter = ThemedArrayAdapter(requireContext(), R.layout.dropdown_item, exerciseTypeDisplayNames)
        dialogBinding.exerciseTypeAutoCompleteTextView.setAdapter(exerciseTypeAdapter)

        // Set the dropdown popup background to match the theme
        val dropdownBackground = ThemeHelper.getDropdownBackgroundForCurrentTheme(requireContext())
        dialogBinding.exerciseTypeAutoCompleteTextView.setDropDownBackgroundResource(dropdownBackground)
        dialogBinding.exerciseTypeAutoCompleteTextView.dropDownVerticalOffset = 0

        // Set default exercise type or current exercise type
        if (exercise != null) {
            val exerciseTypeIndex = exerciseTypes.indexOf(exercise.exerciseType)
            if (exerciseTypeIndex >= 0) {
                dialogBinding.exerciseTypeAutoCompleteTextView.setText(exerciseTypeDisplayNames[exerciseTypeIndex], false)
            }
        } else {
            // Default to "normal"
            dialogBinding.exerciseTypeAutoCompleteTextView.setText(exerciseTypeDisplayNames[0], false)
        }

        // Populate Category Dropdown
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val categories = trainingsplanManager.getExerciseCategories() // Suspend call
                val adapterItems = mutableListOf<String>()
                adapterItems.addAll(categories.distinct().sorted()) // Get distinct sorted categories
                adapterItems.add(createNewCategoryTrigger)

                // Use ThemedArrayAdapter for consistent styling like in TrainingsplanFragment
                val themedAdapter = ThemedArrayAdapter(requireContext(), R.layout.dropdown_item, adapterItems)
                dialogBinding.exerciseCategoryAutoCompleteTextView.setAdapter(themedAdapter)

                // Set the dropdown popup background to match the theme
                val dropdownBackground = ThemeHelper.getDropdownBackgroundForCurrentTheme(requireContext())
                dialogBinding.exerciseCategoryAutoCompleteTextView.setDropDownBackgroundResource(dropdownBackground)

                // Set the popup elevation to 0 to remove any shadows that might cause white lines
                dialogBinding.exerciseCategoryAutoCompleteTextView.dropDownVerticalOffset = 0

                if (exercise != null && exercise.category.isNotBlank()) {
                    dialogBinding.exerciseCategoryAutoCompleteTextView.setText(exercise.category, false)
                    dialogBinding.newCategoryInputLayout.visibility = View.GONE
                } else {
                    dialogBinding.newCategoryInputLayout.visibility = View.GONE
                }

            } catch (e: Exception) {
                Log.e(TAG, "Failed to load categories for dropdown: ${e.message}", e)
                Toast.makeText(context, getString(R.string.error_loading_categories_toast), Toast.LENGTH_SHORT).show() // Assume R.string.error_loading_categories_toast
                dialogBinding.newCategoryInputLayout.visibility = View.GONE
            }
        }

        dialogBinding.exerciseCategoryAutoCompleteTextView.setOnItemClickListener { parent, _, position, _ ->
            val selectedItem = parent.getItemAtPosition(position) as String
            if (selectedItem == createNewCategoryTrigger) {
                dialogBinding.newCategoryInputLayout.visibility = View.VISIBLE
                dialogBinding.newCategoryEditText.requestFocus()
            } else {
                dialogBinding.newCategoryInputLayout.visibility = View.GONE
                dialogBinding.newCategoryEditText.text?.clear()
            }
        }

        if (exercise != null) {
            dialogBinding.exerciseNameEditText.setText(exercise.name)
            // Category is pre-filled above, after adapter is set
            dialogBinding.exerciseDescriptionEditText.setText(exercise.description)
        }

        dialogBinding.saveButton.setOnClickListener {
            val name = dialogBinding.exerciseNameEditText.text.toString().trim()
            val description = dialogBinding.exerciseDescriptionEditText.text.toString().trim()
            var finalCategoryName = dialogBinding.exerciseCategoryAutoCompleteTextView.text.toString().trim()

            // Get the selected exercise type
            val selectedExerciseTypeDisplayName = dialogBinding.exerciseTypeAutoCompleteTextView.text.toString().trim()
            val selectedExerciseTypeIndex = exerciseTypeDisplayNames.indexOf(selectedExerciseTypeDisplayName)
            val finalExerciseType = if (selectedExerciseTypeIndex >= 0) {
                exerciseTypes[selectedExerciseTypeIndex]
            } else {
                "normal" // Default fallback
            }

            if (finalCategoryName == createNewCategoryTrigger) {
                finalCategoryName = dialogBinding.newCategoryEditText.text.toString().trim()
                if (finalCategoryName.isEmpty()) {
                    dialogBinding.newCategoryInputLayout.error = getString(R.string.input_error_category_required)
                    return@setOnClickListener
                } else {
                    dialogBinding.newCategoryInputLayout.error = null
                }
            }

            if (name.isEmpty()) {
                dialogBinding.exerciseNameInputLayout.error = getString(R.string.input_error_name_required)
                return@setOnClickListener
            } else {
                dialogBinding.exerciseNameInputLayout.error = null
            }

            if (finalCategoryName.isEmpty() && dialogBinding.exerciseCategoryAutoCompleteTextView.text.toString().trim() != createNewCategoryTrigger) {
                 dialogBinding.exerciseCategoryInputLayout.error = getString(R.string.input_error_category_required)
                 return@setOnClickListener
            } else if (finalCategoryName.isEmpty() && dialogBinding.exerciseCategoryAutoCompleteTextView.text.toString().trim() == createNewCategoryTrigger) {
                // This case is already handled by the newCategoryEditText check above
            }
            else {
                dialogBinding.exerciseCategoryInputLayout.error = null
            }


            val currentUserId = authManager.getCurrentUserId()
            if (currentUserId == null && exercise == null) {
                 Toast.makeText(context, getString(R.string.please_log_in_to_manage_exercises_toast), Toast.LENGTH_SHORT).show()
                 return@setOnClickListener
            }

            val exerciseToSave = exercise?.copy(
                name = name,
                category = finalCategoryName,
                description = description.ifEmpty { null },
                exerciseType = finalExerciseType
            ) ?: UserExerciseDefinition(
                id = "",
                userId = currentUserId!!,
                name = name,
                category = finalCategoryName,
                description = description.ifEmpty { null },
                exerciseType = finalExerciseType
            )

            viewLifecycleOwner.lifecycleScope.launch {
                val result = exerciseRepository.saveExercise(exerciseToSave)
                result.fold(
                    onSuccess = {
                        dialog.dismiss()
                        loadUserExercises()
                        Toast.makeText(context, getString(R.string.exercise_saved_toast), Toast.LENGTH_SHORT).show()
                    },
                    onFailure = { exception ->
                        Toast.makeText(context, getString(R.string.error_saving_exercise_toast) + ": ${exception.message}", Toast.LENGTH_LONG).show()
                    }
                )
            }
        }

        dialogBinding.cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun handleEditExercise(exercise: UserExerciseDefinition) {
        showAddEditExerciseDialog(exercise)
    }

    private fun handleDeleteExercise(exercise: UserExerciseDefinition) {
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.dialog_title_delete_exercise)) // Assume R.string.dialog_title_delete_exercise
            .setMessage(getString(R.string.confirm_delete_exercise_message, exercise.name))
            .setPositiveButton(getString(R.string.delete_button)) { _, _ ->
                viewLifecycleOwner.lifecycleScope.launch {
                    val result = exerciseRepository.deleteExercise(exercise.id)
                    result.fold(
                        onSuccess = {
                            loadUserExercises()
                            Toast.makeText(context, getString(R.string.exercise_deleted_toast), Toast.LENGTH_SHORT).show()
                        },
                        onFailure = { exception ->
                            Toast.makeText(context, getString(R.string.error_deleting_exercise_toast) + ": ${exception.message}", Toast.LENGTH_LONG).show()
                        }
                    )
                }
            }
            .setNegativeButton(getString(R.string.cancel_button), null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
