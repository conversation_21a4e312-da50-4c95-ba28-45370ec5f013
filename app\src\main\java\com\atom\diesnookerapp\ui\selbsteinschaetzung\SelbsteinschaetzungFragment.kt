package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton

class SelbsteinschaetzungFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_selbsteinschaetzung, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val items = listOf(
            AssessmentItem("before_training", "Vor Training"),
            AssessmentItem("after_training", "Nach Training"),
            AssessmentItem("before_tournament", "Vor Turnier"),
            AssessmentItem("after_tournament", "Nach Turnier"),
            AssessmentItem("questions", "Fragen")
        )

        val adapter = AssessmentAdapter(items) { item ->
            when (item.id) {
                "before_training" -> findNavController().navigate(R.id.beforeTrainingFragment)
                "after_training" -> findNavController().navigate(R.id.afterTrainingFragment)
                "before_tournament" -> findNavController().navigate(R.id.beforeTournamentFragment)
                "after_tournament" -> findNavController().navigate(R.id.afterTournamentFragment)
                "questions" -> findNavController().navigate(R.id.questionsFragment)
            }
        }

        recyclerView.adapter = adapter

        view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton).setOnClickListener {
            findNavController().navigate(R.id.action_selbsteinschaetzungFragment_to_historyFragment)
        }
    }
}