package com.atom.diesnookerapp.ui.settings

import android.app.AlertDialog
import android.app.DatePickerDialog
import android.content.Context
import android.view.ContextThemeWrapper
import android.view.View
import android.widget.PopupMenu
import com.atom.diesnookerapp.R

/**
 * Helper class for applying themes to various UI components
 */
object ThemeHelper {

    /**
     * Creates a PopupMenu with the appropriate theme based on the current app theme
     */
    fun createThemedPopupMenu(context: Context, anchor: View): PopupMenu {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        val popupStyle = when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.PopupMenuStyle_Snooker
            ThemePreferences.THEME_BLUE -> R.style.PopupMenuStyle_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.PopupMenuStyle_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.PopupMenuStyle_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.PopupMenuStyle_Crimson
            ThemePreferences.THEME_NEON -> R.style.PopupMenuStyle_Neon
            ThemePreferences.THEME_DARK -> R.style.PopupMenuStyle_Dark
            else -> R.style.PopupMenuStyle
        }

        // Create a themed context with the appropriate popup menu style
        val themedContext = ContextThemeWrapper(context, popupStyle)

        // Create and return the themed PopupMenu
        return PopupMenu(themedContext, anchor)
    }

    /**
     * Gets the appropriate dialog style based on the current app theme
     */
    fun getDialogStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.DropdownDialog_Snooker
            ThemePreferences.THEME_BLUE -> R.style.DropdownDialog_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.DropdownDialog_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.DropdownDialog_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.DropdownDialog_Crimson
            ThemePreferences.THEME_NEON -> R.style.DropdownDialog_Neon
            ThemePreferences.THEME_DARK -> R.style.DropdownDialog_Dark
            else -> R.style.DropdownDialog
        }
    }

    /**
     * Gets the appropriate dialog style with default animations based on the current app theme
     */
    fun getDefaultAnimationDialogStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.DefaultAnimationDialog_Snooker
            ThemePreferences.THEME_BLUE -> R.style.DefaultAnimationDialog_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.DefaultAnimationDialog_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.DefaultAnimationDialog_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.DefaultAnimationDialog_Crimson
            ThemePreferences.THEME_NEON -> R.style.DefaultAnimationDialog_Neon
            ThemePreferences.THEME_DARK -> R.style.DefaultAnimationDialog_Dark
            else -> R.style.DefaultAnimationDialog
        }
    }

    /**
     * Creates an AlertDialog.Builder with the appropriate theme and default animations based on the current app theme
     */
    fun createDefaultAnimationAlertDialogBuilder(context: Context): AlertDialog.Builder {
        val dialogStyle = getDefaultAnimationDialogStyleForCurrentTheme(context)
        return AlertDialog.Builder(context, dialogStyle)
    }

    /**
     * Creates a DatePickerDialog with the appropriate theme based on the current app theme
     */
    fun createThemedDatePickerDialog(
        context: Context,
        listener: DatePickerDialog.OnDateSetListener,
        year: Int,
        month: Int,
        day: Int
    ): DatePickerDialog {
        val datePickerDialog = DatePickerDialog(
            ContextThemeWrapper(context, R.style.DatePickerDialogTheme),
            listener,
            year,
            month,
            day
        )
        return datePickerDialog
    }

    /**
     * Creates an AlertDialog.Builder with the appropriate theme based on the current app theme
     */
    fun createThemedAlertDialogBuilder(context: Context): AlertDialog.Builder {
        val dialogStyle = getDialogStyleForCurrentTheme(context)
        return AlertDialog.Builder(context, dialogStyle)
    }

    /**
     * Gets the appropriate TabLayout style based on the current app theme
     */
    fun getTabLayoutStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.CustomTabLayout_Snooker
            ThemePreferences.THEME_BLUE -> R.style.CustomTabLayout_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.CustomTabLayout_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.CustomTabLayout_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.CustomTabLayout_Crimson
            ThemePreferences.THEME_NEON -> R.style.CustomTabLayout_Neon
            ThemePreferences.THEME_DARK -> R.style.CustomTabLayout_Dark
            else -> R.style.CustomTabLayout
        }
    }

    /**
     * Gets the appropriate TextInputLayout style based on the current app theme
     */
    fun getTextInputLayoutStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.CustomTextInputLayout_Snooker
            ThemePreferences.THEME_BLUE -> R.style.CustomTextInputLayout_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.CustomTextInputLayout_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.CustomTextInputLayout_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.CustomTextInputLayout_Crimson
            ThemePreferences.THEME_NEON -> R.style.CustomTextInputLayout_Neon
            ThemePreferences.THEME_DARK -> R.style.CustomTextInputLayout_Dark
            else -> R.style.CustomTextInputLayout
        }
    }

    /**
     * Gets the appropriate Dropdown TextInputLayout style based on the current app theme
     */
    fun getDropdownTextInputLayoutStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.CustomDropdownTextInputLayout_Snooker
            ThemePreferences.THEME_BLUE -> R.style.CustomDropdownTextInputLayout_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.CustomDropdownTextInputLayout_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.CustomDropdownTextInputLayout_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.CustomDropdownTextInputLayout_Crimson
            ThemePreferences.THEME_NEON -> R.style.CustomDropdownTextInputLayout_Neon
            ThemePreferences.THEME_DARK -> R.style.CustomDropdownTextInputLayout_Dark
            else -> R.style.CustomDropdownTextInputLayout
        }
    }

    /**
     * Gets the appropriate dropdown item style based on the current app theme
     */
    fun getDropdownStyleForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.CustomDropdownStyle_Snooker
            ThemePreferences.THEME_BLUE -> R.style.CustomDropdownStyle_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.CustomDropdownStyle_DarkBlue
            ThemePreferences.THEME_OCEAN -> R.style.CustomDropdownStyle_Ocean
            ThemePreferences.THEME_CRIMSON -> R.style.CustomDropdownStyle_Crimson
            ThemePreferences.THEME_NEON -> R.style.CustomDropdownStyle_Neon
            ThemePreferences.THEME_DARK -> R.style.CustomDropdownStyle_Dark
            else -> R.style.CustomDropdownStyle
        }
    }

    /**
     * Gets the appropriate dropdown background drawable based on the current app theme
     */
    fun getDropdownBackgroundForCurrentTheme(context: Context): Int {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.drawable.dropdown_background_solid_snooker
            ThemePreferences.THEME_BLUE -> R.drawable.dropdown_background_solid_blue
            ThemePreferences.THEME_DARK_BLUE -> R.drawable.dropdown_background_solid_dark_blue
            ThemePreferences.THEME_OCEAN -> R.drawable.dropdown_background_solid_ocean
            ThemePreferences.THEME_CRIMSON -> R.drawable.dropdown_background_solid_crimson
            ThemePreferences.THEME_NEON -> R.drawable.dropdown_background_solid_neon
            ThemePreferences.THEME_DARK -> R.drawable.dropdown_background_solid_dark
            else -> R.drawable.dropdown_background_solid_default
        }
    }
}
