package com.atom.diesnookerapp.ui.ergebniserfassung

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import android.transition.AutoTransition
import android.transition.TransitionManager
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.recyclerview.widget.DiffUtil

class ExerciseHistoryAdapter(
    private var items: List<ExerciseHistoryItem>
) : RecyclerView.Adapter<ExerciseHistoryAdapter.ViewHolder>() {

    private var visibleItems: List<ExerciseHistoryItem> = items
        set(value) {
            val oldList = field
            field = value
            calculateDiff(oldList, value)
        }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val titleText: TextView = view.findViewById(R.id.titleText)
        val scoreText: TextView = view.findViewById(R.id.scoreText)
        val timeText: TextView = view.findViewById(R.id.timeText)
        val scoreLabel: TextView = view.findViewById(R.id.scoreLabel)
        val container: View = view
        val contentContainer: View = view.findViewById(R.id.contentContainer)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_exercise_history, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = visibleItems[position]

        if (item.isHeader) {
            holder.titleText.text = item.category
            holder.titleText.setTextColor(holder.itemView.context.getColor(android.R.color.holo_blue_dark))
            holder.titleText.textSize = 20f
            holder.contentContainer.visibility = View.GONE

            holder.container.setOnClickListener {
                toggleCategory(item.category, holder.container)
            }
            return
        }

        holder.titleText.text = item.exerciseTitle
        holder.titleText.setTextColor(holder.itemView.context.getColor(android.R.color.black))
        holder.titleText.textSize = 18f

        val scoreBuilder = StringBuilder()

        // Group attempts by spelt type for split exercises or stellungsspiel exercises
        if (item.category == "Splits") {
            val groupedAttempts = item.attempts.groupBy { it.spelt }

            // Add scores for each spelt type
            groupedAttempts["3"]?.let { attempts ->
                scoreBuilder.append("3er:\n")
                attempts.forEachIndexed { index, attempt ->
                    if (attempt.score != null) {
                        scoreBuilder.append("Aufnahme ${index + 1}: ${attempt.score}\n")
                    }
                }
                scoreBuilder.append("\n")
            }

            groupedAttempts["6"]?.let { attempts ->
                scoreBuilder.append("6er:\n")
                attempts.forEachIndexed { index, attempt ->
                    if (attempt.score != null) {
                        scoreBuilder.append("Aufnahme ${index + 1}: ${attempt.score}\n")
                    }
                }
                scoreBuilder.append("\n")
            }

            groupedAttempts["10"]?.let { attempts ->
                scoreBuilder.append("10er:\n")
                attempts.forEachIndexed { index, attempt ->
                    if (attempt.score != null) {
                        scoreBuilder.append("Aufnahme ${index + 1}: ${attempt.score}\n")
                    }
                }
            }
        } else if (item.exerciseId.startsWith("stellungsspiel_") || item.exerciseId == "hohe_schwarze" || item.exerciseId == "blau_durch_bc") {
            // Stellungsspiel exercise display

            // Get all attempts and sort them by timestamp (number)
            val allAttempts = item.attempts.sortedBy { it.number }

            // Find all completion markers (check both isCompletionMarker flag and score == -1)
            val completionMarkers = allAttempts
                .filter { it.isCompletionMarker || it.score == -1 }
                .map { it.number }

            // If there are no completion markers, just show all attempts as one group
            val exerciseGroups = mutableListOf<List<Attempt>>()

            if (completionMarkers.isEmpty()) {
                // Group by column and take max 3 attempts per column
                val regularAttempts = allAttempts.filter { !it.isCompletionMarker && it.score != -1 }
                val attemptsByColumn = regularAttempts.groupBy { it.spelt ?: "" }
                val limitedAttempts = mutableListOf<Attempt>()

                // For each column, take only the first 3 attempts
                for (column in 1..6) {
                    val columnKey = column.toString()
                    val columnAttempts = attemptsByColumn[columnKey] ?: emptyList()
                    limitedAttempts.addAll(columnAttempts.take(3))
                }

                exerciseGroups.add(limitedAttempts.sortedBy { it.number })
            } else {
                // Create a list of all boundary points (start of data + completion markers + end of data)
                val boundaries = mutableListOf(0) // Start of data
                boundaries.addAll(completionMarkers)
                boundaries.add(Int.MAX_VALUE) // End of data

                // For each pair of consecutive boundaries, create an exercise group
                for (i in 0 until boundaries.size - 1) {
                    val startBoundary = boundaries[i]
                    val endBoundary = boundaries[i + 1]

                    // Get all non-marker attempts between these boundaries
                    val groupAttempts = allAttempts.filter {
                        !it.isCompletionMarker && it.score != -1 &&
                        it.number > startBoundary &&
                        it.number < endBoundary
                    }

                    if (groupAttempts.isNotEmpty()) {
                        // Group by column and limit to 3 attempts per column
                        val attemptsByColumn = groupAttempts.groupBy { it.spelt ?: "" }
                        val limitedAttempts = mutableListOf<Attempt>()

                        // For each column, take only the first 3 attempts
                        for (column in 1..6) {
                            val columnKey = column.toString()
                            val columnAttempts = attemptsByColumn[columnKey] ?: emptyList()
                            limitedAttempts.addAll(columnAttempts.take(3))
                        }

                        exerciseGroups.add(limitedAttempts.sortedBy { it.number })
                    }
                }
            }

            // Log the number of exercise groups found
            Log.d("ExerciseHistoryAdapter", "Found ${exerciseGroups.size} exercise groups for ${item.exerciseTitle}")

            // Now display each exercise group separately
            exerciseGroups.forEachIndexed { groupIndex, attempts ->
                // Add a header for each exercise
                scoreBuilder.append("Übung ${groupIndex + 1}:\n")
                scoreBuilder.append("------------------------\n")

                // Calculate overall accuracy for this exercise group
                val validAttempts = attempts.filter { it.score != null }
                if (validAttempts.isNotEmpty()) {
                    val successCount = validAttempts.count { it.score == 1 }
                    val totalCount = validAttempts.size
                    val overallSuccessRate = (successCount.toFloat() / totalCount) * 100
                    scoreBuilder.append("Gesamtgenauigkeit: ${String.format("%.1f", overallSuccessRate)}%\n\n")
                }

                // Group by column within this exercise
                val groupedByColumn = attempts.groupBy { it.spelt }

                // Add scores for each column (1-6)
                for (column in 1..6) {
                    val columnKey = column.toString()
                    groupedByColumn[columnKey]?.let { columnAttempts ->
                        // Only take the first 3 attempts per column for each exercise
                        val limitedAttempts = columnAttempts.take(3)

                        scoreBuilder.append("Spalte $column:\n")
                        limitedAttempts.forEachIndexed { index, attempt ->
                            if (attempt.score != null) {
                                val result = if (attempt.score == 1) "Erfolg" else "Fehlschlag"
                                scoreBuilder.append("Versuch ${index + 1}: $result\n")
                            }
                        }
                        scoreBuilder.append("\n")
                    }
                }

                // Add a separator between exercise groups
                if (groupIndex < exerciseGroups.size - 1) {
                    scoreBuilder.append("\n=========================\n\n")
                }
            }
        } else {
            // Regular exercise display
            item.attempts.forEach { attempt ->
                if (attempt.score != null) {
                    scoreBuilder.append("Aufnahme ${attempt.number}: ${attempt.score}\n")
                }
            }
        }

        val timeBuilder = StringBuilder()
        val totalTime = item.attempts.mapNotNull { it.timeInMinutes }.sum()
        if (totalTime > 0) {
            timeBuilder.append("$totalTime Min")
        }

        holder.scoreText.text = scoreBuilder.toString().trimEnd()
        holder.timeText.text = timeBuilder.toString()

        holder.scoreLabel.visibility = View.GONE
        holder.scoreText.visibility = if (scoreBuilder.isEmpty()) View.GONE else View.VISIBLE
        holder.timeText.visibility = if (timeBuilder.isEmpty()) View.GONE else View.VISIBLE
        holder.contentContainer.visibility = View.VISIBLE
    }

    private fun toggleCategory(category: String, view: View) {
        val mutableItems = items.toMutableList()
        val headerIndex = mutableItems.indexOfFirst { it.isHeader && it.category == category }

        if (headerIndex != -1) {
            val willExpand = !mutableItems[headerIndex].isExpanded

            // First update the header's expanded state
            mutableItems[headerIndex] = mutableItems[headerIndex].copy(
                isExpanded = willExpand
            )

            // Then animate only the content of this category
            val parentRecyclerView = view.parent.parent as? RecyclerView
            parentRecyclerView?.let { recyclerView ->
                TransitionManager.beginDelayedTransition(
                    recyclerView,
                    AutoTransition().apply {
                        duration = 300
                        interpolator = FastOutSlowInInterpolator()
                        // Only animate the height and alpha
                        removeTarget(TextView::class.java)
                    }
                )
            }

            items = mutableItems
            updateVisibleItems()
        }
    }

    private fun updateVisibleItems() {
        visibleItems = items.filter { item ->
            item.isHeader || (items.find { it.isHeader && it.category == item.category }?.isExpanded == true)
        }
    }

    override fun getItemCount() = visibleItems.size

    fun updateItems(newItems: List<ExerciseHistoryItem>) {
        val expandedCategories = items.filter { it.isHeader && it.isExpanded }
            .map { it.category }
            .toSet()

        items = newItems.map { item ->
            if (item.isHeader) {
                item.copy(isExpanded = item.category in expandedCategories)
            } else {
                item
            }
        }
        updateVisibleItems()
    }

    private fun calculateDiff(oldList: List<ExerciseHistoryItem>, newList: List<ExerciseHistoryItem>) {
        val diffCallback = object : DiffUtil.Callback() {
            override fun getOldListSize() = oldList.size
            override fun getNewListSize() = newList.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldList[oldItemPosition]
                val newItem = newList[newItemPosition]
                return oldItem.exerciseId == newItem.exerciseId &&
                       oldItem.isHeader == newItem.isHeader &&
                       oldItem.category == newItem.category
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return oldList[oldItemPosition] == newList[newItemPosition]
            }
        }

        val diffResult = DiffUtil.calculateDiff(diffCallback, true)
        diffResult.dispatchUpdatesTo(this)
    }
}