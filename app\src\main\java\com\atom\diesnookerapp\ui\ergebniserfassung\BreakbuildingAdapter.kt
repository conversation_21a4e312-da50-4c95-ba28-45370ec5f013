package com.atom.diesnookerapp.ui.ergebniserfassung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.trainingsplan.ExerciseItem

class BreakbuildingAdapter(
    private val items: List<ExerciseItem>,
    private val exercisePreferences: ExercisePreferences,
    private val onItemClick: (ExerciseItem) -> Unit
) : RecyclerView.Adapter<BreakbuildingAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val titleText: TextView = view.findViewById(R.id.titleText)
        val averageTimeText: TextView = view.findViewById(R.id.averageTimeText)
        val cardView: View = view.findViewById(R.id.cardView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_breakbuilding, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.titleText.text = item.name // Use name from ExerciseItem
        val averageTime = exercisePreferences.getAverageTimeLastSevenDays(item.id) // Use id from ExerciseItem
        holder.averageTimeText.text = "Ø letzte 7 Tage: ${String.format("%.1f", averageTime)} Min"
        holder.cardView.setOnClickListener { onItemClick(item) } // Pass ExerciseItem
    }

    override fun getItemCount() = items.size
} 