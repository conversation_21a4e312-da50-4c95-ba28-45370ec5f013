package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.util.Log
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinitionRepository
import com.google.gson.Gson
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.temporal.TemporalAdjusters
import java.util.UUID

class TrainingsplanManager(private val context: Context) {
    private val TAG = "TrainingsplanManager"
    private val trainingsplanPreferences = TrainingsplanPreferences(context)
    private val userExerciseDefinitionRepository: UserExerciseDefinitionRepository
    private val firebaseAuthManager: FirebaseAuthManager

    init {
        // Initialize repositories. If they require context, it's available.
        // For this example, assuming default constructors or singleton access.
        userExerciseDefinitionRepository = UserExerciseDefinitionRepository()
        firebaseAuthManager = FirebaseAuthManager()
    }

    // Get the current training plan for this month
    fun getCurrentTrainingsplan(): Trainingsplan {
        val monthStart = LocalDate.now().withDayOfMonth(1)
        val currentTrainingsplan = trainingsplanPreferences.getTrainingsplan()

        return currentTrainingsplan?.takeIf {
            // Compare by year and month to handle the transition from weekly to monthly plans
            it.weekStartDate.year == monthStart.year &&
            it.weekStartDate.month == monthStart.month
        } ?: run {
            // Create a new plan for this month
            val newPlan = Trainingsplan(monthStart)
            saveCurrentTrainingsplan(newPlan)
            newPlan
        }
    }

    // Save the current training plan
    fun saveCurrentTrainingsplan(plan: Trainingsplan) {
        trainingsplanPreferences.saveTrainingsplan(plan)
    }

    suspend fun getExerciseCategories(): List<String> {
        if (!firebaseAuthManager.isLoggedIn()) {
            Log.d(TAG, "User not logged in. Returning empty categories list.")
            return emptyList()
        }
        val userId = firebaseAuthManager.getCurrentUserId()
        if (userId == null) {
            Log.d(TAG, "User ID is null. Returning empty categories list.")
            return emptyList()
        }

        return userExerciseDefinitionRepository.getExercisesForUser(userId)
            .fold(
                onSuccess = { exercises ->
                    exercises.mapNotNull { it.category.takeIf { cat -> cat.isNotBlank() } }.distinct()
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error fetching exercise categories: ${exception.message}", exception)
                    emptyList()
                }
            )
    }

    suspend fun getExercisesForCategory(category: String): List<ExerciseItem> {
        if (!firebaseAuthManager.isLoggedIn()) {
            Log.d(TAG, "User not logged in. Returning empty exercises list for category $category.")
            return emptyList()
        }
        val userId = firebaseAuthManager.getCurrentUserId()
        if (userId == null) {
            Log.d(TAG, "User ID is null. Returning empty exercises list for category $category.")
            return emptyList()
        }

        return userExerciseDefinitionRepository.getExercisesForUser(userId)
            .fold(
                onSuccess = { exercises ->
                    exercises
                        .filter { it.category == category }
                        .map { def ->
                            ExerciseItem(
                                id = def.id,
                                name = def.name,
                                category = def.category,
                                exerciseType = def.exerciseType
                                // isSelected is false by default in ExerciseItem
                            )
                        }
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error fetching exercises for category $category: ${exception.message}", exception)
                    emptyList()
                }
            )
    }

    suspend fun getAllExercises(): List<ExerciseItem> {
        if (!firebaseAuthManager.isLoggedIn()) {
            Log.d(TAG, "User not logged in. Returning empty list of all exercises.")
            return emptyList()
        }
        val userId = firebaseAuthManager.getCurrentUserId()
        if (userId == null) {
            Log.d(TAG, "User ID is null. Returning empty list of all exercises.")
            return emptyList()
        }

        return userExerciseDefinitionRepository.getExercisesForUser(userId)
            .fold(
                onSuccess = { exercises ->
                    exercises.map { def ->
                        ExerciseItem(
                            id = def.id,
                            name = def.name,
                            category = def.category,
                            exerciseType = def.exerciseType
                            // isSelected is false by default in ExerciseItem
                        )
                    }
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error fetching all exercises: ${exception.message}", exception)
                    emptyList()
                }
            )
    }

    /**
     * Adds a list of exercises to the training plan with the specified target count
     * If an exercise with the same ID already exists, its target count is incremented instead
     */
    fun addExercisesToPlan(exercises: List<ExerciseItem>, targetCount: Int = 1) {
        val currentPlan = getCurrentTrainingsplan()
        val updatedItems = currentPlan.items

        exercises.forEach { exercise ->
            // Check if an exercise with the same ID already exists
            val existingItem = updatedItems.find { it.exerciseId == exercise.id }

            if (existingItem != null) {
                // If it exists, increment its target count
                existingItem.targetCount += targetCount
            } else {
                // If it doesn't exist, create a new item
                val newItem = TrainingsplanItem(
                    id = UUID.randomUUID().toString(),
                    name = exercise.name,
                    isChecked = false,
                    isUserDefined = false,
                    exerciseId = exercise.id,
                    exerciseType = exercise.category,
                    completionCount = 0,
                    targetCount = targetCount
                )
                updatedItems.add(newItem)
            }
        }

        saveCurrentTrainingsplan(currentPlan)
    }

    /**
     * Adds a custom exercise to the training plan
     * If a custom exercise with the same name and category already exists, its target count is incremented instead
     */
    fun addCustomExerciseToPlan(name: String, category: String? = null, targetCount: Int = 1) {
        val currentPlan = getCurrentTrainingsplan()
        val updatedItems = currentPlan.items

        // Check if a custom exercise with the same name and category already exists
        val existingItem = updatedItems.find {
            it.isUserDefined &&
            it.name == name &&
            it.exerciseType == category
        }

        if (existingItem != null) {
            // If it exists, increment its target count
            existingItem.targetCount += targetCount
        } else {
            // If it doesn't exist, create a new item
            val newItem = TrainingsplanItem(
                id = UUID.randomUUID().toString(),
                name = name,
                isChecked = false,
                isUserDefined = true,
                exerciseId = null,
                exerciseType = category,
                completionCount = 0,
                targetCount = targetCount
            )
            updatedItems.add(newItem)
        }

        saveCurrentTrainingsplan(currentPlan)
    }

    // Mark an exercise as checked/unchecked
    fun toggleItemChecked(itemId: String, isChecked: Boolean) {
        val plan = getCurrentTrainingsplan()

        plan.items.find { it.id == itemId }?.let {
            if (isChecked) {
                it.completionCount = minOf(it.completionCount + 1, it.targetCount)
                // Always set isChecked based on the completion count to ensure consistency
                it.isChecked = it.completionCount >= it.targetCount
            } else {
                it.completionCount = maxOf(it.completionCount - 1, 0)
                // Always set isChecked based on the completion count to ensure consistency
                it.isChecked = it.completionCount >= it.targetCount
            }
            // Save the updated plan to persist changes
            saveCurrentTrainingsplan(plan)
        }
    }

    /**
     * Marks an exercise as completed when time is added
     * @return true if at least one exercise was marked as completed, false otherwise
     */
    fun markExerciseCompleted(exerciseId: String): Boolean {
        val currentPlan = getCurrentTrainingsplan()
        var anyMarked = false

        // Find items with this exerciseId and increment their completion count
        currentPlan.items.forEach { item ->
            if (item.exerciseId == exerciseId && item.completionCount < item.targetCount) {
                item.completionCount = minOf(item.completionCount + 1, item.targetCount)
                item.isChecked = item.completionCount >= item.targetCount
                anyMarked = true
            }
        }

        if (anyMarked) {
            saveCurrentTrainingsplan(currentPlan)
        }

        return anyMarked
    }

    // Remove an item from the plan
    fun removeItem(itemId: String) {
        val plan = getCurrentTrainingsplan()
        plan.items.removeAll { it.id == itemId }
        saveCurrentTrainingsplan(plan)
    }

    // Reset the current plan (move to history)
    fun resetAndArchiveTrainingsplan(): Boolean {
        val currentPlan = getCurrentTrainingsplan()

        // Only archive if there are items
        if (currentPlan.items.isNotEmpty()) {
            // Add to history
            val history = getTrainingsplanHistory().toMutableList()
            history.add(0, currentPlan) // Add at the beginning

            // Save history
            trainingsplanPreferences.saveTrainingsplanHistory(history)

            // Clear current plan
            trainingsplanPreferences.clearCurrentTrainingsplan()

            return true
        }

        return false
    }

    // Get the training plan history
    fun getTrainingsplanHistory(): List<Trainingsplan> {
        return trainingsplanPreferences.getTrainingsplanHistory()
    }

    // Clear the training plan history
    fun clearTrainingsplanHistory() {
        trainingsplanPreferences.clearHistory()
    }

    // Get incomplete exercises for notifications
    fun getIncompleteExercises(): List<TrainingsplanItem> {
        return getCurrentTrainingsplan().items.filter {
            it.completionCount < it.targetCount
        }
    }

    // Get exercise by ID
    suspend fun getExerciseById(exerciseId: String): ExerciseItem? {
        // This still iterates over all exercises, which might be inefficient
        // if the number of exercises is very large.
        // Consider if a direct repository call is needed for performance in the future.
        return getAllExercises().find { it.id == exerciseId }
    }
}
