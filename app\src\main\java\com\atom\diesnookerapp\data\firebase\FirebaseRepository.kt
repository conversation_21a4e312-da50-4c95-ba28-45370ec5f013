package com.atom.diesnookerapp.data.firebase

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await

/**
 * Base repository class for Firebase operations
 */
abstract class FirebaseRepository<T : FirebaseModel>(
    private val collectionPath: String
) {
    private val firestore = FirebaseFirestore.getInstance()
    private val authManager = FirebaseAuthManager()
    
    companion object {
        private const val TAG = "FirebaseRepository"
    }
    
    /**
     * Get a reference to the collection
     */
    protected fun getCollection() = firestore.collection(collectionPath)
    
    /**
     * Get all documents for the current user
     */
    suspend fun getAll(modelClass: Class<T>): Result<List<T>> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))
        
        return try {
            val snapshot = getCollection()
                .whereEqualTo("userId", userId)
                .orderBy("lastUpdated", Query.Direction.DESCENDING)
                .get()
                .await()
            
            val items = snapshot.documents.mapNotNull { doc ->
                try {
                    doc.toObject(modelClass)?.apply { id = doc.id }
                } catch (e: Exception) {
                    Log.e(TAG, "Error converting document: ${e.message}")
                    null
                }
            }
            
            Result.success(items)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting documents: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * Save a document to Firestore
     */
    suspend fun save(item: T): Result<T> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))
        
        return try {
            item.userId = userId
            item.lastUpdated = System.currentTimeMillis()
            
            if (item.id.isBlank()) {
                // Create new document
                val docRef = getCollection().add(item).await()
                item.id = docRef.id
            } else {
                // Update existing document
                getCollection().document(item.id).set(item).await()
            }
            
            Result.success(item)
        } catch (e: Exception) {
            Log.e(TAG, "Error saving document: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * Delete a document from Firestore
     */
    suspend fun delete(id: String): Result<Unit> {
        return try {
            getCollection().document(id).delete().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting document: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * Get a document by ID
     */
    suspend fun getById(id: String, modelClass: Class<T>): Result<T> {
        return try {
            val doc = getCollection().document(id).get().await()
            val item = doc.toObject(modelClass)?.apply { this.id = doc.id }
            
            if (item != null) {
                Result.success(item)
            } else {
                Result.failure(Exception("Document not found"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting document: ${e.message}")
            Result.failure(e)
        }
    }
}
