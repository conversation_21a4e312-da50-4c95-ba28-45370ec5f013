package com.atom.diesnookerapp.ui.aufgaben

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import org.threeten.bp.temporal.WeekFields
import java.util.Locale

class TaskHistoryEntriesAdapter(
    private val historyEntries: List<TaskHistoryEntry>
) : RecyclerView.Adapter<TaskHistoryEntriesAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val weekText: TextView = view.findViewById(R.id.weekText)
        val dateRangeText: TextView = view.findViewById(R.id.dateRangeText)
        val totalPointsText: TextView = view.findViewById(R.id.totalPointsText)
        val taskCompletionsRecyclerView: RecyclerView = view.findViewById(R.id.taskCompletionsRecyclerView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_task_history, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val entry = historyEntries[position]

        // Format week number and date range
        val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")

        // Handle null endDate by using current date as fallback
        val endDate = entry.endDate ?: LocalDate.now()

        // Get week number and year
        val weekNumber = endDate.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear())
        val year = endDate.year
        holder.weekText.text = "Woche $weekNumber, $year"

        // Calculate date range (Monday to Sunday of that week)
        val weekStart = endDate.minusDays(endDate.dayOfWeek.value - 1L)
        val weekEnd = weekStart.plusDays(6)
        holder.dateRangeText.text = "${weekStart.format(dateFormatter)} - ${weekEnd.format(dateFormatter)}"

        // Set total points
        holder.totalPointsText.text = "Gesammelte Punkte: ${entry.totalPoints}"

        // Set up nested RecyclerView for task completions
        holder.taskCompletionsRecyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
        holder.taskCompletionsRecyclerView.adapter = TaskCompletionAdapter(entry.taskCompletions)
    }

    override fun getItemCount() = historyEntries.size
}

class TaskCompletionAdapter(
    private val completions: List<TaskCompletionSummary>
) : RecyclerView.Adapter<TaskCompletionAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val taskTitleText: TextView = view.findViewById(R.id.taskTitleText)
        val completionText: TextView = view.findViewById(R.id.completionText)
        val pointsText: TextView = view.findViewById(R.id.pointsText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_task_completion, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val completion = completions[position]

        holder.taskTitleText.text = completion.title
        holder.completionText.text = "${completion.completions}/${completion.maxCompletions}"
        holder.pointsText.text = "${completion.points} Punkte"
    }

    override fun getItemCount() = completions.size
}