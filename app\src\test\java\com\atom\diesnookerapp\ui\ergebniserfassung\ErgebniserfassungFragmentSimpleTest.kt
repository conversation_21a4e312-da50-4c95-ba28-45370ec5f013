package com.atom.diesnookerapp.ui.ergebniserfassung

import org.junit.Test
import org.junit.Assert.*
import org.threeten.bp.LocalDate
import org.threeten.bp.DayOfWeek

class ErgebniserfassungFragmentSimpleTest {
    
    private val fragment = ErgebniserfassungFragment()
    
    @Test
    fun testGetWeekStartDate() {
        // Test Wednesday -> should return Monday of the same week
        assertEquals(LocalDate.of(2023, 10, 30), fragment.getWeekStartDate(LocalDate.of(2023, 11, 1)))
        
        // Test Monday -> should return the same Monday
        assertEquals(LocalDate.of(2023, 10, 30), fragment.getWeekStartDate(LocalDate.of(2023, 10, 30)))
        
        // Test Sunday -> should return Monday of the previous week
        assertEquals(LocalDate.of(2023, 10, 23), fragment.getWeekStartDate(LocalDate.of(2023, 10, 29)))
    }
    
    @Test
    fun testFormatDuration() {
        assertEquals("0 Sek.", fragment.formatDuration(0L))
        assertEquals("5 Sek.", fragment.formatDuration(5000L))
        assertEquals("1 Min.", fragment.formatDuration(60000L))
        assertEquals("1 Min. 30 Sek.", fragment.formatDuration(90000L))
        assertEquals("1 Std.", fragment.formatDuration(3600000L))
        assertEquals("1 Std. 15 Min. 30 Sek.", fragment.formatDuration(3600000L + 15*60000L + 30000L))
        assertEquals("2 Min.", fragment.formatDuration(120000L))
    }
    
    @Test
    fun testFormatDurationEdgeCases() {
        // Test exact minute
        assertEquals("1 Min.", fragment.formatDuration(60000L))

        // Test exact hour
        assertEquals("1 Std.", fragment.formatDuration(3600000L))

        // Test large duration
        assertEquals("2 Std. 30 Min. 45 Sek.", fragment.formatDuration(2*3600000L + 30*60000L + 45000L))
    }
    
    @Test
    fun testWeekStartDateCalculation() {
        // Test various days of the week
        val monday = LocalDate.of(2023, 11, 6) // Monday
        val tuesday = LocalDate.of(2023, 11, 7) // Tuesday
        val wednesday = LocalDate.of(2023, 11, 8) // Wednesday
        val thursday = LocalDate.of(2023, 11, 9) // Thursday
        val friday = LocalDate.of(2023, 11, 10) // Friday
        val saturday = LocalDate.of(2023, 11, 11) // Saturday
        val sunday = LocalDate.of(2023, 11, 12) // Sunday
        
        val expectedMonday = LocalDate.of(2023, 11, 6)
        
        assertEquals(expectedMonday, fragment.getWeekStartDate(monday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(tuesday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(wednesday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(thursday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(friday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(saturday))
        assertEquals(expectedMonday, fragment.getWeekStartDate(sunday))
    }
}
