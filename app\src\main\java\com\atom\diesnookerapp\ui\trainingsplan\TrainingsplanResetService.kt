package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.temporal.TemporalAdjusters

/**
 * Service class that handles the monthly reset of the training plan.
 * The training plan is reset at the beginning of each month and the previous month's plan is archived.
 */
class TrainingsplanResetService(private val context: Context) {
    private val trainingsplanPreferences = TrainingsplanPreferences(context)
    private val trainingsplanManager = TrainingsplanManager(context)

    /**
     * Checks if the current training plan needs to be reset and archives it if necessary.
     * This should be called whenever the app is started or the training plan is accessed.
     *
     * @return true if the plan was reset, false otherwise
     */
    fun checkAndResetTrainingsplan(): Boolean {
        val currentPlan = trainingsplanPreferences.getTrainingsplan() ?: return false
        val today = LocalDate.now()
        val currentMonthStart = today.withDayOfMonth(1)

        // If the current plan is from a previous month, archive it and create a new one
        if (currentPlan.weekStartDate.year != currentMonthStart.year ||
            currentPlan.weekStartDate.month != currentMonthStart.month) {
            // Archive the current plan
            val history = trainingsplanPreferences.getTrainingsplanHistory().toMutableList()

            // Set the date for each item to the plan's start date for historical reference
            currentPlan.items.forEach { it.date = currentPlan.weekStartDate }

            // Add to history and save
            history.add(currentPlan)
            trainingsplanPreferences.saveTrainingsplanHistory(history)

            // Clear the current plan to force creation of a new one
            trainingsplanPreferences.clearCurrentTrainingsplan()

            return true
        }

        return false
    }
}
