package com.atom.diesnookerapp

import android.app.Application
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_NO
import androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_YES
import com.atom.diesnookerapp.ui.aufgaben.DefaultTasks // Import the new object
import com.atom.diesnookerapp.ui.aufgaben.TaskManager // Import TaskManager
import com.atom.diesnookerapp.ui.settings.ThemePreferences
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.gms.security.ProviderInstaller
import com.google.firebase.FirebaseApp
import com.jakewharton.threetenabp.AndroidThreeTen
import java.security.NoSuchAlgorithmException
import javax.net.ssl.SSLContext

class SnookerApplication : Application() {
    companion object {
        private const val TAG = "SnookerApplication"
    }

    override fun onCreate() {
        super.onCreate()
        AndroidThreeTen.init(this)

        // Initialize Firebase
        try {
            FirebaseApp.initializeApp(this)
            Log.d(TAG, "Firebase initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase: ${e.message}")
        }

        // Update Android security provider to avoid SSL issues
        updateAndroidSecurityProvider()

        // Apply saved theme at application startup
        applyTheme()

        // Initialize default tasks
        val taskManager = TaskManager(applicationContext)
        taskManager.initializeTasksIfNeeded(DefaultTasks.allDefaultTasks)
    }

    private fun updateAndroidSecurityProvider() {
        try {
            ProviderInstaller.installIfNeeded(this)
            Log.d(TAG, "Security provider updated successfully")
        } catch (e: GooglePlayServicesRepairableException) {
            // Google Play Services is out of date, prompt user to update
            Log.e(TAG, "Google Play Services out of date: ${e.message}")
            // We'll continue anyway and hope for the best
        } catch (e: GooglePlayServicesNotAvailableException) {
            // Google Play Services is not available on this device
            Log.e(TAG, "Google Play Services not available: ${e.message}")
            // Create a fallback SSL context that doesn't depend on latest security provider
            try {
                val sslContext = SSLContext.getInstance("TLSv1.2")
                sslContext.init(null, null, null)
                SSLContext.setDefault(sslContext)
                Log.d(TAG, "Fallback SSL context initialized")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create fallback SSL context: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating security provider: ${e.message}")
        }
    }

    private fun applyTheme() {
        val themePreferences = ThemePreferences(this)
        val themeMode = themePreferences.getThemeMode()
        val customTheme = themePreferences.getCustomTheme()

        // First, reset any night mode to follow system
        // This helps prevent theme conflicts
        AppCompatDelegate.setDefaultNightMode(MODE_NIGHT_FOLLOW_SYSTEM)

        when (themeMode) {
            // Standard themes use AppCompatDelegate
            MODE_NIGHT_NO, MODE_NIGHT_YES, MODE_NIGHT_FOLLOW_SYSTEM -> {
                AppCompatDelegate.setDefaultNightMode(themeMode)
            }
            // Custom themes
            ThemePreferences.THEME_SNOOKER -> {
                setTheme(R.style.Theme_DieSnookerApp_Snooker)
            }
            ThemePreferences.THEME_BLUE -> {
                setTheme(R.style.Theme_DieSnookerApp_Blue)
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                setTheme(R.style.Theme_DieSnookerApp_DarkBlue)
            }
            ThemePreferences.THEME_OCEAN -> {
                setTheme(R.style.Theme_DieSnookerApp_Ocean)
            }
            ThemePreferences.THEME_CRIMSON -> {
                setTheme(R.style.Theme_DieSnookerApp_Crimson)
            }
            ThemePreferences.THEME_NEON -> {
                setTheme(R.style.Theme_DieSnookerApp_Neon)
            }
            // Default to system theme if unknown
            else -> AppCompatDelegate.setDefaultNightMode(MODE_NIGHT_FOLLOW_SYSTEM)
        }
    }
}
