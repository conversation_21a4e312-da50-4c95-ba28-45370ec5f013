package com.atom.diesnookerapp.ui.ergebniserfassung

data class ExerciseHistoryItem(
    val exerciseId: String,
    val exerciseTitle: String,
    val category: String,
    val attempts: List<Attempt>,
    val isHeader: Boolean = false,
    var isExpanded: Boolean = false
)

data class Attempt(
    val number: Int,
    val score: Int?,
    val timeInMinutes: Int?,
    val spelt: String? = null,
    val isCompletionMarker: Boolean = false
)