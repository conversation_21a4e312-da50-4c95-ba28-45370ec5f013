package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class QuestionsFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_questions, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val items = listOf(
            AssessmentItem("questions_before_training", "Vor Training"),
            AssessmentItem("questions_after_training", "Nach Training"),
            AssessmentItem("questions_before_tournament", "Vor Turnier"),
            AssessmentItem("questions_after_tournament", "Nach Turnier")
        )

        val adapter = AssessmentAdapter(items) { item ->
            when (item.id) {
                "questions_before_training" -> findNavController().navigate(R.id.questionsBeforeTrainingFragment)
                "questions_after_training" -> findNavController().navigate(R.id.questionsAfterTrainingFragment)
                "questions_before_tournament" -> findNavController().navigate(R.id.questionsBeforeTournamentFragment)
                "questions_after_tournament" -> findNavController().navigate(R.id.questionsAfterTournamentFragment)
            }
        }

        recyclerView.adapter = adapter
    }
}