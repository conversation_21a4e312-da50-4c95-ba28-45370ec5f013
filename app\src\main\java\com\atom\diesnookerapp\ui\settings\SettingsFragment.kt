package com.atom.diesnookerapp.ui.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ProgressBar
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.SyncManager
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SettingsFragment : Fragment() {

    private lateinit var emailTextView: TextView
    private lateinit var syncStatusTextView: TextView
    private lateinit var lastSyncTextView: TextView
    private lateinit var loginButton: Button
    private lateinit var logoutButton: Button
    private lateinit var syncNowButton: Button
    private lateinit var openWebButton: Button
    private lateinit var userManagementButton: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var themeRadioGroup: RadioGroup
    private lateinit var lightThemeRadioButton: RadioButton
    private lateinit var darkThemeRadioButton: RadioButton
    private lateinit var systemThemeRadioButton: RadioButton
    private lateinit var snookerThemeRadioButton: RadioButton
    private lateinit var blueThemeRadioButton: RadioButton
    private lateinit var darkBlueThemeRadioButton: RadioButton
    private lateinit var oceanThemeRadioButton: RadioButton
    private lateinit var crimsonThemeRadioButton: RadioButton
    private lateinit var neonThemeRadioButton: RadioButton

    private val authManager = FirebaseAuthManager()
    private lateinit var syncManager: SyncManager
    private lateinit var themePreferences: ThemePreferences

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        syncManager = SyncManager(requireContext())
        themePreferences = ThemePreferences(requireContext())

        // Initialize views
        emailTextView = view.findViewById(R.id.emailTextView)
        syncStatusTextView = view.findViewById(R.id.syncStatusTextView)
        lastSyncTextView = view.findViewById(R.id.lastSyncTextView)
        loginButton = view.findViewById(R.id.loginButton)
        logoutButton = view.findViewById(R.id.logoutButton)
        syncNowButton = view.findViewById(R.id.syncNowButton)
        openWebButton = view.findViewById(R.id.openWebButton)
        userManagementButton = view.findViewById(R.id.userManagementButton)
        progressBar = view.findViewById(R.id.progressBar)

        // Initialize theme views
        themeRadioGroup = view.findViewById(R.id.themeRadioGroup)
        lightThemeRadioButton = view.findViewById(R.id.lightThemeRadioButton)
        darkThemeRadioButton = view.findViewById(R.id.darkThemeRadioButton)
        systemThemeRadioButton = view.findViewById(R.id.systemThemeRadioButton)
        snookerThemeRadioButton = view.findViewById(R.id.snookerThemeRadioButton)
        blueThemeRadioButton = view.findViewById(R.id.blueThemeRadioButton)
        darkBlueThemeRadioButton = view.findViewById(R.id.darkBlueThemeRadioButton)
        oceanThemeRadioButton = view.findViewById(R.id.oceanThemeRadioButton)
        crimsonThemeRadioButton = view.findViewById(R.id.crimsonThemeRadioButton)
        neonThemeRadioButton = view.findViewById(R.id.neonThemeRadioButton)

        // Set up UI
        updateUI()
        updateThemeUI()

        // Set up click listeners
        loginButton.setOnClickListener {
            findNavController().navigate(R.id.action_settings_to_login)
        }

        logoutButton.setOnClickListener {
            logout()
        }

        syncNowButton.setOnClickListener {
            performSync()
        }

        openWebButton.setOnClickListener {
            openWebsite()
        }

        userManagementButton.setOnClickListener {
            findNavController().navigate(R.id.action_settings_to_userManagement)
        }

        // Set up theme selection listener
        themeRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.lightThemeRadioButton -> {
                    setAppTheme(ThemePreferences.THEME_LIGHT)
                }
                R.id.darkThemeRadioButton -> {
                    setAppTheme(ThemePreferences.THEME_DARK)
                }
                R.id.systemThemeRadioButton -> {
                    setAppTheme(ThemePreferences.THEME_SYSTEM)
                }
                R.id.snookerThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_SNOOKER)
                }
                R.id.blueThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_BLUE)
                }
                R.id.darkBlueThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_DARK_BLUE)
                }
                R.id.oceanThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_OCEAN)
                }
                R.id.crimsonThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_CRIMSON)
                }
                R.id.neonThemeRadioButton -> {
                    setCustomTheme(ThemePreferences.THEME_NEON)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        updateUI()
    }

    private fun updateUI() {
        val isLoggedIn = authManager.isLoggedIn()

        // Update email
        emailTextView.text = authManager.getCurrentUser()?.email ?: "Nicht angemeldet"

        // Update login/logout buttons
        loginButton.visibility = if (isLoggedIn) View.GONE else View.VISIBLE
        logoutButton.visibility = if (isLoggedIn) View.VISIBLE else View.GONE

        // Update user management button
        userManagementButton.isEnabled = isLoggedIn

        // Update sync status
        syncStatusTextView.text = if (isLoggedIn) "Aktiv" else "Inaktiv"
        syncNowButton.isEnabled = isLoggedIn

        // Update last sync time
        val lastSyncTime = getLastSyncTime()
        lastSyncTextView.text = if (lastSyncTime > 0) {
            "Letzte Synchronisierung: ${formatDate(lastSyncTime)}"
        } else {
            "Letzte Synchronisierung: Nie"
        }
    }

    private fun performSync() {
        if (!authManager.isLoggedIn()) {
            Toast.makeText(
                requireContext(),
                "Du musst angemeldet sein, um zu synchronisieren",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        setLoading(true)

        lifecycleScope.launch {
            val result = syncManager.performFullSync()

            if (result.isSuccess) {
                val syncResult = result.getOrThrow()

                // Save sync time
                saveLastSyncTime(System.currentTimeMillis())

                // Create a message that includes cleanup information
                val message = if (syncResult.totalCleanupCount > 0) {
                    "Synchronisierung erfolgreich: ${syncResult.totalUploaded} hochgeladen, " +
                    "${syncResult.totalDownloaded} heruntergeladen, " +
                    "${syncResult.totalCleanupCount} Duplikate entfernt"
                } else {
                    "Synchronisierung erfolgreich: ${syncResult.totalUploaded} hochgeladen, " +
                    "${syncResult.totalDownloaded} heruntergeladen"
                }

                Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Synchronisierung fehlgeschlagen: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
            }

            updateUI()
            setLoading(false)
        }
    }

    private fun logout() {
        // Cancel sync work
        syncManager.cancelSyncWork()

        // Logout
        authManager.logout()

        // Update UI
        updateUI()

        Toast.makeText(
            requireContext(),
            "Du wurdest abgemeldet",
            Toast.LENGTH_SHORT
        ).show()
    }

    private fun openWebsite() {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://die-snooker-app.web.app"))
        startActivity(intent)
    }

    private fun setLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        syncNowButton.isEnabled = !isLoading && authManager.isLoggedIn()
        loginButton.isEnabled = !isLoading
        logoutButton.isEnabled = !isLoading
        openWebButton.isEnabled = !isLoading
        userManagementButton.isEnabled = !isLoading && authManager.isLoggedIn()
    }

    private fun saveLastSyncTime(time: Long) {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        prefs.edit().putLong("last_sync_time", time).apply()
    }

    private fun getLastSyncTime(): Long {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        return prefs.getLong("last_sync_time", 0)
    }

    private fun updateThemeUI() {
        val themeMode = themePreferences.getThemeMode()

        // First clear all selections
        themeRadioGroup.clearCheck()

        // Then set the appropriate selection
        when (themeMode) {
            ThemePreferences.THEME_LIGHT -> lightThemeRadioButton.isChecked = true
            ThemePreferences.THEME_DARK -> darkThemeRadioButton.isChecked = true
            ThemePreferences.THEME_SYSTEM -> systemThemeRadioButton.isChecked = true
            ThemePreferences.THEME_SNOOKER -> snookerThemeRadioButton.isChecked = true
            ThemePreferences.THEME_BLUE -> blueThemeRadioButton.isChecked = true
            ThemePreferences.THEME_DARK_BLUE -> darkBlueThemeRadioButton.isChecked = true
            ThemePreferences.THEME_OCEAN -> oceanThemeRadioButton.isChecked = true
            ThemePreferences.THEME_CRIMSON -> crimsonThemeRadioButton.isChecked = true
            ThemePreferences.THEME_NEON -> neonThemeRadioButton.isChecked = true
        }
    }

    private fun setAppTheme(themeMode: Int) {
        // Save the theme mode
        themePreferences.saveThemeMode(themeMode)

        // Reset any custom theme flag
        themePreferences.saveCustomTheme(ThemePreferences.THEME_SYSTEM)

        // Apply the system theme mode
        AppCompatDelegate.setDefaultNightMode(themeMode)

        // Recreate the activity to ensure theme is fully applied
        requireActivity().recreate()
    }

    private fun setCustomTheme(customTheme: Int) {
        // Save the theme mode and custom theme
        themePreferences.saveThemeMode(customTheme)
        themePreferences.saveCustomTheme(customTheme)

        // Reset any night mode settings to default
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)

        // Apply the custom theme by recreating the activity
        requireActivity().recreate()
    }

    private fun formatDate(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("dd.MM.yyyy HH:mm", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }
}
