<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical">

    <CheckBox
        android:id="@+id/checkBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp" />

    <View
        android:id="@+id/colorIndicator"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="8dp" />

    <TextView
        android:id="@+id/scoreName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:layout_marginStart="8dp" />
</LinearLayout> 