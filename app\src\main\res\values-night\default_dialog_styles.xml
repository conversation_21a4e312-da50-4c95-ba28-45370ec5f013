<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Snooker theme dialog style with default animations for night mode -->
    <style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style>

    <!-- Blue theme dialog style with default animations for night mode -->
    <style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style>

    <!-- Dark Blue theme dialog style with default animations for night mode -->
    <style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style>

    <!-- Dark theme dialog style with default animations for night mode -->
    <style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>
</resources>
