package com.atom.diesnookerapp.ui.trainingsplan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class TrainingsplanHistoryFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var historyAdapter: TrainingsplanHistoryAdapter
    private lateinit var recyclerView: RecyclerView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_trainingsplan_history, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())

        recyclerView = view.findViewById(R.id.trainingsplanHistoryRecyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        historyAdapter = TrainingsplanHistoryAdapter(emptyList())
        recyclerView.adapter = historyAdapter

        updateUI()
    }

    private fun updateUI() {
        val history = trainingsplanManager.getTrainingsplanHistory()
        // Sort the history by date (newest first) to ensure newest entries are at the top
        val sortedHistory = history.sortedByDescending { it.weekStartDate }
        historyAdapter.updateData(sortedHistory)
    }
}
