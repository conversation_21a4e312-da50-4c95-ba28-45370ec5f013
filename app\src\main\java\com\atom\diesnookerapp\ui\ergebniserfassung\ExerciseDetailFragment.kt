package com.atom.diesnookerapp.ui.ergebniserfassung

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanErgebniserfassungConnector
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.google.android.material.button.MaterialButton
import org.threeten.bp.LocalDateTime
import androidx.navigation.fragment.findNavController

class ExerciseDetailFragment : Fragment() {
    private var exerciseId: String? = null
    private var exerciseTitle: String? = null
    private var exerciseType: String? = null
    private lateinit var exercisePreferences: ExercisePreferences
    private lateinit var trainingsplanConnector: TrainingsplanErgebniserfassungConnector
    private lateinit var scoresText: TextView
    private lateinit var totalTimeText : TextView
    private var isSplitsExercise = false
    private var isStellungsspielExercise = false
    private var isTimeOnlyExercise = false

    companion object {
        val TIME_ONLY_EXERCISES = listOf(
            "black_routines",
            "brown_to_reds",
            "bc_to_reds",
            "pj_routines",
            "random_bilder",
            "long_shots",
            "medium_to_blue",
            "rest",
            "snooker_legen",
            "black_to_bc",
            "blue_pink_black",
            "langer_stoss",
            "gerader_stoss_2_kreiden"
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            exerciseId = it.getString("exerciseId")
            exerciseTitle = it.getString("exerciseTitle")
            exerciseType = it.getString("exerciseType")

            // Use exercise type if provided, otherwise fall back to hardcoded lists for backward compatibility
            when (exerciseType) {
                "splits" -> {
                    isSplitsExercise = true
                    isStellungsspielExercise = false
                    isTimeOnlyExercise = false
                }
                "stellungsspiel" -> {
                    isSplitsExercise = false
                    isStellungsspielExercise = true
                    isTimeOnlyExercise = false
                }
                "timeonly" -> {
                    isSplitsExercise = false
                    isStellungsspielExercise = false
                    isTimeOnlyExercise = true
                }
                "normal" -> {
                    isSplitsExercise = false
                    isStellungsspielExercise = false
                    isTimeOnlyExercise = false
                }
                else -> {
                    // Fallback to old hardcoded logic for backward compatibility
                    isSplitsExercise = exerciseId in listOf("yellow_splits", "green", "brown", "blue", "black")
                    isStellungsspielExercise = exerciseId in listOf("stellungsspiel_gelb", "stellungsspiel_gruen", "stellungsspiel_braun", "hohe_schwarze")
                    isTimeOnlyExercise = exerciseId in TIME_ONLY_EXERCISES
                }
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return when {
            isSplitsExercise -> {
                inflater.inflate(R.layout.fragment_exercise_detail_splits, container, false)
            }
            isStellungsspielExercise -> {
                inflater.inflate(R.layout.fragment_exercise_detail_stellungsspiel, container, false)
            }
            isTimeOnlyExercise -> {
                inflater.inflate(R.layout.fragment_exercise_detail_timeonly, container, false)
            }
            else -> {
                inflater.inflate(R.layout.fragment_exercise_detail, container, false)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        exercisePreferences = ExercisePreferences(requireContext())
        trainingsplanConnector = TrainingsplanErgebniserfassungConnector(requireContext())

        view.findViewById<TextView>(R.id.titleText).text = exerciseTitle

        when {
            isTimeOnlyExercise -> {
                setupTimeOnlyView(view)
            }
            isSplitsExercise -> {
                setupSplitsView(view)
            }
            isStellungsspielExercise -> {
                setupStellungsspielView(view)
            }
            else -> {
                setupNormalView(view)
            }
        }
    }

    //Add this method
    private fun navigateToGraphFragment() {
        val bundle = Bundle().apply {
            putString("exerciseId", exerciseId)
        }
        findNavController().navigate(R.id.action_exerciseDetailFragment_to_exerciseGraphFragment, bundle)
    }

    private fun setupSplitsView(view: View) {
        // Setup click listeners for each spelt
        view.findViewById<MaterialButton>(R.id.addScore3Button).setOnClickListener {
            showAddScoreDialog("3")
        }
        view.findViewById<MaterialButton>(R.id.addScore6Button).setOnClickListener {
            showAddScoreDialog("6")
        }
        view.findViewById<MaterialButton>(R.id.addScore10Button).setOnClickListener {
            showAddScoreDialog("10")
        }

        // Add click listener for time button
        view.findViewById<MaterialButton>(R.id.addTimeButton).setOnClickListener {
            showAddTimeDialog()
        }

        // Setup graph button
        view.findViewById<MaterialButton>(R.id.showGraphButton).setOnClickListener {
            navigateToGraphFragment()
        }

        updateSplitsStats()
    }

    private fun setupTimeOnlyView(view: View) {
        view.findViewById<MaterialButton>(R.id.addTimeButton).setOnClickListener {
            showAddTimeDialog()
        }
        scoresText = view.findViewById(R.id.lastTimeText)
        totalTimeText = view.findViewById(R.id.totalTimeText)
        updateTimeStats()
    }

    private fun setupNormalView(view: View) {
        scoresText = view.findViewById(R.id.lastScoreText)
        updateStats()

        view.findViewById<MaterialButton>(R.id.addScoreButton).setOnClickListener {
            showAddScoreDialog()
        }
        view.findViewById<MaterialButton>(R.id.addTimeButton).setOnClickListener {
            showAddTimeDialog()
        }

        // Setup graph button
        view.findViewById<MaterialButton>(R.id.showGraphButton).setOnClickListener {
            navigateToGraphFragment()
        }
    }

    private fun updateTimeStats() {
        exerciseId?.let { id ->
            val today = LocalDateTime.now().toLocalDate()
            val startOfDay = today.atStartOfDay()
            val endOfDay = today.plusDays(1).atStartOfDay()

            val todaysTimeRecords = exercisePreferences.getRecords()
                .filter { it.exerciseId == id }
                .filter { record ->
                    record.timestamp.isAfter(startOfDay) &&
                            record.timestamp.isBefore(endOfDay)
                }
                .filter { it.timeInMinutes != null }

            var totalTime = 0

            todaysTimeRecords.forEach { record ->
                val time = record.timeInMinutes ?: 0
                totalTime += time
            }

            if (todaysTimeRecords.isEmpty()) {
                scoresText.text = "Noch keine Zeitangaben heute"
                totalTimeText.visibility = View.GONE
            } else {
                totalTimeText.visibility = View.VISIBLE
                scoresText.text = "Zeitangaben für heute:"
                totalTimeText.text = "Gesamtzeit: ${totalTime} Minuten"
            }
        }
    }

    private fun updateStats() {
        exerciseId?.let { id ->
            val today = LocalDateTime.now().toLocalDate()
            val startOfDay = today.atStartOfDay()
            val endOfDay = today.plusDays(1).atStartOfDay()

            val todaysScores = exercisePreferences.getRecords()
                .filter { it.exerciseId == id }
                .filter { record ->
                    record.timestamp.isAfter(startOfDay) &&
                            record.timestamp.isBefore(endOfDay)
                }
                .filter { it.score != null }
                .sortedBy { it.timestamp }

            val scoreBuilder = StringBuilder()
            todaysScores.forEachIndexed { index, record ->
                scoreBuilder.append("Aufnahme ${index + 1}: ${record.score}\n")
            }

            if (scoreBuilder.isEmpty()) {
                scoresText.text = "Noch keine Ergebnisse heute"
            } else {
                scoresText.text = scoreBuilder.toString().trimEnd()
            }
        }
    }

    private fun showAddScoreDialog(spelt: String? = null) {
        val input = EditText(requireContext())
        input.hint = "Ergebnis"
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER

        // Use ThemeHelper to create a themed AlertDialog
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle("Ergebnis eintragen" + (spelt?.let { " ($it)" } ?: ""))
            .setView(input)
            .setPositiveButton("Speichern") { _, _ ->
                val score = input.text.toString().toIntOrNull()
                if (score != null) {
                    saveScore(score, spelt)
                } else {
                    Toast.makeText(requireContext(), "Ungültige Eingabe", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun showAddTimeDialog() {
        val input = EditText(requireContext())
        input.hint = "Zeit in Minuten"
        input.inputType = android.text.InputType.TYPE_CLASS_NUMBER

        // Use ThemeHelper to create a themed AlertDialog
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle("Trainingszeit hinzufügen")
            .setView(input)
            .setPositiveButton("Speichern") { _, _ ->
                val time = input.text.toString().toIntOrNull()
                if (time != null) {
                    saveTime(time)
                } else {
                    Toast.makeText(requireContext(), "Ungültige Eingabe", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun saveScore(score: Int, spelt: String? = null) {
        exerciseId?.let { id ->
            val record = ExerciseRecord(
                exerciseId = id,
                exerciseName = exerciseTitle ?: "", // Use exerciseTitle, provide default if null
                timestamp = LocalDateTime.now(),
                score = score,
                spelt = spelt
            )
            exercisePreferences.saveRecord(record)
            when {
                isSplitsExercise -> {
                    updateSplitsStats()
                }
                isStellungsspielExercise -> {
                    updateStellungsspielStats()
                }
                else -> {
                    updateStats()
                }
            }
            Toast.makeText(requireContext(), "Ergebnis gespeichert", Toast.LENGTH_SHORT).show()
        }
    }

    private fun saveTime(timeInMinutes: Int) {
        exerciseId?.let { id ->
            val today = LocalDateTime.now().toLocalDate()
            val startOfDay = today.atStartOfDay()
            val endOfDay = today.plusDays(1).atStartOfDay()

            // Find existing time record for today
            val existingRecord = exercisePreferences.getRecords()
                .filter { it.exerciseId == id }
                .filter { record ->
                    record.timestamp.isAfter(startOfDay) &&
                            record.timestamp.isBefore(endOfDay)
                }
                .firstOrNull { it.timeInMinutes != null }

            if (existingRecord != null) {
                // Update existing record with accumulated time
                val updatedRecord = existingRecord.copy(
                    timeInMinutes = (existingRecord.timeInMinutes ?: 0) + timeInMinutes
                )
                exercisePreferences.updateRecord(existingRecord, updatedRecord)
            } else {
                // Create new record
                val record = ExerciseRecord(
                    exerciseId = id,
                    exerciseName = exerciseTitle ?: "", // Use exerciseTitle, provide default if null
                    timestamp = LocalDateTime.now(),
                    timeInMinutes = timeInMinutes
                )
                exercisePreferences.saveRecord(record)
            }

            // Mark exercise as completed in Trainingsplan
            trainingsplanConnector.markExerciseCompletedFromErgebniserfassung(id)

            // Update the appropriate view based on exercise type
            when {
                isSplitsExercise -> {
                    updateSplitsStats()
                }
                isStellungsspielExercise -> {
                    updateStellungsspielStats()
                }
                isTimeOnlyExercise -> {
                    updateTimeStats()
                }
                else -> {
                    updateStats()
                }
            }
            Toast.makeText(requireContext(), "Zeit gespeichert", Toast.LENGTH_SHORT).show()
        }
    }

    private fun updateSplitsStats() {
        exerciseId?.let { id ->
            val today = LocalDateTime.now().toLocalDate()
            val startOfDay = today.atStartOfDay()
            val endOfDay = today.plusDays(1).atStartOfDay()

            val todaysScores = exercisePreferences.getRecords()
                .filter { it.exerciseId == id }
                .filter { record ->
                    record.timestamp.isAfter(startOfDay) &&
                            record.timestamp.isBefore(endOfDay)
                }
                .filter { it.score != null }
                .groupBy { it.spelt }

            // Update UI for each spelt
            updateSpeltScores(view?.findViewById(R.id.scores3Text), todaysScores["3"])
            updateSpeltScores(view?.findViewById(R.id.scores6Text), todaysScores["6"])
            updateSpeltScores(view?.findViewById(R.id.scores10Text), todaysScores["10"])
        }
    }

    private fun updateSpeltScores(textView: TextView?, records: List<ExerciseRecord>?) {
        if (isStellungsspielExercise) {
            textView?.text = records?.sortedBy { it.timestamp }
                ?.mapIndexed { index, record ->
                    val result = if (record.score == 1) "     +" else "     -"
                    "$result"
                }
                ?.joinToString("\n")
                ?: "Noch keine Ergebnisse"
        } else {
            textView?.text = records?.sortedBy { it.timestamp }
                ?.mapIndexed { index, record ->
                    "Aufnahme ${index + 1}: ${record.score}"
                }
                ?.joinToString("\n")
                ?: "Noch keine Ergebnisse"
        }
    }

    // Track current column and attempts for Stellungsspiel
    private var currentColumn = 1
    private var totalAttempts = 0
    private var cycleCount = 0
    private val maxCycles = 3
    private val maxColumns = 6

    private fun setupStellungsspielView(view: View) {
        // Setup success and miss buttons
        view.findViewById<MaterialButton>(R.id.successButton).setOnClickListener {
            handleStellungsspielAttempt(true)
        }
        view.findViewById<MaterialButton>(R.id.missButton).setOnClickListener {
            handleStellungsspielAttempt(false)
        }

        // Add click listener for time button
        view.findViewById<MaterialButton>(R.id.addTimeButton).setOnClickListener {
            showAddTimeDialog()
        }

        // Setup graph button
        view.findViewById<MaterialButton>(R.id.showGraphButton).setOnClickListener {
            navigateToGraphFragment()
        }

        // Load the current state from SharedPreferences
        loadStellungsspielState()

        // Update the UI with the current state
        updateStellungsspielStats()
    }

    private fun handleStellungsspielAttempt(isSuccess: Boolean) {
        // Save the score (1 for success, 0 for miss)
        val score = if (isSuccess) 1 else 0
        saveScore(score, currentColumn.toString())

        totalAttempts++

        // Always move to the next column after one attempt
        currentColumn++

        // If we've gone through all columns, increment cycle count and start again at column 1
        if (currentColumn > maxColumns) {
            currentColumn = 1
            cycleCount++

            // If we've completed all cycles, reset everything
            if (cycleCount >= maxCycles) {
                // Exercise is complete - all columns have 3 values each
                Toast.makeText(requireContext(), "Alle Spalten vollständig! Übung abgeschlossen.", Toast.LENGTH_LONG).show()

                // Add a completion marker record
                addCompletionMarker()

                // Reset counters
                cycleCount = 0
                totalAttempts = 0

                // Clear the UI but keep the history
                clearStellungsspielUI()
            }
        }

        // Save the current state
        saveStellungsspielState()

        updateCurrentColumnText()
    }

    private fun clearStellungsspielUI() {
        // Clear all the text views but keep the data in history
        view?.findViewById<TextView>(R.id.scores1Text)?.text = "Noch keine Ergebnisse"
        view?.findViewById<TextView>(R.id.scores2Text)?.text = "Noch keine Ergebnisse"
        view?.findViewById<TextView>(R.id.scores3Text)?.text = "Noch keine Ergebnisse"
        view?.findViewById<TextView>(R.id.scores4Text)?.text = "Noch keine Ergebnisse"
        view?.findViewById<TextView>(R.id.scores5Text)?.text = "Noch keine Ergebnisse"
        view?.findViewById<TextView>(R.id.scores6Text)?.text = "Noch keine Ergebnisse"

        // Force a refresh of the UI after a short delay to ensure everything is cleared
        Handler(Looper.getMainLooper()).postDelayed({
            updateStellungsspielStats()
        }, 100)
    }

    private fun addCompletionMarker() {
        exerciseId?.let { id ->
            // Create a special record that marks the completion of an exercise
            val completionRecord = ExerciseRecord(
                exerciseId = id,
                exerciseName = exerciseTitle ?: "", // Add this line
                timestamp = LocalDateTime.now(),
                score = -1, // Use a special score to identify completion markers
                isCompletionMarker = true
            )
            exercisePreferences.saveRecord(completionRecord)

            // Clear the saved state since we're starting a new exercise
            val prefs = requireContext().getSharedPreferences("stellungsspiel_$id", Context.MODE_PRIVATE)
            prefs.edit().clear().apply()

            // Log the marker creation for debugging
            Log.d("StellungsspielFragment", "Added completion marker for exercise $id at ${completionRecord.timestamp}")
            Log.d("StellungsspielFragment", "Cleared saved state for exercise $id")
        }
    }

    private fun updateCurrentColumnText() {
        view?.findViewById<TextView>(R.id.currentColumnText)?.text = "Aktuelle Spalte: $currentColumn (Durchgang ${cycleCount + 1} von $maxCycles)"
    }

    private fun loadStellungsspielState() {
        exerciseId?.let { id ->
            // Get SharedPreferences for this exercise
            val prefs = requireContext().getSharedPreferences("stellungsspiel_$id", Context.MODE_PRIVATE)

            // Load the current column and cycle count
            currentColumn = prefs.getInt("current_column", 1)
            cycleCount = prefs.getInt("cycle_count", 0)
            totalAttempts = prefs.getInt("total_attempts", 0)

            Log.d("StellungsspielFragment", "Loaded state: column=$currentColumn, cycle=${cycleCount + 1}, attempts=$totalAttempts")
        }
    }

    private fun saveStellungsspielState() {
        exerciseId?.let { id ->
            // Get SharedPreferences for this exercise
            val prefs = requireContext().getSharedPreferences("stellungsspiel_$id", Context.MODE_PRIVATE)

            // Save the current column and cycle count
            prefs.edit()
                .putInt("current_column", currentColumn)
                .putInt("cycle_count", cycleCount)
                .putInt("total_attempts", totalAttempts)
                .apply()

            Log.d("StellungsspielFragment", "Saved state: column=$currentColumn, cycle=${cycleCount + 1}, attempts=$totalAttempts")
        }
    }

    private fun updateStellungsspielStats() {
        exerciseId?.let { id ->
            val today = LocalDateTime.now().toLocalDate()
            val startOfDay = today.atStartOfDay()
            val endOfDay = today.plusDays(1).atStartOfDay()

            // Get all records for today
            val allTodaysRecords = exercisePreferences.getRecords()
                .filter { it.exerciseId == id }
                .filter { record ->
                    record.timestamp.isAfter(startOfDay) &&
                            record.timestamp.isBefore(endOfDay)
                }
                .sortedBy { it.timestamp }

            // Find the most recent completion marker
            val lastCompletionMarker = allTodaysRecords
                .filter { it.isCompletionMarker || it.score == -1 } // Check both flags for backward compatibility
                .maxByOrNull { it.timestamp }

            // Log the completion marker for debugging
            if (lastCompletionMarker != null) {
                Log.d("StellungsspielFragment", "Found completion marker at ${lastCompletionMarker.timestamp}")
            } else {
                Log.d("StellungsspielFragment", "No completion marker found")
            }

            // Filter records to only include those after the last completion marker
            val activeRecords = if (lastCompletionMarker != null) {
                allTodaysRecords.filter { it.timestamp.isAfter(lastCompletionMarker.timestamp) }
            } else {
                allTodaysRecords
            }

            // Calculate overall accuracy for all attempts combined
            val allAttempts = activeRecords.filter { it.score != null && !it.isCompletionMarker }
            if (allAttempts.isNotEmpty()) {
                val successCount = allAttempts.count { it.score == 1 }
                val totalCount = allAttempts.size
                val overallSuccessRate = (successCount.toFloat() / totalCount) * 100

                // Display overall accuracy at the top of the screen
                view?.findViewById<TextView>(R.id.titleText)?.let { titleText ->
                    val currentTitle = titleText.text
                    if (!currentTitle.contains("Gesamtgenauigkeit")) {
                        titleText.text = "$currentTitle\nGesamtgenauigkeit: ${String.format("%.1f", overallSuccessRate)}%"
                    }
                }
            }

            // Group active records by column
            val activeScores = activeRecords
                .filter { it.score != null && !it.isCompletionMarker }
                .groupBy { it.spelt }

            // Get the most recent records for each column (up to 3 per column)
            val latestScores = mutableMapOf<String, List<ExerciseRecord>>()
            for (column in 1..6) {
                val columnKey = column.toString()
                val columnScores = activeScores[columnKey] ?: emptyList()
                latestScores[columnKey] = columnScores.sortedBy { it.timestamp }
            }

            // Update UI for each column
            updateSpeltScores(view?.findViewById(R.id.scores1Text), latestScores["1"])
            updateSpeltScores(view?.findViewById(R.id.scores2Text), latestScores["2"])
            updateSpeltScores(view?.findViewById(R.id.scores3Text), latestScores["3"])
            updateSpeltScores(view?.findViewById(R.id.scores4Text), latestScores["4"])
            updateSpeltScores(view?.findViewById(R.id.scores5Text), latestScores["5"])
            updateSpeltScores(view?.findViewById(R.id.scores6Text), latestScores["6"])

            // Calculate and display success rate for each column
            for (column in 1..6) {
                val columnScores = latestScores[column.toString()] ?: emptyList()
                if (columnScores.isNotEmpty()) {
                    val successCount = columnScores.count { it.score == 1 }
                    val totalCount = columnScores.size
                    val successRate = if (totalCount > 0) (successCount.toFloat() / totalCount) * 100 else 0f

                    val textView = when (column) {
                        1 -> view?.findViewById<TextView>(R.id.scores1Text)
                        2 -> view?.findViewById<TextView>(R.id.scores2Text)
                        3 -> view?.findViewById<TextView>(R.id.scores3Text)
                        4 -> view?.findViewById<TextView>(R.id.scores4Text)
                        5 -> view?.findViewById<TextView>(R.id.scores5Text)
                        6 -> view?.findViewById<TextView>(R.id.scores6Text)
                        else -> null
                    }

                    val currentText = textView?.text?.toString() ?: ""
                    /*if (currentText.isNotEmpty() && !currentText.contains("Erfolgsrate")) {
                        textView?.text = "$currentText\n\nErfolgsrate: ${String.format("%.1f", successRate)}%"
                    }*/
                }
            }

            // Only update the totalAttempts count based on active records
            totalAttempts = activeRecords.count { !it.isCompletionMarker && it.score != null }

            // Update the current column text with the updated cycle count
            updateCurrentColumnText()
        }
    }
}
