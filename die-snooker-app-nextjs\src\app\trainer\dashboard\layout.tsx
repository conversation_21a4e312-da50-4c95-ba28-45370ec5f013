"use client";

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { useAuth } from '@/context/AuthContext';
import LoadingSpinner from '@/components/ui/LoadingSpinner'; // Assuming this exists

interface NavLinkProps {
  href: string;
  children: ReactNode;
}

const NavLink = ({ href, children }: NavLinkProps) => {
  const pathname = usePathname();
  // Adjust isActive for nested routes, e.g. /trainer/dashboard/athlete/[id] should highlight /trainer/dashboard/athletes
  const baseHref = href.split('/')[3]; // e.g. 'athletes' from '/trainer/dashboard/athletes'
  const currentBase = pathname.split('/')[3];
  const isActive = baseHref ? currentBase === baseHref : pathname === href;

  return (
    <Link href={href} className={`block px-4 py-2 rounded-md text-sm font-medium transition-colors
      ${isActive ? 'bg-purple-700 text-white' : 'text-purple-100 hover:bg-purple-600 hover:text-white'}`}>
      {children}
    </Link>
  );
};

export default function TrainerDashboardLayout({ children }: { children: ReactNode }) {
  const { currentUser, userProfile, loading: authLoading } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await auth.signOut();
      router.push('/trainer/login');
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // This layout is protected by withAuth at the page level.
  // However, we add a check here for robustness or if direct access to layout occurs.
  if (authLoading) {
    return <LoadingSpinner />;
  }

  if (!currentUser || !userProfile || (userProfile.role !== 'TRAINER' && userProfile.role !== 'MENTAL_TRAINER')) {
    // This should ideally be caught by withAuth on the page.
    // If reached, redirect or show access denied.
    // For simplicity, relying on page-level HOC to redirect.
    // router.replace('/trainer/login'); // Could cause redirect loops if not careful
    return <div className="min-h-screen flex items-center justify-center"><p>Access Denied or Loading...</p></div>;
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-gray-100">
      {/* Sidebar: Ensured flex flex-col for proper structure */}
      <aside className="w-full md:w-64 bg-purple-800 text-white flex-shrink-0 flex flex-col">
        <div className="p-5 border-b border-purple-700">
          <h1 className="text-2xl font-semibold">Trainer Dashboard</h1>
          {userProfile && (
            <div className="mt-2 text-sm text-purple-300">
              <p>{userProfile.displayName || currentUser?.email}</p>
              <p>Role: {userProfile.role}</p>
            </div>
          )}
        </div>
        {/* Navigation: Ensured flex-grow to push logout to bottom */}
        <nav className="p-3 space-y-1 flex-grow">
          <NavLink href="/trainer/dashboard">Home</NavLink>
          <NavLink href="/trainer/dashboard/athletes">Athletes</NavLink>
          <NavLink href="/trainer/dashboard/requests">Connection Requests</NavLink>
          <NavLink href="/trainer/dashboard/profile">My Profile</NavLink>
          {/* Add more trainer-specific links here */}
        </nav>
        {/* Logout Button Container: Removed mt-auto */}
        <div className="p-5 border-t border-purple-700">
          <button
            onClick={handleLogout}
            className="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm transition-colors"
          >
            Logout
          </button>
        </div>
      </aside>
      <main className="flex-1 p-6 overflow-auto">
        {children}
      </main>
    </div>
  );
}
