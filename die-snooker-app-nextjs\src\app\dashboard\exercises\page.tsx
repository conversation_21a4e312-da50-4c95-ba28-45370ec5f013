"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import { collection, query, where, orderBy, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import LoadingSpinner from "@/components/ui/LoadingSpinner";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface UserExerciseDefinition {
  id: string; // This is the exercise's unique ID, also used as exerciseId in records
  userId: string;
  name: string;
  category: string;
  description?: string;
  exerciseType: string; // 'normal', 'timeonly', 'splits', 'stellungsspiel'
}

interface ExerciseRecord {
  id: string; // Document ID from Firestore
  userId: string;
  exerciseId: string; // Links to UserExerciseDefinition.id
  timestamp: Timestamp; // Firestore Timestamp
  score?: number;
  timeInMinutes?: number;
  spelt?: '3' | '6' | '10' | string; // For splits exercises
  // other potential fields like 'completed', 'notes', etc.
}

interface GroupedExercises {
  [category: string]: UserExerciseDefinition[];
}

interface ExerciseWithRecords extends UserExerciseDefinition {
  records: ExerciseRecord[];
}

function ExercisesPage() {
  const { currentUser } = useAuth();
  const [exerciseDefinitions, setExerciseDefinitions] = useState<UserExerciseDefinition[]>([]);
  const [exerciseRecords, setExerciseRecords] = useState<ExerciseRecord[]>([]);
  const [groupedExercisesWithRecords, setGroupedExercisesWithRecords] = useState<{ [category: string]: ExerciseWithRecords[] }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // 1. Fetch User Exercise Definitions
        const defsQuery = query(
          collection(db, "user_exercise_definitions"),
          where("userId", "==", currentUser.uid)
        );
        const defsSnapshot = await getDocs(defsQuery);
        const fetchedDefinitions = defsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as UserExerciseDefinition));
        setExerciseDefinitions(fetchedDefinitions);

        // 2. Fetch Exercise Records
        const recsQuery = query(
          collection(db, "exercise_records"),
          where("userId", "==", currentUser.uid),
          orderBy("timestamp", "desc")
        );
        const recsSnapshot = await getDocs(recsQuery);
        const fetchedRecords = recsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ExerciseRecord));
        setExerciseRecords(fetchedRecords);

        // 3. Combine and Group Data
        const exercisesWithRecs: ExerciseWithRecords[] = fetchedDefinitions.map(def => {
          const relevantRecords = fetchedRecords
            .filter(rec => rec.exerciseId === def.id)
            .sort((a, b) => {
              const timeA = a.timestamp && typeof a.timestamp.toMillis === 'function' ? a.timestamp.toMillis() : (typeof a.timestamp === 'number' ? a.timestamp : 0);
              const timeB = b.timestamp && typeof b.timestamp.toMillis === 'function' ? b.timestamp.toMillis() : (typeof b.timestamp === 'number' ? b.timestamp : 0);
              return timeB - timeA; // Sort descending (most recent first for display list)
            });
          return { ...def, records: relevantRecords };
        });

        const grouped: { [category: string]: ExerciseWithRecords[] } = {};
        exercisesWithRecs.forEach(ex => {
          if (!grouped[ex.category]) {
            grouped[ex.category] = [];
          }
          grouped[ex.category].push(ex);
        });
        setGroupedExercisesWithRecords(grouped);

      } catch (err: any) {
        console.error("Error fetching exercises data:", err);
        setError("Failed to load exercises data. " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentUser]);

  const formatDateTimestamp = (timestamp: any, options?: Intl.DateTimeFormatOptions): string => {
    const defaultOptions: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    const fmtOptions = options || defaultOptions;

    if (timestamp && typeof timestamp.toDate === 'function') { // Firestore Timestamp
      return new Date(timestamp.toDate()).toLocaleDateString('de-DE', fmtOptions);
    }
    if (typeof timestamp === 'number') { // Milliseconds epoch
      return new Date(timestamp).toLocaleDateString('de-DE', fmtOptions);
    }
    if (timestamp instanceof Date) { // JavaScript Date object
      return timestamp.toLocaleDateString('de-DE', fmtOptions);
    }
    return 'Invalid Date'; // Fallback
  };

  const getChartData = (records: ExerciseRecord[]) => {
    const filteredRecords = records
      .filter(r => typeof r.score === 'number')
      .sort((a,b) => { // sort by date ascending for chart
        const timeA = a.timestamp && typeof a.timestamp.toMillis === 'function' ? a.timestamp.toMillis() : (typeof a.timestamp === 'number' ? a.timestamp : 0);
        const timeB = b.timestamp && typeof b.timestamp.toMillis === 'function' ? b.timestamp.toMillis() : (typeof b.timestamp === 'number' ? b.timestamp : 0);
        return timeA - timeB;
      });
    return {
      labels: filteredRecords.map(r => formatDateTimestamp(r.timestamp, { year: 'numeric', month: '2-digit', day: '2-digit' })),
      datasets: [
        {
          label: 'Score',
          data: filteredRecords.map(r => r.score),
          fill: false,
          borderColor: 'rgb(238, 187, 195)', // #eebbc3
          tension: 0.1,
        },
      ],
    };
  };

  const chartOptions = (exerciseName: string) => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: `Score History: ${exerciseName}`,
      },
    },
    scales: {
        y: {
            beginAtZero: true,
            // max: can be dynamic based on max score if needed
        }
    }
  });

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Übungen</h1>
      {Object.keys(groupedExercisesWithRecords).length === 0 && !loading && (
        <p className="text-gray-600">No exercises defined yet. Please add some exercises.</p>
      )}
      {Object.entries(groupedExercisesWithRecords).map(([category, exercises]) => (
        <section key={category} className="mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-indigo-700 border-b-2 border-indigo-200 pb-2">{category}</h2>
          <div className="space-y-6">
            {exercises.map(exercise => (
              <details key={exercise.id} className="bg-white p-4 rounded-lg shadow-md">
                <summary className="font-medium text-lg cursor-pointer text-gray-800 hover:text-indigo-600">
                  {exercise.name}
                  <span className="text-sm text-gray-500 ml-2">({exercise.exerciseType}) - {exercise.records.length} record(s)</span>
                </summary>
                <div className="mt-4">
                  {exercise.description && <p className="text-sm text-gray-600 mb-3">{exercise.description}</p>}

                  {exercise.exerciseType === 'splits' ? (
                    <SplitsExerciseView records={exercise.records} formatDate={formatDateTimestamp} />
                  ) : (
                    <>
                      {/* Default View for non-'splits' exercises */}
                      {exercise.records.filter(r => typeof r.score === 'number').length > 1 ? (
                        <div className="h-64 mb-4">
                          <Line options={chartOptions(exercise.name)} data={getChartData(exercise.records)} />
                        </div>
                      ) : exercise.records.filter(r => typeof r.score === 'number').length > 0 ? (
                        <p className="text-sm text-gray-500 mb-3">Not enough score data for a trend chart.</p>
                      ) : null}

                      <h4 className="font-semibold text-md text-gray-700 mb-2">Recent Records:</h4>
                      {exercise.records.length > 0 ? (
                        <ul className="list-disc list-inside pl-4 space-y-1 text-sm">
                          {exercise.records.slice(0, 5).map(record => (
                            <li key={record.id} className="text-gray-600">
                              {formatDateTimestamp(record.timestamp)}:
                              {typeof record.score === 'number' && ` Score: ${record.score}`}
                              {typeof record.timeInMinutes === 'number' && ` Time: ${record.timeInMinutes} min`}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-gray-500">No records for this exercise yet.</p>
                      )}
                    </>
                  )}
                </div>
              </details>
            ))}
          </div>
        </section>
      ))}
    </div>
  );
}

// New component for displaying 'splits' exercises
const SplitsExerciseView = ({ records, formatDate }: { records: ExerciseRecord[], formatDate: (timestamp: any, options?: Intl.DateTimeFormatOptions) => string }) => {
  if (records.length === 0) {
    return <p className="text-sm text-gray-500">No records for this exercise yet.</p>;
  }

  // Group records by date string
  const groupedByDate = records.reduce((acc, record) => {
    const dateKey = formatDate(record.timestamp, { year: 'numeric', month: '2-digit', day: '2-digit' });
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(record);
    return acc;
  }, {} as Record<string, ExerciseRecord[]>);

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm text-left text-gray-500">
        <thead className="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" className="px-4 py-3">Date</th>
            <th scope="col" className="px-4 py-3 text-center">3er</th>
            <th scope="col" className="px-4 py-3 text-center">6er</th>
            <th scope="col" className="px-4 py-3 text-center">10er</th>
          </tr>
        </thead>
        <tbody>
          {Object.entries(groupedByDate).map(([date, dateRecords]) => {
            const score3er = dateRecords.find(r => r.spelt === '3')?.score;
            const score6er = dateRecords.find(r => r.spelt === '6')?.score;
            const score10er = dateRecords.find(r => r.spelt === '10')?.score;

            return (
              <tr key={date} className="bg-white border-b hover:bg-gray-50">
                <td className="px-4 py-3 font-medium text-gray-900 whitespace-nowrap">
                  {date}
                </td>
                <td className="px-4 py-3 text-center">
                  {typeof score3er === 'number' ? score3er : '-'}
                </td>
                <td className="px-4 py-3 text-center">
                  {typeof score6er === 'number' ? score6er : '-'}
                </td>
                <td className="px-4 py-3 text-center">
                  {typeof score10er === 'number' ? score10er : '-'}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default withAuth(ExercisesPage, { redirectTo: '/login' });
