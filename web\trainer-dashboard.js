// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY",
    authDomain: "die-snooker-app.firebaseapp.com",
    projectId: "die-snooker-app",
    storageBucket: "die-snooker-app.firebasestorage.app",
    messagingSenderId: "547283642216",
    appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
    measurementId: "G-GTFVZZ4LJ"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// DOM Elements
const loginContainer = document.getElementById('login-container');
const appContainer = document.getElementById('app-container');
const loginForm = document.getElementById('login-form');
const userEmail = document.getElementById('user-email');
const userRole = document.getElementById('user-role');
const logoutBtn = document.getElementById('logout-btn');
const navLinks = document.querySelectorAll('nav a');
const sections = document.querySelectorAll('section');
const trainerTypeDisplay = document.getElementById('trainer-type-display').querySelector('span');
const athletesList = document.getElementById('athletes-list');
const emptyAthletes = document.getElementById('empty-athletes');
const refreshBtn = document.getElementById('refresh-btn');
const backToAthletesBtn = document.getElementById('back-to-athletes');
const athleteDetailSection = document.getElementById('athlete-detail-section');
const athletesSection = document.getElementById('athletes-section');
const detailAthleteName = document.getElementById('detail-athlete-name');
const detailAthleteEmail = document.getElementById('detail-athlete-email');
const trainerCharts = document.getElementById('trainer-charts');
const mentalTrainerCharts = document.getElementById('mental-trainer-charts');
const dateRangePicker = document.getElementById('date-range-picker');
const exerciseTypeSelect = document.getElementById('exercise-type-select');
const exerciseTypeFilter = document.getElementById('exercise-type-filter');
const profileName = document.getElementById('profile-name');
const profileEmail = document.getElementById('profile-email');
const profileRole = document.getElementById('profile-role');
const profileExperience = document.getElementById('profile-experience');
const saveProfileBtn = document.getElementById('save-profile-btn');
const rawDataSection = document.getElementById('raw-data-section');
const backToAthletesFromRawDataBtn = document.getElementById('back-to-athletes-from-raw-data');
const viewRawDataBtn = document.getElementById('view-raw-data-btn');
const groupByToggle = document.getElementById('group-by-toggle');

// Chart instances
let completionRateChart = null;
let trainingsplanAdherenceChart = null;
let performanceTrendsChart = null;
let selfAssessmentChart = null;
let questionAnalysisChart = null;
let emotionalStateChart = null;

// Current user and selected athlete
let currentUser = null;
let currentUserProfile = null;
let selectedAthlete = null;
let selectedDateRange = {
    startDate: moment().subtract(30, 'days'),
    endDate: moment()
};

// Event Listeners
if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
}
if (logoutBtn) {
    logoutBtn.addEventListener('click', handleLogout);
}
if (navLinks) {
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}
if (refreshBtn) {
    refreshBtn.addEventListener('click', loadAthletes);
}
if (backToAthletesBtn) {
    backToAthletesBtn.addEventListener('click', showAthletesList);
}
if (saveProfileBtn) {
    saveProfileBtn.addEventListener('click', saveProfile);
}

if (backToAthletesFromRawDataBtn) {
    backToAthletesFromRawDataBtn.addEventListener('click', () => {
        if (rawDataSection && athletesSection) { // Ensure sections exist
            rawDataSection.classList.remove('active');
            rawDataSection.classList.add('hidden');
            athletesSection.classList.remove('hidden');
            athletesSection.classList.add('active');

            // Update active state for navigation links
            navLinks.forEach(link => link.classList.remove('active'));
            const athletesNavLink = document.querySelector('nav a[data-section="athletes"]');
            if (athletesNavLink) {
                athletesNavLink.classList.add('active');
            }
        }
        // Optionally, clear selected athlete or raw data content here if necessary in the future
    });
}

// Event listener for the group by toggle
if (groupByToggle) {
    groupByToggle.addEventListener('change', () => {
        if (selectedAthlete && rawDataSection.classList.contains('active')) {
            // If an athlete is selected and the raw data section is visible, reload data with new grouping
            loadRawData(selectedAthlete);
        }
    });
}

if (viewRawDataBtn) {
    viewRawDataBtn.addEventListener('click', () => {
        if (selectedAthlete && athleteDetailSection && rawDataSection) {
            // Hide current athlete detail section
            athleteDetailSection.classList.remove('active');
            athleteDetailSection.classList.add('hidden');

            // Show raw data section
            rawDataSection.classList.remove('hidden');
            rawDataSection.classList.add('active');

            // Load and display raw data for the selected athlete
            loadRawData(selectedAthlete);

            // Update navigation state
            navLinks.forEach(link => link.classList.remove('active'));
            const rawDataNavLink = document.querySelector('nav a[data-section="raw-data"]');
            if (rawDataNavLink) {
                rawDataNavLink.classList.add('active');
            }
        } else {
            if (!selectedAthlete) {
                alert("Please select an athlete first.");
            }
            // console.error for missing sections would be good if they are essential and expected to always exist
            if (!athleteDetailSection) console.error("athleteDetailSection not found");
            if (!rawDataSection) console.error("rawDataSection not found");
        }
    });
}

// Initialize date range picker
if (dateRangePicker) {
    $(dateRangePicker).daterangepicker({
        startDate: selectedDateRange.startDate,
        endDate: selectedDateRange.endDate,
        locale: {
            format: 'DD.MM.YYYY'
        },
        ranges: {
            'Letzte 7 Tage': [moment().subtract(6, 'days'), moment()],
            'Letzte 30 Tage': [moment().subtract(29, 'days'), moment()],
            'Dieser Monat': [moment().startOf('month'), moment().endOf('month')],
            'Letzter Monat': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'Letzte 3 Monate': [moment().subtract(3, 'month'), moment()],
            'Letzte 6 Monate': [moment().subtract(6, 'month'), moment()]
        }
    }, function(start, end) {
        selectedDateRange.startDate = start;
        selectedDateRange.endDate = end;
        if (selectedAthlete) {
            loadAthleteData(selectedAthlete);
        }
    });
}

// Check auth state
auth.onAuthStateChanged(user => {
    const emailVerificationAlert = document.getElementById('email-verification-alert'); // Get the alert element

    if (user) {
        currentUser = user;

        if (!user.emailVerified) {
            if (emailVerificationAlert) {
                emailVerificationAlert.innerHTML = `
                    Ihre E-Mail-Adresse ist nicht bestätigt. Bitte überprüfen Sie Ihr Postfach für die Bestätigungs-E-Mail.
                    <button id="resend-verification-btn" class="btn btn-small" style="margin-left: 10px; padding: 5px 10px; font-size: 0.9em;">Bestätigungs-E-Mail erneut senden</button>
                `;
                emailVerificationAlert.classList.remove('hidden');

                const resendBtn = document.getElementById('resend-verification-btn');
                if (resendBtn) {
                    resendBtn.addEventListener('click', async () => {
                        try {
                            resendBtn.disabled = true; // Disable immediately
                            resendBtn.textContent = 'Senden...';
                            await user.sendEmailVerification();
                            alert('Bestätigungs-E-Mail wurde erneut gesendet. Bitte überprüfen Sie Ihr Postfach (auch Spam).');
                            // Keep button disabled for a while to prevent spam
                            setTimeout(() => {
                                resendBtn.textContent = 'Bestätigungs-E-Mail erneut senden';
                                resendBtn.disabled = false;
                            }, 30000); // Re-enable after 30 seconds
                        } catch (error) {
                            console.error("Error resending verification email:", error);
                            alert(`Fehler beim Senden der E-Mail: ${error.message}`);
                            resendBtn.textContent = 'Bestätigungs-E-Mail erneut senden';
                            resendBtn.disabled = false;
                        }
                    });
                }
            }
            // Do NOT sign out. Allow user to proceed but with the alert displayed.
            // Specific features might be disabled elsewhere if needed based on verification status.
        } else {
            if (emailVerificationAlert) {
                emailVerificationAlert.classList.add('hidden'); // Hide if verified
            }
        }

        // Proceed with role checking and app setup
        checkUserRole(user)
            .then(isTrainer => {
                if (isTrainer) {
                    console.log("User is a trainer, showing trainer dashboard");
                    showApp(user); // This function shows the main app sections and hides login
                    loadAthletes();
                    loadProfile();
                } else {
                    // This case means the user is authenticated, email might be verified or not,
                    // but their profile does not have a TRAINER/MENTAL_TRAINER role.
                    console.log("User is not a trainer, redirecting to main app");
                    alert('Nur Trainer können auf dieses Dashboard zugreifen. Möglicherweise ist Ihr Konto nicht als Trainer registriert oder die Rolle wurde nicht korrekt zugewiesen.');
                    auth.signOut(); // Sign out as they are on the wrong dashboard.
                    // window.location.href = 'index.html'; // Redirecting after signOut will be handled by onAuthStateChanged again
                }
            })
            .catch(error => {
                console.error("Error checking user role:", error);
                alert('Fehler beim Überprüfen der Benutzerrolle. Bitte versuchen Sie es später erneut.');
                auth.signOut(); // Sign out on critical error during role check
            });

    } else {
        // User is signed out
        console.log("User signed out");
        if (emailVerificationAlert) {
            emailVerificationAlert.classList.add('hidden'); // Ensure it's hidden on logout
        }
        showLogin(); // This function shows the login form and hides the main app sections
    }
});

// Functions
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    // Show loading indicator
    const loginButton = document.querySelector('#login-form button');
    const originalButtonText = loginButton.textContent;
    loginButton.textContent = "Anmeldung läuft...";
    loginButton.disabled = true;

    auth.signInWithEmailAndPassword(email, password)
        .then(userCredential => {
            console.log("Login successful");
        })
        .catch(error => {
            console.error("Login failed:", error);
            alert(`Anmeldung fehlgeschlagen: ${error.message}`);

            // Reset button
            loginButton.textContent = originalButtonText;
            loginButton.disabled = false;
        });
}

function handleLogout() {
    auth.signOut();
}

function showLogin() {
    loginContainer.classList.remove('hidden');
    appContainer.classList.add('hidden');
}

function showApp(user) {
    loginContainer.classList.add('hidden');
    appContainer.classList.remove('hidden');
    userEmail.textContent = user.email;
}

function handleNavigation(e) {
    e.preventDefault();

    // Remove active class from all links
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to clicked link
    e.target.classList.add('active');

    // Hide all sections
    sections.forEach(section => {
        section.classList.remove('active');
        section.classList.add('hidden');
    });

    // Show selected section
    const sectionId = `${e.target.dataset.section}-section`;
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.add('active');
        section.classList.remove('hidden');
        if (sectionId === 'raw-data-section') {
            initializeCollapsibles(); // Ensure collapsibles are active if this section is shown
        }
    }
}

// Check if user is a trainer
async function checkUserRole(user) {
    try {
        const doc = await db.collection('user_profiles').doc(user.uid).get();

        if (doc.exists) {
            const profile = doc.data();
            currentUserProfile = { ...profile, id: doc.id };

            // Update UI with role
            const roleName = getRoleDisplayName(profile.role);
            userRole.textContent = roleName;
            trainerTypeDisplay.textContent = roleName;

            return profile.role === 'TRAINER' || profile.role === 'MENTAL_TRAINER';
        } else {
            console.log('No user profile found');
            return false;
        }
    } catch (error) {
        console.error('Error checking user role:', error);
        throw error;
    }
}

// Load athletes connected to the trainer
async function loadAthletes() {
    if (!currentUser) return;

    try {
        // Show loading state
        athletesList.innerHTML = '<div class="loading">Loading...</div>';

        // Get connections where the trainer is the target
        const snapshot = await db.collection('user_connections')
            .where('targetId', '==', currentUser.uid)
            .where('status', '==', 'ACTIVE')
            .get();

        // Clear athletes list
        athletesList.innerHTML = '';

        if (snapshot.empty) {
            emptyAthletes.style.display = 'block';
            return;
        }

        emptyAthletes.style.display = 'none';

        // Process each connection
        const athletePromises = snapshot.docs.map(async doc => {
            const connection = doc.data();

            // Get athlete profile
            const athleteDoc = await db.collection('user_profiles').doc(connection.initiatorId).get();
            if (!athleteDoc.exists) return null;

            const athlete = athleteDoc.data();

            // Get athlete stats
            const stats = await getAthleteStats(connection.initiatorId, connection);

            return {
                id: connection.initiatorId,
                name: athlete.displayName || athlete.email.split('@')[0],
                email: athlete.email,
                connection: connection,
                stats: stats,
                lastActive: athlete.lastUpdated || 0
            };
        });

        // Wait for all promises to resolve
        const athletes = (await Promise.all(athletePromises)).filter(a => a !== null);

        // Sort athletes by last active date (most recent first)
        athletes.sort((a, b) => b.lastActive - a.lastActive);

        // Render athletes
        athletes.forEach(athlete => {
            renderAthleteCard(athlete);
        });

    } catch (error) {
        console.error('Error loading athletes:', error);
        athletesList.innerHTML = `<div class="error">Fehler beim Laden der Athleten: ${error.message}</div>`;
    }
}

// Get athlete statistics based on connection type
async function getAthleteStats(athleteId, connection) {
    const stats = {
        exerciseCount: 0,
        completionRate: 0,
        trainingsplanAdherence: 0,
        selfAssessmentScore: 0,
        questionCount: 0
    };

    try {
        // For regular trainers, get exercise and trainingsplan stats
        if (connection.trainerAccess) {
            // Get exercise records count
            const exerciseSnapshot = await db.collection('exercise_records')
                .where('userId', '==', athleteId)
                .get();

            stats.exerciseCount = exerciseSnapshot.size;

            // Calculate completion rate from exercise records
            if (exerciseSnapshot.size > 0) {
                let completed = 0;
                let total = 0;

                exerciseSnapshot.forEach(doc => {
                    const record = doc.data();
                    if (record.completed) completed++;
                    total++;
                });

                stats.completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
            }

            // Get trainingsplan adherence
            const trainingsplanSnapshot = await db.collection('trainingsplan_history')
                .where('userId', '==', athleteId)
                .orderBy('lastUpdated', 'desc')
                .limit(1)
                .get();

            if (!trainingsplanSnapshot.empty) {
                const trainingsplan = trainingsplanSnapshot.docs[0].data();
                if (trainingsplan.items && trainingsplan.items.length > 0) {
                    const checkedItems = trainingsplan.items.filter(item => item.isChecked).length;
                    stats.trainingsplanAdherence = Math.round((checkedItems / trainingsplan.items.length) * 100);
                }
            }
        }

        // For mental trainers, get self-assessment stats
        if (connection.mentalTrainerAccess) {
            // Get training records (self-assessment)
            const trainingSnapshot = await db.collection('training_records')
                .where('userId', '==', athleteId)
                .get();

            if (trainingSnapshot.size > 0) {
                let totalScore = 0;
                let scoreCount = 0;

                trainingSnapshot.forEach(doc => {
                    const record = doc.data();
                    if (record.items && record.items.length > 0) {
                        record.items.forEach(item => {
                            if (item.score) {
                                totalScore += item.score;
                                scoreCount++;
                            }
                        });
                    }
                });

                stats.selfAssessmentScore = scoreCount > 0 ? Math.round((totalScore / scoreCount) * 10) / 10 : 0;
            }

            // Get question records count
            const questionSnapshot = await db.collection('question_records')
                .where('userId', '==', athleteId)
                .get();

            stats.questionCount = questionSnapshot.size;
        }

        return stats;
    } catch (error) {
        console.error('Error getting athlete stats:', error);
        return stats;
    }
}

// Render athlete card
function renderAthleteCard(athlete) {
    const card = document.createElement('div');
    card.className = 'athlete-card';
    card.dataset.id = athlete.id;

    // Format last active date
    const lastActive = athlete.lastActive ? moment(athlete.lastActive).format('DD.MM.YYYY HH:mm') : 'Nie';

    // Determine which stats to show based on connection permissions
    let statsHtml = '';

    if (athlete.connection.trainerAccess) {
        statsHtml += `
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.exerciseCount}</div>
                <div class="stat-label">Übungen</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.completionRate}%</div>
                <div class="stat-label">Abschlussrate</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.trainingsplanAdherence}%</div>
                <div class="stat-label">Trainingsplan</div>
            </div>
        `;
    }

    if (athlete.connection.mentalTrainerAccess) {
        statsHtml += `
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.selfAssessmentScore}</div>
                <div class="stat-label">Ø Bewertung</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.questionCount}</div>
                <div class="stat-label">Fragen</div>
            </div>
        `;
    }

    card.innerHTML = `
        <div class="athlete-name">${athlete.name}</div>
        <div class="athlete-email">${athlete.email}</div>
        <div class="athlete-stats">
            ${statsHtml}
        </div>
        <div class="athlete-last-active">Zuletzt aktiv: ${lastActive}</div>
    `;

    // Add click event to show athlete details
    card.addEventListener('click', () => {
        showAthleteDetails(athlete);
    });

    athletesList.appendChild(card);
}

// Show athlete details
function showAthleteDetails(athlete) {
    selectedAthlete = athlete;

    // Update UI
    detailAthleteName.textContent = athlete.name;
    detailAthleteEmail.textContent = athlete.email;

    // Show appropriate charts based on trainer type
    if (athlete.connection.trainerAccess) {
        trainerCharts.style.display = 'block';
        exerciseTypeFilter.style.display = 'block';
    } else {
        trainerCharts.style.display = 'none';
        exerciseTypeFilter.style.display = 'none';
    }

    if (athlete.connection.mentalTrainerAccess) {
        mentalTrainerCharts.style.display = 'block';
    } else {
        mentalTrainerCharts.style.display = 'none';
    }

    // Log data access
    logDataAccess(athlete.id);

    // Load athlete data
    loadAthleteData(athlete);

    // Show athlete detail section
    athletesSection.classList.remove('active');
    athletesSection.classList.add('hidden');
    athleteDetailSection.classList.remove('hidden');
    athleteDetailSection.classList.add('active');
}

// Log data access for audit purposes
async function logDataAccess(athleteId) {
    if (!currentUser || !currentUserProfile) return;

    try {
        // Create access log
        await db.collection('access_logs').add({
            trainerId: currentUser.uid,
            trainerEmail: currentUser.email,
            trainerRole: currentUserProfile.role,
            dataOwnerId: athleteId,
            timestamp: firebase.firestore.FieldValue.serverTimestamp(),
            accessType: currentUserProfile.role === 'TRAINER' ? 'EXERCISE_DATA' : 'SELF_ASSESSMENT_DATA',
            userAgent: navigator.userAgent
        });

        console.log('Data access logged successfully');
    } catch (error) {
        console.error('Error logging data access:', error);
        // Continue even if logging fails
    }
}

// Show athletes list
function showAthletesList() {
    athleteDetailSection.classList.remove('active');
    athleteDetailSection.classList.add('hidden');
    athletesSection.classList.remove('hidden');
    athletesSection.classList.add('active');

    // Clear selected athlete
    selectedAthlete = null;
}

// Load athlete data for charts
async function loadAthleteData(athlete) {
    if (!athlete) return;

    try {
        // Convert date range to timestamps
        const startTimestamp = selectedDateRange.startDate.valueOf();
        const endTimestamp = selectedDateRange.endDate.valueOf();

        // Load data based on trainer type
        if (athlete.connection.trainerAccess) {
            await loadExerciseTypes(athlete.id);
            await loadExerciseData(athlete.id, startTimestamp, endTimestamp);
            await loadTrainingsplanData(athlete.id, startTimestamp, endTimestamp);
        }

        if (athlete.connection.mentalTrainerAccess) {
            await loadSelfAssessmentData(athlete.id, startTimestamp, endTimestamp);
            await loadQuestionData(athlete.id, startTimestamp, endTimestamp);
        }

    } catch (error) {
        console.error('Error loading athlete data:', error);
        alert(`Fehler beim Laden der Athletendaten: ${error.message}`);
    }
}

// Load exercise types for filter
async function loadExerciseTypes(athleteId) {
    try {
        const snapshot = await db.collection('exercise_records')
            .where('userId', '==', athleteId)
            .get();

        // Clear select options except "All"
        while (exerciseTypeSelect.options.length > 1) {
            exerciseTypeSelect.remove(1);
        }

        if (snapshot.empty) return;

        // Extract unique exercise types
        const exerciseTypes = new Set();

        snapshot.forEach(doc => {
            const record = doc.data();
            if (record.exerciseId) {
                exerciseTypes.add(record.exerciseId);
            }
        });

        // Add options to select
        exerciseTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = getExerciseDisplayName(type);
            exerciseTypeSelect.appendChild(option);
        });

        // Add change event listener
        exerciseTypeSelect.addEventListener('change', () => {
            if (selectedAthlete) {
                loadExerciseData(selectedAthlete.id, selectedDateRange.startDate.valueOf(), selectedDateRange.endDate.valueOf());
            }
        });

    } catch (error) {
        console.error('Error loading exercise types:', error);
    }
}

// Get display name for exercise ID
function getExerciseDisplayName(exerciseId) {
    // Map of exercise IDs to display names
    const exerciseNames = {
        't_break': 'T Break',
        'y_break': 'Y Break',
        'line_up': 'Line Up',
        'eight_reds': '8reds Break',
        'five_reds': '5reds clear',
        'fifty_plus': '50+ Break',
        'frames': 'Frames',
        'x_break': 'X Break',
        'black_routines': 'Black Routines',
        'brown_to_reds': 'Brown to reds',
        'bc_to_reds': 'BC to reds',
        'pj_routines': 'PJ Routines',
        'random_bilder': 'Random Bilder',
        'blue_to_pink': 'Blau zu Pink',
        'long_shots': '8 Long shots',
        'medium_to_blue': 'Medium zu Blau',
        'rest': 'Rest',
        'three_reds_save': '3reds Save',
        'snooker_legen': 'Snooker legen',
        'black_to_bc': 'Von Schwarz zu BC',
        'blue_pink_black': 'Blau pink Schwarz',
        'yellow_splits': 'Gelb splits',
        'green': 'Grün',
        'brown': 'Braun',
        'blue': 'Blau',
        'black': 'Schwarz',
        'blue_doubletouch': 'Blue Doubletouch',
        'langer_stoss': 'Langer Stoß',
        'gerader_stoss_2_kreiden': 'Gerader Stoß 2 Kreiden',
        'stellungsspiel_gelb': 'Gelb',
        'stellungsspiel_gruen': 'Grün',
        'stellungsspiel_braun': 'Braun',
        'blau_durch_bc': 'Blau durch BC',
        'hohe_schwarze': 'Hohe Schwarze'
    };

    return exerciseNames[exerciseId] || exerciseId;
}

// Helper function to calculate linear regression slope
function calculateLinearRegressionSlope(points) {
    const n = points.length;
    if (n < 2) {
        return 0; // Not enough data points to calculate a slope
    }

    let sumX = 0;
    let sumY = 0;
    let sumXY = 0;
    let sumXX = 0;

    for (const point of points) {
        const x = point[0];
        const y = point[1];
        sumX += x;
        sumY += y;
        sumXY += x * y;
        sumXX += x * x;
    }

    const denominator = (n * sumXX - sumX * sumX);
    if (denominator === 0) {
        return 0; // Avoid division by zero; flat line
    }
    const slope = (n * sumXY - sumX * sumY) / denominator;
    return isNaN(slope) ? 0 : slope;
}

// Helper function to calculate mean
function calculateMean(scores) {
    if (!scores || scores.length === 0) {
        return 0;
    }
    return scores.reduce((a, b) => a + b, 0) / scores.length;
}

// Helper function to calculate standard deviation (population)
function calculateStdDev(scores, mean) {
    if (!scores || scores.length === 0) {
        return 0;
    }
    const variance = scores.map(s => Math.pow(s - mean, 2)).reduce((a, b) => a + b, 0) / scores.length;
    return Math.sqrt(variance);
}

// Load exercise data for charts
async function loadExerciseData(athleteId, startTimestamp, endTimestamp) {
    try {
        const selectedExerciseType = exerciseTypeSelect.value;

        let query = db.collection('exercise_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp);

        // This initial query is for completion rate, which always considers all exercises in the date range.
        // Performance trend will be filtered later if a specific exercise is selected.
        const completionSnapshot = await query.orderBy('lastUpdated', 'asc').get();

        // --- Completion Rate Chart (remains largely the same) ---
        if (completionSnapshot.empty) {
            renderCompletionRateChart([], []);
        } else {
            const completionDateLabels = [];
            const completionRates = [];
            const recordsByDateForCompletion = {};

            completionSnapshot.forEach(doc => {
                const record = doc.data();
                const date = moment(record.lastUpdated).format('DD.MM.YYYY');
                if (!recordsByDateForCompletion[date]) {
                    recordsByDateForCompletion[date] = [];
                }
                recordsByDateForCompletion[date].push(record);
            });

            Object.entries(recordsByDateForCompletion).sort((a,b) => moment(a[0], 'DD.MM.YYYY').valueOf() - moment(b[0], 'DD.MM.YYYY').valueOf()).forEach(([date, records]) => {
                completionDateLabels.push(date);
                const completed = records.filter(r => r.completed).length;
                const total = records.length;
                const rate = total > 0 ? (completed / total) * 100 : 0;
                completionRates.push(rate);
            });
            renderCompletionRateChart(completionDateLabels, completionRates);
        }

        // --- Performance Trend Chart (Z-Score Normalization Logic) ---
        let performanceQuery = db.collection('exercise_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp);

        if (selectedExerciseType !== 'all') {
            performanceQuery = performanceQuery.where('exerciseId', '==', selectedExerciseType);
        }
        // Order by lastUpdated to process chronologically if needed, though Z-score calculation itself is not order-dependent for stats.
        const performanceSnapshot = await performanceQuery.orderBy('lastUpdated', 'asc').get();

        if (performanceSnapshot.empty) {
            renderPerformanceTrendsChart([], []);
            return;
        }

        // Step A: Calculate Mean and StdDev per Exercise Type
        const exerciseStats = {};
        performanceSnapshot.forEach(doc => {
            const record = doc.data();
            if (record.exerciseId && typeof record.score === 'number') {
                if (!exerciseStats[record.exerciseId]) {
                    exerciseStats[record.exerciseId] = {
                        scores: [],
                        name: getExerciseDisplayName(record.exerciseId),
                        mean: 0,
                        stdDev: 0
                    };
                }
                exerciseStats[record.exerciseId].scores.push(record.score);
            }
        });

        for (const exId in exerciseStats) {
            const stats = exerciseStats[exId];
            if (stats.scores.length > 0) {
                stats.mean = calculateMean(stats.scores);
                stats.stdDev = calculateStdDev(stats.scores, stats.mean);
            }
        }

        // Step B: Calculate Z-Scores for each record
        const normalizedRecords = [];
        performanceSnapshot.forEach(doc => {
            const record = doc.data();
            if (record.exerciseId && typeof record.score === 'number' && record.lastUpdated != null) {
                const stats = exerciseStats[record.exerciseId];
                if (stats && stats.scores.length > 0) { // Ensure stats were calculated
                    const zScore = (stats.stdDev === 0) ? 0 : (record.score - stats.mean) / stats.stdDev;
                    normalizedRecords.push({
                        timestamp: record.lastUpdated,
                        exerciseId: record.exerciseId,
                        zScore: zScore,
                        originalScore: record.score,
                        exerciseName: stats.name
                    });
                }
            }
        });

        if (normalizedRecords.length === 0) {
            renderPerformanceTrendsChart([], []);
            return;
        }

        // --- Weekly Aggregation of Z-Scores ---
        if (normalizedRecords.length === 0) { // This check might be redundant if already done, but safe
            renderPerformanceTrendsChart([], []);
            return;
        }

        const weeklyAggregatedZScores = {};
        normalizedRecords.forEach(record => {
            const weekId = moment(record.timestamp).startOf('isoWeek').format('YYYY-MM-DD');
            if (!weeklyAggregatedZScores[weekId]) {
                weeklyAggregatedZScores[weekId] = {
                    sumZScore: 0,
                    count: 0,
                    weekStartTimestamp: moment(record.timestamp).startOf('isoWeek').valueOf()
                };
            }
            weeklyAggregatedZScores[weekId].sumZScore += record.zScore;
            weeklyAggregatedZScores[weekId].count++;
        });

        const processedWeeklyData = [];
        const weekIds = Object.keys(weeklyAggregatedZScores).sort((a, b) => {
            return weeklyAggregatedZScores[a].weekStartTimestamp - weeklyAggregatedZScores[b].weekStartTimestamp;
        });

        weekIds.forEach(weekId => {
            const data = weeklyAggregatedZScores[weekId];
            const averageZScore = (data.count > 0) ? data.sumZScore / data.count : 0;
            const weekLabel = `${moment(data.weekStartTimestamp).format('DD.MM')} - ${moment(data.weekStartTimestamp).endOf('isoWeek').format('DD.MM.YYYY')}`;
            processedWeeklyData.push({
                weekId: weekId,
                weekLabel: weekLabel,
                averageZScore: averageZScore,
                weekStartTimestamp: data.weekStartTimestamp
            });
        });

        // Sort final data by week start time just in case (though sorted keys should handle this)
        processedWeeklyData.sort((a,b) => a.weekStartTimestamp - b.weekStartTimestamp);

        // --- Calculate Week-over-Week Changes ---
        if (processedWeeklyData.length < 1) { // Needs at least 1 entry to show anything, though change needs 2.
                                              // If 1 entry, it will show 0 change.
            renderPerformanceTrendsChart([], []);
            return;
        }

        const weeklyPerformanceChanges = [];
        for (let i = 0; i < processedWeeklyData.length; i++) {
            let change = 0;
            if (i > 0) {
                const currentWeekAverageZScore = processedWeeklyData[i].averageZScore;
                const previousWeekAverageZScore = processedWeeklyData[i-1].averageZScore;
                change = currentWeekAverageZScore - previousWeekAverageZScore;
            }
            weeklyPerformanceChanges.push({
                weekLabel: processedWeeklyData[i].weekLabel,
                change: change
            });
        }

        const chartLabels = weeklyPerformanceChanges.map(w => w.weekLabel);
        const chartData = weeklyPerformanceChanges.map(w => w.change);

        const datasetLabel = 'Wöchentliche Veränderung (durchschnittl. Z-Score)';
        const changeDataset = { // Renamed from trendDataset to changeDataset for clarity
            label: datasetLabel,
            data: chartData,
            borderColor: '#e67e22', // Orange color for change
            backgroundColor: 'rgba(230, 126, 34, 0.1)',
            fill: false,
            tension: 0.1,
            pointRadius: 4
        };

        renderPerformanceTrendsChart(chartLabels, [changeDataset]); // Use changeDataset here

    } catch (error) {
        console.error('Error loading exercise data:', error);
    }
}

// Load trainingsplan data for charts
async function loadTrainingsplanData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('trainingsplan_history')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderTrainingsplanAdherenceChart([], []);
            return;
        }

        // Process data for chart
        const dateLabels = [];
        const adherenceRates = [];

        snapshot.forEach(doc => {
            const trainingsplan = doc.data();
            const date = moment(trainingsplan.lastUpdated).format('DD.MM.YYYY');

            dateLabels.push(date);

            if (trainingsplan.items && trainingsplan.items.length > 0) {
                const checkedItems = trainingsplan.items.filter(item => item.isChecked).length;
                const adherenceRate = Math.round((checkedItems / trainingsplan.items.length) * 100);
                adherenceRates.push(adherenceRate);
            } else {
                adherenceRates.push(0);
            }
        });

        // Render chart
        renderTrainingsplanAdherenceChart(dateLabels, adherenceRates);

    } catch (error) {
        console.error('Error loading trainingsplan data:', error);
    }
}

// Load self-assessment data for charts
async function loadSelfAssessmentData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('training_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderSelfAssessmentChart([], []);
            renderEmotionalStateChart([], []);
            return;
        }

        // Process data for charts
        const dateLabels = [];
        const avgScores = [];
        const emotionalStates = {
            'Vor Training': [],
            'Nach Training': []
        };

        snapshot.forEach(doc => {
            const record = doc.data();
            const date = moment(record.lastUpdated).format('DD.MM.YYYY');

            // Skip if no items
            if (!record.items || record.items.length === 0) return;

            // Calculate average score
            let totalScore = 0;
            let scoreCount = 0;

            record.items.forEach(item => {
                if (item.score !== undefined) {
                    totalScore += item.score;
                    scoreCount++;
                }
            });

            if (scoreCount > 0) {
                const avgScore = Math.round((totalScore / scoreCount) * 10) / 10;

                dateLabels.push(date);
                avgScores.push(avgScore);

                // Track emotional state by training type
                if (record.type === 'VOR_TRAINING') {
                    emotionalStates['Vor Training'].push({
                        date: date,
                        score: avgScore
                    });
                } else if (record.type === 'NACH_TRAINING') {
                    emotionalStates['Nach Training'].push({
                        date: date,
                        score: avgScore
                    });
                }
            }
        });

        // Render self-assessment chart
        renderSelfAssessmentChart(dateLabels, avgScores);

        // Prepare emotional state data
        const datasets = Object.entries(emotionalStates).map(([label, data], index) => {
            const color = index === 0 ? '#4a90e2' : '#e27c4a';

            return {
                label: label,
                data: data.map(d => ({ x: d.date, y: d.score })),
                borderColor: color,
                backgroundColor: color + '20',
                tension: 0.2,
                fill: false,
                pointRadius: 4
            };
        });

        // Get all unique dates for emotional state chart
        const allDates = [...new Set([
            ...emotionalStates['Vor Training'].map(d => d.date),
            ...emotionalStates['Nach Training'].map(d => d.date)
        ])].sort();

        renderEmotionalStateChart(allDates, datasets);

    } catch (error) {
        console.error('Error loading self-assessment data:', error);
    }
}

// Load question data for charts
async function loadQuestionData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('question_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderQuestionAnalysisChart([], []);
            return;
        }

        // Process data for chart
        const questionCounts = {};

        snapshot.forEach(doc => {
            const record = doc.data();

            // Skip if no questions
            if (!record.questions || record.questions.length === 0) return;

            // Count questions by title
            record.questions.forEach(question => {
                if (!question.title) return;

                if (!questionCounts[question.title]) {
                    questionCounts[question.title] = 0;
                }

                questionCounts[question.title]++;
            });
        });

        // Sort questions by count (descending)
        const sortedQuestions = Object.entries(questionCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // Show top 10 questions

        const labels = sortedQuestions.map(([title]) => title);
        const counts = sortedQuestions.map(([, count]) => count);

        // Render chart
        renderQuestionAnalysisChart(labels, counts);

    } catch (error) {
        console.error('Error loading question data:', error);
    }
}

// Render completion rate chart
function renderCompletionRateChart(labels, data) {
    const ctx = document.getElementById('completion-rate-chart').getContext('2d');

    if (completionRateChart) {
        completionRateChart.destroy();
    }

    completionRateChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Abschlussrate (%)',
                data: data,
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                tension: 0.2,
                fill: true,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Abschlussrate (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render trainingsplan adherence chart
function renderTrainingsplanAdherenceChart(labels, data) {
    const ctx = document.getElementById('trainingsplan-adherence-chart').getContext('2d');

    if (trainingsplanAdherenceChart) {
        trainingsplanAdherenceChart.destroy();
    }

    trainingsplanAdherenceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Trainingsplan-Einhaltung (%)',
                data: data,
                backgroundColor: 'rgba(101, 186, 105, 0.7)',
                borderColor: 'rgba(101, 186, 105, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Einhaltung (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render performance trends chart
function renderPerformanceTrendsChart(labels, datasets) {
    const ctx = document.getElementById('performance-trends-chart').getContext('2d');

    if (performanceTrendsChart) {
        performanceTrendsChart.destroy();
    }

    performanceTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    // beginAtZero is removed, so it defaults to false, allowing negative values for change
                    title: {
                        display: true,
                        text: 'Wöchentliche Veränderung (norm. Leistung Ø Z-Score)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Woche'
                    }
                }
            }
        }
    });
}

// Render self-assessment chart
function renderSelfAssessmentChart(labels, data) {
    const ctx = document.getElementById('self-assessment-chart').getContext('2d');

    if (selfAssessmentChart) {
        selfAssessmentChart.destroy();
    }

    selfAssessmentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Selbsteinschätzung (Ø)',
                data: data,
                borderColor: '#9c27b0',
                backgroundColor: 'rgba(156, 39, 176, 0.1)',
                tension: 0.2,
                fill: true,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Durchschnittliche Bewertung'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render question analysis chart
function renderQuestionAnalysisChart(labels, data) {
    const ctx = document.getElementById('question-analysis-chart').getContext('2d');

    if (questionAnalysisChart) {
        questionAnalysisChart.destroy();
    }

    // Generate colors for each bar
    const colors = labels.map((_, index) => {
        const hue = (index * 137) % 360;
        return `hsl(${hue}, 70%, 60%)`;
    });

    questionAnalysisChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Anzahl der Antworten',
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('60%', '70%')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Anzahl'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Frage'
                    }
                }
            }
        }
    });
}

// Render emotional state chart
function renderEmotionalStateChart(labels, datasets) {
    const ctx = document.getElementById('emotional-state-chart').getContext('2d');

    if (emotionalStateChart) {
        emotionalStateChart.destroy();
    }

    emotionalStateChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Emotionaler Zustand'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Load profile data
function loadProfile() {
    if (!currentUserProfile) return;

    profileName.value = currentUserProfile.displayName || '';
    profileEmail.value = currentUserProfile.email || '';
    profileRole.value = getRoleDisplayName(currentUserProfile.role) || '';
    profileExperience.value = currentUserProfile.experience || '';
}

// Save profile data
async function saveProfile() {
    if (!currentUser || !currentUserProfile) return;

    try {
        // Show loading state
        saveProfileBtn.disabled = true;
        saveProfileBtn.textContent = 'Speichern...';

        // Update profile
        await db.collection('user_profiles').doc(currentUser.uid).update({
            experience: profileExperience.value,
            lastUpdated: firebase.firestore.FieldValue.serverTimestamp()
        });

        // Update local profile
        currentUserProfile.experience = profileExperience.value;

        // Show success message
        alert('Profil erfolgreich gespeichert.');

    } catch (error) {
        console.error('Error saving profile:', error);
        alert(`Fehler beim Speichern des Profils: ${error.message}`);
    } finally {
        // Reset button
        saveProfileBtn.disabled = false;
        saveProfileBtn.textContent = 'Speichern';
    }
}

function escapeHTML(str) {
    if (str === null || str === undefined) return '';
    return String(str).replace(/[&<>"']/g, function (match) {
        return {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        }[match];
    });
}

// Helper function to get role display name
function getRoleDisplayName(role) {
    switch (role) {
        case 'TRAINER':
            return 'Trainer';
        case 'MENTAL_TRAINER':
            return 'Mental Trainer';
        case 'PLAYER':
            return 'Spieler';
        default:
            return role;
    }
}

// Add export functionality
document.querySelectorAll('.export-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        const chartId = btn.dataset.chart;
        exportChartData(chartId);
    });
});

// Export chart data to CSV
function exportChartData(chartId) {
    if (!selectedAthlete) return;

    let chart;
    let fileName;

    switch (chartId) {
        case 'completion-rate':
            chart = completionRateChart;
            fileName = `Abschlussrate_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'trainingsplan-adherence':
            chart = trainingsplanAdherenceChart;
            fileName = `Trainingsplan_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'performance-trends':
            chart = performanceTrendsChart;
            fileName = `Leistungstrends_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'self-assessment':
            chart = selfAssessmentChart;
            fileName = `Selbsteinschätzung_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'question-analysis':
            chart = questionAnalysisChart;
            fileName = `Fragenanalyse_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'emotional-state':
            chart = emotionalStateChart;
            fileName = `EmotionalerZustand_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        default:
            return;
    }

    if (!chart || !chart.data) return;

    // Prepare CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';

    // Add headers
    const headers = ['Datum'];
    chart.data.datasets.forEach(dataset => {
        headers.push(dataset.label);
    });
    csvContent += headers.join(',') + '\n';

    // Add data rows
    chart.data.labels.forEach((label, index) => {
        const row = [label];
        chart.data.datasets.forEach(dataset => {
            row.push(dataset.data[index] !== undefined ? dataset.data[index] : '');
        });
        csvContent += row.join(',') + '\n';
    });

    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

async function loadRawData(athlete) {
    const rawDataContainer = document.getElementById('raw-data-container');
    if (!rawDataContainer) {
        console.error('Error: raw-data-container not found.');
        return;
    }
    rawDataContainer.innerHTML = '<div class="loading">Loading raw data...</div>'; // Show loading indicator
    const selectedGroupBy = groupByToggle ? groupByToggle.value : 'date'; // Get selected grouping

    try {
        // Log access
        if (currentUser && currentUserProfile && athlete && athlete.id) {
            await db.collection('access_logs').add({
                trainerId: currentUser.uid,
                trainerEmail: currentUser.email,
                trainerRole: currentUserProfile.role,
                dataOwnerId: athlete.id, // Make sure athlete.id is the correct athlete UID
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                accessType: 'RAW_DATA_VIEW', // New access type
                userAgent: navigator.userAgent
            });
            console.log('Raw data access logged successfully for athlete:', athlete.id);
        } else {
            console.warn('Could not log raw data access: missing user, profile, or athlete ID.');
        }

        // Fetch all necessary data in parallel
        const [
            exerciseRecordsSnap,
            trainingsplanHistorySnap,
            trainingRecordsSnap,
            questionRecordsSnap,
            defaultExerciseDefinitionsSnap,
            customExerciseDefinitionsSnap
        ] = await Promise.all([
            db.collection('exercise_records').where('userId', '==', athlete.id).orderBy('lastUpdated', 'desc').get(),
            db.collection('trainingsplan_history').where('userId', '==', athlete.id).orderBy('lastUpdated', 'desc').get(),
            db.collection('training_records').where('userId', '==', athlete.id).orderBy('lastUpdated', 'desc').get(),
            db.collection('question_records').where('userId', '==', athlete.id).orderBy('lastUpdated', 'desc').get(),
            db.collection('user_exercise_definitions').where('custom', '==', false).get(),
            db.collection('user_exercise_definitions').where('userId', '==', athlete.id).where('custom', '==', true).get()
        ]);

        // Process exercise definitions into a map
        const exerciseCategoryMap = new Map();
        defaultExerciseDefinitionsSnap.forEach(doc => {
            const def = doc.data();
            if (def.id && def.category) {
                exerciseCategoryMap.set(def.id, def.category);
            }
        });
        customExerciseDefinitionsSnap.forEach(doc => {
            const def = doc.data();
            if (def.id && def.category) { // Custom definitions might override default if IDs clash, or just add new ones
                exerciseCategoryMap.set(def.id, def.category);
            }
        });

        // Process main records
        let exerciseRecords = exerciseRecordsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const trainingsplanHistory = trainingsplanHistorySnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const trainingRecords = trainingRecordsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const questionRecords = questionRecordsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Augment exercise records with their categories
        exerciseRecords = exerciseRecords.map(record => {
            const category = exerciseCategoryMap.get(record.exerciseId) || 'Uncategorized';
            return { ...record, resolvedCategory: category };
        });

        rawDataContainer.innerHTML = ''; // Clear loading message

        // Pass the selectedGroupBy value to displayDataInSection for Exercise Records
        displayDataInSection(rawDataContainer, 'Exercise Records', exerciseRecords, selectedGroupBy);
        displayDataInSection(rawDataContainer, 'Trainingsplan History', trainingsplanHistory); // Other sections don't need groupBy
        displayDataInSection(rawDataContainer, 'Self-Assessment Records (Training Records)', trainingRecords);
        displayDataInSection(rawDataContainer, 'Question Records', questionRecords);

        initializeCollapsibles(); // Initialize the new collapsible elements

    } catch (error) {
        console.error("Error loading raw data:", error);
        rawDataContainer.innerHTML = `<div class="error">Error loading raw data: ${error.message}</div>`;
    }
}

function displayDataInSection(container, title, dataArray, groupBy = 'date') { // Added groupBy parameter
    const sectionDiv = document.createElement('div');
    sectionDiv.className = 'data-category-block';

    const mainCategoryButton = document.createElement('button');
    mainCategoryButton.className = 'collapsible';
    mainCategoryButton.textContent = `${title} (${dataArray.length})`;
    sectionDiv.appendChild(mainCategoryButton);

    const mainCategoryContentDiv = document.createElement('div');
    mainCategoryContentDiv.className = 'collapsible-content';

    if (dataArray.length === 0) {
        const noDataMsg = document.createElement('p');
        noDataMsg.textContent = `No ${title.toLowerCase()} found.`;
        mainCategoryContentDiv.appendChild(noDataMsg);
    } else {
        if (title === 'Exercise Records' && groupBy === 'category') {
            // Group by category
            const recordsByCategory = {};
            dataArray.forEach(record => {
                const category = record.resolvedCategory || 'Uncategorized'; // Use resolvedCategory
                if (!recordsByCategory[category]) {
                    recordsByCategory[category] = [];
                }
                recordsByCategory[category].push(record);
            });

            Object.entries(recordsByCategory).forEach(([category, records]) => {
                const subCategoryButton = document.createElement('button');
                subCategoryButton.className = 'collapsible'; // Nested collapsible
                subCategoryButton.textContent = `${category} (${records.length})`;
                mainCategoryContentDiv.appendChild(subCategoryButton);

                const subCategoryContentDiv = document.createElement('div');
                subCategoryContentDiv.className = 'collapsible-content';

                records.forEach(record => {
                    subCategoryContentDiv.appendChild(createRecordCard(record, title));
                });
                mainCategoryContentDiv.appendChild(subCategoryContentDiv);
            });

        } else if (title === 'Exercise Records' && groupBy === 'date') {
            // Sort by timestamp (desc) if available, else lastUpdated
            dataArray.sort((a, b) => (b.timestamp || b.lastUpdated || 0) - (a.timestamp || a.lastUpdated || 0));
            dataArray.forEach(record => {
                mainCategoryContentDiv.appendChild(createRecordCard(record, title));
            });
        } else {
            // Default handling for other data types (Trainingsplan, Self-Assessment, etc.)
            // Sort by timestamp (desc) if available, else lastUpdated.
            // Trainingsplan History might have 'weekStartDate' as a primary sort key from DB,
            // but for consistency in raw data display, we can also use timestamp if available.
            // However, Trainingsplan data from Firestore is already ordered by lastUpdated desc.
            // Let's apply a consistent client-side sort if timestamp is the target.
            if (dataArray.length > 0 && (dataArray[0].timestamp || dataArray[0].lastUpdated)) {
                 dataArray.sort((a, b) => (b.timestamp || b.lastUpdated || 0) - (a.timestamp || a.lastUpdated || 0));
            }
            // For 'Trainingsplan History', if `weekStartDate` is the more relevant display sort key,
            // that should be handled specifically. Given the request is about 'timestamp', we'll use that.
            dataArray.forEach(record => {
                mainCategoryContentDiv.appendChild(createRecordCard(record, title));
            });
        }
    }

    sectionDiv.appendChild(mainCategoryContentDiv);
    container.appendChild(sectionDiv);
}


// Helper function to create a single data card (extracted from displayDataInSection)
function createRecordCard(record, recordTypeTitle) {
    const card = document.createElement('div');
    card.className = 'data-card';

    const cardHeader = document.createElement('div');
    cardHeader.className = 'data-card-header';

    let headerIcon = '';
    let headerText = '';

    switch (recordTypeTitle) {
        case 'Exercise Records':
            headerIcon = '<i class="fas fa-dumbbell"></i>';
            const exerciseDate = record.timestamp ? moment(record.timestamp).format('DD.MM.YYYY HH:mm') : (record.lastUpdated ? moment(record.lastUpdated).format('DD.MM.YYYY HH:mm') + ' (sync)' : 'No date');
            headerText = `${getExerciseDisplayName(record.exerciseId || 'Unknown Exercise')} (${exerciseDate})`;
            break;
        case 'Trainingsplan History':
            headerIcon = '<i class="fas fa-tasks"></i>';
            // This seems to use a specific date field, not lastUpdated or timestamp for its primary date.
            headerText = `Plan from ${record.weekStartDate ? moment(record.weekStartDate).format('LL') : 'Unknown Date'}`;
            break;
        case 'Self-Assessment Records (Training Records)':
            headerIcon = '<i class="fas fa-psychology"></i>';
            const assessmentDate = record.timestamp ? moment(record.timestamp).format('DD.MM.YYYY HH:mm') : (record.lastUpdated ? moment(record.lastUpdated).format('DD.MM.YYYY HH:mm') + ' (sync)' : 'No date');
            headerText = `Assessment - ${record.type || 'N/A'} (${assessmentDate})`;
            break;
        case 'Question Records':
            headerIcon = '<i class="fas fa-question-circle"></i>';
            const questionDate = record.timestamp ? moment(record.timestamp).format('DD.MM.YYYY HH:mm') : (record.lastUpdated ? moment(record.lastUpdated).format('DD.MM.YYYY HH:mm') + ' (sync)' : 'No date');
            headerText = `Questionnaire (${questionDate})`;
            break;
        default: // General fallback, prefer timestamp if available
            headerIcon = '<i class="fas fa-file-alt"></i>';
            const defaultDate = record.timestamp ? moment(record.timestamp).format('DD.MM.YYYY HH:mm') : (record.lastUpdated ? moment(record.lastUpdated).format('DD.MM.YYYY HH:mm') + ' (sync)' : 'No date');
            headerText = `Record (${defaultDate})`;
    }

    cardHeader.innerHTML = `${headerIcon} ${escapeHTML(headerText)}`;
    card.appendChild(cardHeader);

    const cardBody = document.createElement('div');
    cardBody.className = 'data-card-body';

    // Define keys that are already displayed in the header or are meta-keys not for direct display here.
    const unwantedKeys = ['id', 'userId', 'user', 'athleteId', 'timestamp', 'date', 'weekStartDate', 'exerciseId', 'type', 'category', 'lastUpdated'];
    // For Exercise Records, 'score' and 'completed' are important, so don't hide them by default via unwantedKeys.
    // Let's refine unwantedKeys based on context if necessary, or handle specific display directly.

    for (const key in record) {
        if (Object.hasOwnProperty.call(record, key) && !unwantedKeys.includes(key)) {
            const value = record[key];
            if (value === null || value === undefined || (Array.isArray(value) && value.length === 0)) continue;

            const dataPoint = document.createElement('div');
            dataPoint.className = 'data-point';

            const labelDiv = document.createElement('div');
            labelDiv.className = 'label';
            labelDiv.textContent = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'); // Format key name

            const valueDiv = document.createElement('div');
            valueDiv.className = 'value';

            if (value && typeof value.toDate === 'function') { // Firestore Timestamp
                valueDiv.textContent = moment(value.toDate()).format('DD.MM.YYYY HH:mm');
            } else if (Array.isArray(value)) {
                const ul = document.createElement('ul');
                value.forEach(item => {
                    const li = document.createElement('li');
                    if (typeof item === 'object' && item !== null) {
                        // Display simple objects nicely, complex ones might need JSON.stringify or specific formatting
                        li.innerHTML = Object.entries(item).map(([k, v]) => `<strong>${escapeHTML(k)}:</strong> ${escapeHTML(v)}`).join(', ');
                    } else {
                        li.textContent = escapeHTML(item);
                    }
                    ul.appendChild(li);
                });
                valueDiv.appendChild(ul);
            } else if (typeof value === 'object' && value !== null) {
                // For other objects, pretty print JSON
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(value, null, 2);
                valueDiv.appendChild(pre);
            } else {
                valueDiv.textContent = escapeHTML(value);
            }

            dataPoint.appendChild(labelDiv);
            dataPoint.appendChild(valueDiv);
            cardBody.appendChild(dataPoint);
        }
    }
    card.appendChild(cardBody);
    return card;
}


function initializeCollapsibles() {
    const collapsibles = document.querySelectorAll('#raw-data-container .collapsible');

    collapsibles.forEach(button => {
        // To prevent attaching multiple listeners if this function is called more than once,
        // we replace the element with a clone of itself, which removes any existing listeners.
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        newButton.addEventListener('click', function() {
            this.classList.toggle('active');
            const content = this.nextElementSibling;
            if (content && content.classList.contains('collapsible-content')) {
                if (content.style.display === 'block') {
                    content.style.display = 'none';
                } else {
                    content.style.display = 'block';
                }
            }
        });
    });
}
