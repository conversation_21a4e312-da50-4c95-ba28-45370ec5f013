package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.util.Log
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.LocalDateTime
import org.threeten.bp.LocalTime
import org.threeten.bp.ZoneId
import org.threeten.bp.temporal.ChronoUnit
import org.threeten.bp.temporal.TemporalAdjusters
import java.util.concurrent.TimeUnit

/**
 * <PERSON>les scheduling the weekly training plan reminder
 */
class TrainingsplanReminderScheduler(private val context: Context) {

    companion object {
        private const val TAG = "TrainingsplanScheduler"
    }

    /**
     * Schedules a weekly check on Friday at 6 PM to notify about incomplete exercises
     */
    fun scheduleWeeklyReminder() {
        // Create work constraints
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        // Calculate initial delay to next Friday at 6 PM
        val initialDelay = calculateInitialDelay()
        
        Log.d(TAG, "Scheduling weekly reminder with initial delay of $initialDelay ms")
        
        // Create a periodic work request that runs daily
        val reminderWorkRequest = PeriodicWorkRequestBuilder<TrainingsplanReminderWorker>(
            24, TimeUnit.HOURS // Run every 24 hours
        )
            .setConstraints(constraints)
            .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
            // Add flex interval to allow WorkManager to optimize battery usage
            .setBackoffCriteria(
                androidx.work.BackoffPolicy.LINEAR,
                30, TimeUnit.MINUTES
            )
            // Add tags for easier debugging
            .addTag("trainingsplan_reminder")
            .addTag("weekly_notification")
            .build()
        
        // Schedule the work request with REPLACE policy to ensure it's always up to date
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            TrainingsplanReminderWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE, // Always use fresh scheduling
            reminderWorkRequest
        )
        
        Log.d(TAG, "Weekly reminder scheduled successfully")
        
        // Also schedule a one-time work to ensure it runs at least once
        scheduleOneTimeCheck()
    }
    
    /**
     * Schedule a one-time check to ensure the worker runs at least once
     * This helps with testing and ensures the worker is properly registered
     */
    private fun scheduleOneTimeCheck() {
        val oneTimeWorkRequest = OneTimeWorkRequestBuilder<TrainingsplanReminderWorker>()
            .setInitialDelay(10, TimeUnit.SECONDS)
            .addTag("trainingsplan_reminder_test")
            .build()
            
        WorkManager.getInstance(context).enqueue(oneTimeWorkRequest)
        Log.d(TAG, "One-time test worker scheduled")
    }
    
    /**
     * Calculate the initial delay until the next Friday at 6 PM
     */
    private fun calculateInitialDelay(): Long {
        val now = LocalDateTime.now()
        val currentDayOfWeek = now.dayOfWeek
        val currentTime = now.toLocalTime()
        
        // Target time is 6 PM (18:00)
        val targetTime = LocalTime.of(18, 0)
        
        // Calculate the next Friday
        val nextFriday: LocalDate = if (currentDayOfWeek == DayOfWeek.FRIDAY && currentTime.isBefore(targetTime)) {
            // If today is Friday and it's before 6 PM, use today
            now.toLocalDate()
        } else {
            // Otherwise, get the next Friday
            now.toLocalDate().with(TemporalAdjusters.next(DayOfWeek.FRIDAY))
        }
        
        // Combine the date and time
        val targetDateTime = LocalDateTime.of(nextFriday, targetTime)
        
        // Calculate the delay in milliseconds
        val delay = ChronoUnit.MILLIS.between(now, targetDateTime)
        
        Log.d(TAG, "Current time: $now")
        Log.d(TAG, "Target time: $targetDateTime")
        Log.d(TAG, "Calculated delay: $delay ms")
        
        return delay
    }
    
    /**
     * Cancels the scheduled reminder
     */
    fun cancelReminder() {
        Log.d(TAG, "Cancelling existing reminder")
        WorkManager.getInstance(context).cancelUniqueWork(TrainingsplanReminderWorker.WORK_NAME)
    }
}
