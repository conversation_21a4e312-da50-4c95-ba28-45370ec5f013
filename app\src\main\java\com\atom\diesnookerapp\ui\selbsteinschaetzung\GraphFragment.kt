package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class GraphFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_graph, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val items = listOf(
            AssessmentItem("graph_before_training", "Vor Training"),
            AssessmentItem("graph_after_training", "Nach Training"),
            AssessmentItem("graph_before_tournament", "Vor Turnier"),
            AssessmentItem("graph_after_tournament", "Nach Turnier")
        )

        val adapter = AssessmentAdapter(items) { item ->
            when (item.id) {
                "graph_before_training" -> navigateToGraphDetail(TrainingType.BEFORE_TRAINING)
                "graph_after_training" -> navigateToGraphDetail(TrainingType.AFTER_TRAINING)
                "graph_before_tournament" -> navigateToGraphDetail(TrainingType.BEFORE_TOURNAMENT)
                "graph_after_tournament" -> navigateToGraphDetail(TrainingType.AFTER_TOURNAMENT)
            }
        }

        recyclerView.adapter = adapter
    }

    private fun navigateToGraphDetail(type: TrainingType) {
        findNavController().navigate(
            R.id.action_graphFragment_to_graphDetailFragment,
            Bundle().apply {
                putString("type", type.name)
            }
        )
    }
} 