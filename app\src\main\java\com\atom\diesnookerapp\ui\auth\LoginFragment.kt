package com.atom.diesnookerapp.ui.auth

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ProgressBar
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.FirebaseUserProfile
import com.atom.diesnookerapp.data.firebase.SyncManager
import com.atom.diesnookerapp.data.firebase.UserProfileRepository
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

class LoginFragment : Fragment() {

    private lateinit var emailEditText: TextInputEditText
    private lateinit var passwordEditText: TextInputEditText
    private lateinit var loginButton: Button
    private lateinit var registerButton: Button
    private lateinit var forgotPasswordButton: Button
    private lateinit var progressBar: ProgressBar

    private lateinit var authManager: FirebaseAuthManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_login, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize views
        emailEditText = view.findViewById(R.id.emailEditText)
        passwordEditText = view.findViewById(R.id.passwordEditText)
        loginButton = view.findViewById(R.id.loginButton)
        registerButton = view.findViewById(R.id.registerButton)
        forgotPasswordButton = view.findViewById(R.id.forgotPasswordButton)
        progressBar = view.findViewById(R.id.progressBar)

        // Initialize Firebase Auth Manager with context
        authManager = FirebaseAuthManager(requireContext())

        // Check if already logged in
        if (authManager.isLoggedIn()) {
            Log.d("LoginFragment", "User already logged in, navigating to home")
            navigateToHome()
            return
        } else {
            Log.d("LoginFragment", "No user logged in, showing login screen")
        }

        // Set up click listeners
        loginButton.setOnClickListener {
            login()
        }

        registerButton.setOnClickListener {
            findNavController().navigate(R.id.action_loginFragment_to_registerFragment)
        }

        forgotPasswordButton.setOnClickListener {
            val email = emailEditText.text.toString().trim()
            if (email.isNotEmpty()) {
                resetPassword(email)
            } else {
                Toast.makeText(
                    requireContext(),
                    "Bitte gib deine E-Mail-Adresse ein",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun login() {
        val email = emailEditText.text.toString().trim()
        val password = passwordEditText.text.toString().trim()

        // Validate input
        if (email.isEmpty() || password.isEmpty()) {
            Toast.makeText(
                requireContext(),
                "Bitte fülle alle Felder aus",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        // Show progress
        setLoading(true)

        // Attempt login
        lifecycleScope.launch {
            try {
                val result = authManager.loginUser(email, password)

                if (result.isSuccess) {
                    // Ensure user profile exists
                    val userProfileRepository = UserProfileRepository(requireContext())
                    val profileResult = userProfileRepository.getCurrentUserProfile()

                    if (profileResult.isFailure) {
                        Log.e("LoginFragment", "Failed to get user profile: ${profileResult.exceptionOrNull()?.message}")
                        // Create a default profile if getting the profile failed
                        val userId = authManager.getCurrentUserId()!!
                        val defaultProfile = FirebaseUserProfile.createDefault(userId, email)
                        userProfileRepository.updateCurrentUserProfile(defaultProfile)
                    }

                    // Start sync service
                    val syncManager = SyncManager(requireContext())
                    syncManager.scheduleSyncWork()

                    try {
                        // Perform initial sync
                        syncManager.performFullSync()
                    } catch (e: Exception) {
                        Log.e("LoginFragment", "Error during initial sync: ${e.message}")
                        // Continue even if sync fails
                    }

                    // Navigate to home
                    navigateToHome()
                } else {
                    val errorMessage = result.exceptionOrNull()?.message ?: "Unbekannter Fehler"
                    Log.e("LoginFragment", "Login failed: $errorMessage")
                    Toast.makeText(
                        requireContext(),
                        "Login fehlgeschlagen: $errorMessage",
                        Toast.LENGTH_SHORT
                    ).show()
                    setLoading(false)
                }
            } catch (e: Exception) {
                Log.e("LoginFragment", "Exception during login: ${e.message}")
                Toast.makeText(
                    requireContext(),
                    "Login fehlgeschlagen: ${e.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
                setLoading(false)
            }
        }
    }

    private fun resetPassword(email: String) {
        setLoading(true)

        lifecycleScope.launch {
            val result = authManager.sendPasswordResetEmail(email)

            if (result.isSuccess) {
                Toast.makeText(
                    requireContext(),
                    "E-Mail zum Zurücksetzen des Passworts wurde gesendet",
                    Toast.LENGTH_SHORT
                ).show()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Fehler beim Senden der E-Mail: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
            }

            setLoading(false)
        }
    }

    private fun navigateToHome() {
        // Check if we came from the settings screen
        val previousDestination = findNavController().previousBackStackEntry?.destination?.id
        if (previousDestination == R.id.navigation_settings) {
            // Go back to settings
            findNavController().popBackStack()
        } else {
            // Go to home screen
            findNavController().navigate(R.id.action_loginFragment_to_homeFragment)
        }
    }

    private fun setLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        loginButton.isEnabled = !isLoading
        registerButton.isEnabled = !isLoading
        forgotPasswordButton.isEnabled = !isLoading
    }
}
