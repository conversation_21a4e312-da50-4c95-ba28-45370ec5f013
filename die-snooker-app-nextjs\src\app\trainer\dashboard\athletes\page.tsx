"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import {
  collection, query, where, getDocs, doc, getDoc, Timestamp
} from "firebase/firestore";
import Link from "next/link";
import { useEffect, useState } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface UserProfile { // Simplified, expand if more trainer details needed
  userId: string;
  email: string;
  displayName: string;
  role: string;
}

interface ConnectedAthlete {
  connectionId: string; // ID of the user_connections document
  athleteId: string;    // UID of the athlete (initiatorId)
  athleteProfile?: UserProfile; // Populated client-side
  connectionDate: Timestamp; // createdAt from user_connections
  permissions: {
    exerciseAccess: boolean;
    selfAssessmentAccess: boolean;
  };
  trainerType: 'TRAINER' | 'MENTAL_TRAINER'; // The type of connection this trainer has with athlete
}

function AthletesListPage() {
  const { currentUser, userProfile: currentTrainerProfile } = useAuth();
  const [athletes, setAthletes] = useState<ConnectedAthlete[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    const fetchConnectedAthletes = async () => {
      setLoading(true);
      setError(null);
      try {
        const q = query(
          collection(db, "user_connections"),
          where("targetId", "==", currentUser.uid),
          where("status", "==", "ACTIVE")
        );
        const querySnapshot = await getDocs(q);
        const fetchedAthletesPromises = querySnapshot.docs.map(async (docSnap) => {
          const connectionData = docSnap.data();
          const athleteId = connectionData.initiatorId;

          const athleteProfileDoc = await getDoc(doc(db, "user_profiles", athleteId));
          let athleteProfileData: UserProfile | undefined = undefined;
          if (athleteProfileDoc.exists()) {
            athleteProfileData = athleteProfileDoc.data() as UserProfile;
          }

          return {
            connectionId: docSnap.id,
            athleteId: athleteId,
            athleteProfile: athleteProfileData,
            connectionDate: connectionData.createdAt as Timestamp,
            permissions: connectionData.permissions || { exerciseAccess: false, selfAssessmentAccess: false }, // Default if undefined
            trainerType: connectionData.trainerType,
          } as ConnectedAthlete;
        });

        const fetchedAthletes = await Promise.all(fetchedAthletesPromises);
        setAthletes(fetchedAthletes.sort((a,b) => (a.athleteProfile?.displayName || "").localeCompare(b.athleteProfile?.displayName || ""))); // Sort by name
      } catch (err: any) {
        console.error("Error fetching connected athletes:", err);
        setError("Failed to load athletes. " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchConnectedAthletes();
  }, [currentUser]);

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Connected Athletes</h1>

      {athletes.length === 0 && !loading && (
        <p className="text-gray-600 bg-white p-6 rounded-lg shadow-md">No active athlete connections.</p>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {athletes.map(athlete => (
          <Link key={athlete.athleteId} href={`/trainer/dashboard/athlete/${athlete.athleteId}`} legacyBehavior>
            <a className="block bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <h2 className="text-xl font-semibold text-purple-700 mb-2">
                {athlete.athleteProfile?.displayName || athlete.athleteProfile?.email || 'Unnamed Athlete'}
              </h2>
              <p className="text-sm text-gray-600">
                Connected as: <span className="font-medium">{athlete.trainerType}</span>
              </p>
              <p className="text-sm text-gray-600">
                Email: {athlete.athleteProfile?.email || 'N/A'}
              </p>
              <p className="text-sm text-gray-600">
                Connection Date: {athlete.connectionDate ? new Date(athlete.connectionDate.toDate()).toLocaleDateString('de-DE') : 'N/A'}
              </p>
              <div className="mt-3">
                <h4 className="text-xs font-semibold text-gray-500 uppercase">Permissions:</h4>
                <ul className="text-xs text-gray-500 list-disc list-inside">
                  {athlete.permissions.exerciseAccess && <li>View Exercises & Training Plan</li>}
                  {athlete.permissions.selfAssessmentAccess && <li>View Self-Assessments</li>}
                  {!athlete.permissions.exerciseAccess && !athlete.permissions.selfAssessmentAccess && <li>No specific data permissions granted.</li>}
                </ul>
              </div>
            </a>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default withAuth(AthletesListPage, {
  roles: ['TRAINER', 'MENTAL_TRAINER'],
  redirectTo: '/trainer/login'
});
