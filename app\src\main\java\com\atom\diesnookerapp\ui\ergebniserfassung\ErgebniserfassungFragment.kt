package com.atom.diesnookerapp.ui.ergebniserfassung

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.app.AlertDialog
import android.widget.Button // Added for the new Button
import android.widget.Chronometer
import android.widget.EditText
// import android.widget.ImageButton // Removed as trainingActionsMenuButton is removed
import android.widget.TextView
import android.widget.Toast
// import androidx.appcompat.widget.PopupMenu // Removed as showTrainingActionsPopupMenu is removed
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.WeeklyTrainingRepository
import com.atom.diesnookerapp.data.firebase.WeeklyTrainingSummary
import com.atom.diesnookerapp.ui.selbsteinschaetzung.AssessmentAdapter
import com.atom.diesnookerapp.ui.selbsteinschaetzung.AssessmentItem
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import kotlinx.coroutines.launch
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.temporal.TemporalAdjusters

class ErgebniserfassungFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    internal lateinit var firebaseAuthManager: FirebaseAuthManager
    private lateinit var assessmentAdapter: AssessmentAdapter
    private var itemsList = mutableListOf<AssessmentItem>()

    // Timer and SharedPreferences variables
    internal lateinit var chronometer: Chronometer
    // private lateinit var startButton: Button // Removed
    // private lateinit var stopButton: Button // Removed
    private var trainingStartTime: Long = 0L
    internal var isTrainingRunning: Boolean = false
    internal lateinit var sharedPreferences: SharedPreferences
    internal lateinit var weeklyTrainingRepository: WeeklyTrainingRepository
    internal lateinit var weeklyTimeTextView: TextView
    // private lateinit var manualTimeEntryButton: Button // Already removed
    // private lateinit var trainingActionsMenuButton: ImageButton // Removed
    private lateinit var toggleTimerButton: Button // Added
    private lateinit var manualEntryButtonRevised: Button // Added for this task


    companion object {
        private const val PREFS_NAME = "TrainingTimePrefs"
        private const val PREF_IS_TRAINING_RUNNING = "isTrainingRunning"
        private const val PREF_CHRONOMETER_BASE_TIME = "chronometerBaseTime"
        private const val PREF_TRAINING_WEEK_START_DATE = "trainingWeekStartDate"
        private const val PREF_CURRENT_WEEK_ACCUMULATED_TIME = "currentWeekAccumulatedTime"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_ergebniserfassung, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        firebaseAuthManager = FirebaseAuthManager() // Already initialized
        weeklyTrainingRepository = WeeklyTrainingRepository(requireContext())
        sharedPreferences = requireActivity().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        chronometer = view.findViewById(R.id.training_chronometer)
        weeklyTimeTextView = view.findViewById(R.id.textview_weekly_training_time)
        toggleTimerButton = view.findViewById(R.id.button_toggle_timer)
        manualEntryButtonRevised = view.findViewById(R.id.button_manual_entry_revised)

        toggleTimerButton.setOnClickListener {
            if (isTrainingRunning) {
                stopTrainingTimer()
            } else {
                startTrainingTimer()
            }
        }

        manualEntryButtonRevised.setOnClickListener {
            showManualTimeEntryDialog() // This method already exists
        }

        view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton).setOnClickListener {
            findNavController().navigate(R.id.navigation_exercise_history)
        }

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        assessmentAdapter = AssessmentAdapter(itemsList) { item ->
            val bundle = Bundle().apply { putString("categoryName", item.id) }
            when (item.id.lowercase().replace(" ", "")) {
                "breakbuilding" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_breakbuildingFragment, bundle)
                "potting" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_pottingFragment, bundle)
                "safeties" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_safetiesFragment, bundle)
                "splits" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_splitsFragment, bundle)
                "stellungsspiel" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_stellungsspielFragment, bundle)
                "technik" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_technikFragment, bundle)
                else -> findNavController().navigate(R.id.action_ergebniserfassung_to_generic_exercise_list, bundle)
            }
        }
        recyclerView.adapter = assessmentAdapter

        initializeWeeklyTracking()
        archiveAndResetIfNeeded() // Call after initialization
        loadTimerState()
        updateWeeklyTimeDisplay() // Initial display update
        updateTimerToggleButtonText() // Added call
        loadCategories()
    }

    // Removed showTrainingActionsPopupMenu method as it's no longer used
    // private fun showTrainingActionsPopupMenu(anchorView: View) { ... }

    private fun updateTimerToggleButtonText() {
        if (isTrainingRunning) {
            toggleTimerButton.text = "Training stoppen"
        } else {
            toggleTimerButton.text = "Training starten"
        }
    }

    private fun showManualTimeEntryDialog() {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_manual_time_entry, null)
        val hoursEditText = dialogView.findViewById<EditText>(R.id.edittext_manual_hours)
        val minutesEditText = dialogView.findViewById<EditText>(R.id.edittext_manual_minutes)

        AlertDialog.Builder(requireContext())
            .setTitle("Trainingszeit manuell eingeben")
            .setView(dialogView)
            .setPositiveButton("Speichern") { dialog, _ ->
                val hoursStr = hoursEditText.text.toString()
                val minutesStr = minutesEditText.text.toString()

                val hours = hoursStr.toIntOrNull() ?: 0
                val minutes = minutesStr.toIntOrNull() ?: 0

                if (hours < 0 || minutes < 0 || minutes > 59) {
                    Toast.makeText(context, "Ungültige Eingabe.", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val durationMillis = (hours * 60 * 60 * 1000L) + (minutes * 60 * 1000L)

                if (durationMillis <= 0) {
                     Toast.makeText(context, "Die Zeit muss größer als 0 sein.", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                val currentWeeklyAccumulated = sharedPreferences.getLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
                val newWeeklyAccumulated = currentWeeklyAccumulated + durationMillis

                sharedPreferences.edit().putLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, newWeeklyAccumulated).apply()

                updateWeeklyTimeDisplay() // Update the displayed total weekly time
                archiveAndResetIfNeeded() // Check if week needs to be archived

                Toast.makeText(context, "Zeit hinzugefügt: ${formatDuration(durationMillis)}", Toast.LENGTH_SHORT).show()
                dialog.dismiss()
            }
            .setNegativeButton("Abbrechen") { dialog, _ ->
                dialog.cancel()
            }
            .show()
    }

    internal fun formatDuration(milliseconds: Long): String {
        val hours = java.util.concurrent.TimeUnit.MILLISECONDS.toHours(milliseconds)
        val minutes = java.util.concurrent.TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60
        val seconds = java.util.concurrent.TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60
        val parts = mutableListOf<String>()
        if (hours > 0) parts.add("$hours Std.")
        if (minutes > 0) parts.add("$minutes Min.")
        // Show seconds if duration is less than a minute, or if it's exactly 0 to show "0 Sek."
        if (seconds > 0 || parts.isEmpty()) parts.add("$seconds Sek.")
        return if (parts.isEmpty()) "0 Sek." else parts.joinToString(" ")
    }

    private fun updateWeeklyTimeDisplay() {
        val accumulatedTimeMillis = sharedPreferences.getLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
        weeklyTimeTextView.text = "Wochenzeit: ${formatDuration(accumulatedTimeMillis)}"
    }

    internal fun getWeekStartDate(date: LocalDate): LocalDate {
        return date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
    }

    private fun initializeWeeklyTracking() {
        val editor = sharedPreferences.edit()
        if (!sharedPreferences.contains(PREF_TRAINING_WEEK_START_DATE)) {
            val currentWeekStart = getWeekStartDate(LocalDate.now())
            editor.putString(PREF_TRAINING_WEEK_START_DATE, currentWeekStart.toString())
            editor.putLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
            editor.apply()
        }
    }

    internal fun archiveAndResetIfNeeded() {
        val today = LocalDate.now()
        val currentActualWeekStart = getWeekStartDate(today)
        val storedWeekStartDateString = sharedPreferences.getString(PREF_TRAINING_WEEK_START_DATE, null)

        if (storedWeekStartDateString == null) {
            // This case should ideally be handled by initializeWeeklyTracking ensuring a value is always there.
            // If it still happens, re-initialize and return to avoid errors.
            initializeWeeklyTracking() // Attempt to fix missing initial values
            return
        }

        val storedWeekStartDate = LocalDate.parse(storedWeekStartDateString)

        if (currentActualWeekStart.isAfter(storedWeekStartDate)) {
            val previousWeekAccumulatedTime = sharedPreferences.getLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)

            if (previousWeekAccumulatedTime > 0L) {
                val userId = firebaseAuthManager.getCurrentUserId() ?: "unknown_user"
                val summary = WeeklyTrainingSummary.fromData(userId, storedWeekStartDate, previousWeekAccumulatedTime)
                lifecycleScope.launch {
                    try {
                        weeklyTrainingRepository.save(summary)
                        // Check if context is still available before showing Toast
                        context?.let {
                            Toast.makeText(it, "Trainingszeit der letzten Woche archiviert.", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        context?.let {
                            Toast.makeText(it, "Fehler beim Archivieren der wöchentlichen Trainingszeit: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                        // Optionally, log the error for debugging
                        // Log.e("ErgebniserfassungFragment", "Error saving weekly summary", e)
                    }
                }
            }

            val editor = sharedPreferences.edit()
            editor.putLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
            editor.putString(PREF_TRAINING_WEEK_START_DATE, currentActualWeekStart.toString())
            editor.apply()
            updateWeeklyTimeDisplay() // Update display after reset
        }
    }


    private fun startTrainingTimer() {
        if (!isTrainingRunning) {
            isTrainingRunning = true
            // If there's a previously saved base (e.g. from a pause), don't overwrite it with current time
            // Instead, calculate offset from when it was paused.
            // For a fresh start, offset is 0.
            val offset = SystemClock.elapsedRealtime() - chronometer.base
            trainingStartTime = SystemClock.elapsedRealtime() - offset // More like session start time
            chronometer.base = SystemClock.elapsedRealtime() - getCurrentChronometerOffset()
            chronometer.start()
            saveTimerState()
            updateTimerToggleButtonText() // Added call
        }
    }

    internal fun stopTrainingTimer() {
        if (isTrainingRunning) {
            chronometer.stop()
            // This is the duration of the session just ended.
            val elapsedTimeSinceLastStart = SystemClock.elapsedRealtime() - chronometer.base

            // Update weekly accumulated time
            val currentWeeklyAccumulated = sharedPreferences.getLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
            val newWeeklyAccumulated = currentWeeklyAccumulated + elapsedTimeSinceLastStart
            sharedPreferences.edit().putLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, newWeeklyAccumulated).apply()

            isTrainingRunning = false
            // Reset chronometer base for the next session AFTER calculating elapsed time
            chronometer.base = SystemClock.elapsedRealtime()
            saveTimerState(clearBase = true) // Clear the stored base for chronometer itself
            // Reset the displayed time to 00:00 for the chronometer
            chronometer.text = "00:00" // Or use a context-aware string resource
            updateTimerToggleButtonText() // Added call

            updateWeeklyTimeDisplay() // Update display after stopping timer
            // Call archiveAndResetIfNeeded in case stopping the timer crosses a week boundary.
            archiveAndResetIfNeeded()
        }
    }

    // Optional: Call this onFragmentPause or when navigating away if desired
    private fun pauseTrainingTimer() {
        if (isTrainingRunning) {
            chronometer.stop()
            // Important: Keep isTrainingRunning = true conceptually,
            // but save the current base so it can be resumed.
            // The actual isTrainingRunning flag in prefs will be updated by saveTimerState.
            saveTimerState() // Saves current base and isTrainingRunning = true
            // If isTrainingRunning is set to false on pause, then update text:
            // if (!isTrainingRunning) updateTimerToggleButtonText()
            // However, typical pause keeps isTrainingRunning true conceptually for prefs,
            // and chronometer is just stopped. The text would remain "Training stoppen".
            // If pause implies isTrainingRunning = false, then:
            // isTrainingRunning = false // if pause means it's not "running" for the button text
            // updateTimerToggleButtonText()
        }
    }

    private fun resumeTrainingTimer() {
        if (sharedPreferences.getBoolean(PREF_IS_TRAINING_RUNNING, false)) {
            isTrainingRunning = true // Ensure local state matches persisted
            chronometer.base = sharedPreferences.getLong(PREF_CHRONOMETER_BASE_TIME, SystemClock.elapsedRealtime())
            chronometer.start()
            updateTimerToggleButtonText() // Added call
        }
    }

    // This method can be deleted if no longer needed.
    // private fun updateButtonStates() {
    //     startButton.isEnabled = !isTrainingRunning
    //     stopButton.isEnabled = isTrainingRunning
    // }

    private fun saveTimerState(clearBase: Boolean = false) {
        with(sharedPreferences.edit()) {
            putBoolean(PREF_IS_TRAINING_RUNNING, isTrainingRunning)
            if (clearBase) {
                remove(PREF_CHRONOMETER_BASE_TIME) // Or putLong(PREF_CHRONOMETER_BASE_TIME, 0L)
            } else {
                // Only save base if training is running or was just paused
                if(isTrainingRunning || chronometer.base != SystemClock.elapsedRealtime()) {
                    putLong(PREF_CHRONOMETER_BASE_TIME, chronometer.base)
                } else {
                    remove(PREF_CHRONOMETER_BASE_TIME)
                }
            }
            apply()
        }
    }

    private fun loadTimerState() {
        isTrainingRunning = sharedPreferences.getBoolean(PREF_IS_TRAINING_RUNNING, false)
        val savedBaseTime = sharedPreferences.getLong(PREF_CHRONOMETER_BASE_TIME, 0L)

        if (isTrainingRunning) {
            chronometer.base = savedBaseTime
            chronometer.start()
        } else {
            // If not running, set base to current time so it shows 00:00,
            // or set it to the saved base if you want to show the last stopped time.
            // For a clean start if it was properly stopped, base should make it 00:00.
            if (savedBaseTime != 0L) { // Was paused or stopped mid-session
                 chronometer.base = savedBaseTime
            } else { // Was reset or never run
                 chronometer.base = SystemClock.elapsedRealtime()
            }
        }
        // updateButtonStates() // Removed
        updateTimerToggleButtonText() // Ensures button text is correct on initial load
    }

    private fun getCurrentChronometerOffset(): Long {
        // If training was running and paused, this would be the duration it already ran.
        // For a fresh start, this should be 0.
        // This is effectively what chronometer.base stores relative to SystemClock.elapsedRealtime()
        // If chronometer.base is set to a past SystemClock.elapsedRealtime() value, then
        // SystemClock.elapsedRealtime() - chronometer.base IS the offset.
        // When starting fresh, we want offset = 0, so chronometer.base = SystemClock.elapsedRealtime().
        // When resuming, we want chronometer.base = SystemClock.elapsedRealtime() - previouslyRunDuration.
        val savedBaseTime = sharedPreferences.getLong(PREF_CHRONOMETER_BASE_TIME, 0L)
        if (savedBaseTime != 0L && !isTrainingRunning) { // It was paused, and we are about to start
            // This logic might be tricky. If isTrainingRunning is false, but savedBaseTime is present,
            // it means it was paused. The time on the chronometer is (SystemClock.elapsedRealtime() - savedBaseTime)
            // but this is incorrect if the app was closed.
            // The base itself stores the reference.
            // Let's simplify: when resuming, loadTimerState should correctly set the base.
            // When starting, if there's a persisted base from a *running* state (e.g. app crash), restore it.
            // If it was *not* running, then any persisted base is from a *paused* state.
            return if (sharedPreferences.getBoolean(PREF_IS_TRAINING_RUNNING, false)) { // Was running before (e.g. app crash)
                SystemClock.elapsedRealtime() - savedBaseTime
            } else {
                0L // Start fresh or from a consciously stopped/paused state handled by loadTimerState
            }
        }
        return 0L // Default to no offset
    }


    // Example for weekly accumulation - to be fleshed out
    // private fun updateAccumulatedWeeklyTime(timeToAddMillis: Long) {
    //     val currentAccumulated = sharedPreferences.getLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, 0L)
    //     with(sharedPreferences.edit()) {
    //         putLong(PREF_CURRENT_WEEK_ACCUMULATED_TIME, currentAccumulated + timeToAddMillis)
    //         apply()
    //     }
    // }


    override fun onPause() {
        super.onPause()
        // If training is running, treat it as a pause
        if (isTrainingRunning) {
            // chronometer.stop() // SystemChronometer stops itself on window detach.
            // Save the state as "running" so it can be resumed.
            // The base is already correct from start/resume.
            saveTimerState()
        }
    }

    override fun onResume() {
        super.onResume()
        // loadTimerState() is already called in onViewCreated.
        // If the view is re-created, it handles it. If just resuming,
        // and if we saved state in onPause, we might need to restore it here.
        // However, typical fragment lifecycle means onViewCreated is called after onResume
        // if the view needs to be recreated. If view is still there, chronometer might still be running.
        // For simplicity, loadTimerState in onViewCreated should handle most cases.
        // Re-evaluating if chronometer needs explicit restart if it was running and fragment was just paused (not stopped/destroyed)
        if (sharedPreferences.getBoolean(PREF_IS_TRAINING_RUNNING, false)) {
            val savedBaseTime = sharedPreferences.getLong(PREF_CHRONOMETER_BASE_TIME, SystemClock.elapsedRealtime())
            // Ensure chronometer is actually running if isTrainingRunning is true
            if (chronometer.base != savedBaseTime || !isTrainingRunning) { // Second condition for local isTrainingRunning
                 chronometer.base = savedBaseTime
                 chronometer.start()
                 isTrainingRunning = true // ensure local flag is also true
            }
            // updateButtonStates() // Removed
        }
        updateWeeklyTimeDisplay() // Also update weekly time on resume
        if (::toggleTimerButton.isInitialized) { // Check if button is initialized
            updateTimerToggleButtonText()
        }
    }

    private fun loadCategories() {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                if (firebaseAuthManager.isLoggedIn()) {
                    val categories = trainingsplanManager.getExerciseCategories()
                    if (categories.isNotEmpty()) {
                        itemsList.clear()
                        itemsList.addAll(categories.map { categoryName ->
                            AssessmentItem(id = categoryName, title = categoryName)
                        })
                        assessmentAdapter.notifyDataSetChanged() // Or use DiffUtil if adapter supports it
                    } else {
                        itemsList.clear()
                        assessmentAdapter.notifyDataSetChanged()
                        Toast.makeText(context, "No exercise categories found.", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    itemsList.clear()
                    assessmentAdapter.notifyDataSetChanged()
                    Toast.makeText(context, "Please log in to see categories.", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                itemsList.clear()
                assessmentAdapter.notifyDataSetChanged()
                Toast.makeText(context, "Failed to load categories: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
}