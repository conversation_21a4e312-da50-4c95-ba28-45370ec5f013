package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.google.android.gms.tasks.Tasks
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import com.google.firebase.auth.FirebaseUser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * Manager class for Firebase Authentication
 */
class FirebaseAuthManager(private val context: Context? = null) {
    private val auth: FirebaseAuth

    companion object {
        private const val TAG = "FirebaseAuthManager"
        private const val AUTH_TIMEOUT_SECONDS = 30L
    }

    init {
        // Ensure Firebase is initialized
        if (context != null && !FirebaseApp.getApps(context).isEmpty()) {
            Log.d(TAG, "Using existing Firebase instance")
            auth = FirebaseAuth.getInstance()
        } else {
            Log.d(TAG, "Creating new Firebase Auth instance")
            auth = FirebaseAuth.getInstance()
        }

        // Check if there's a cached user
        val currentUser = auth.currentUser
        if (currentUser != null) {
            Log.d(TAG, "User already signed in: ${currentUser.email}")
        } else {
            Log.d(TAG, "No user currently signed in")
        }
    }

    /**
     * Get the current user ID or null if not logged in
     */
    fun getCurrentUserId(): String? {
        return auth.currentUser?.uid
    }

    /**
     * Get the current user or null if not logged in
     */
    fun getCurrentUser(): FirebaseUser? {
        return auth.currentUser
    }

    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        return auth.currentUser != null
    }

    /**
     * Register a new user with email and password
     */
    suspend fun registerUser(email: String, password: String): Result<FirebaseUser> = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Attempting to register user: $email")

            // Use Tasks.await with timeout to prevent indefinite waiting
            val authResult = Tasks.await(
                auth.createUserWithEmailAndPassword(email, password),
                AUTH_TIMEOUT_SECONDS,
                TimeUnit.SECONDS
            )

            authResult.user?.let {
                Log.d(TAG, "User registered successfully: ${it.uid}")
                Result.success(it)
            } ?: Result.failure(Exception("User registration failed - null user returned"))
        } catch (e: FirebaseAuthException) {
            Log.e(TAG, "Firebase Auth error registering user: ${e.errorCode} - ${e.message}")
            Result.failure(e)
        } catch (e: Exception) {
            Log.e(TAG, "Error registering user: ${e.javaClass.simpleName} - ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Login with email and password
     */
    suspend fun loginUser(email: String, password: String): Result<FirebaseUser> = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Attempting to login user: $email")

            // Use Tasks.await with timeout to prevent indefinite waiting
            val authResult = Tasks.await(
                auth.signInWithEmailAndPassword(email, password),
                AUTH_TIMEOUT_SECONDS,
                TimeUnit.SECONDS
            )

            authResult.user?.let {
                Log.d(TAG, "User logged in successfully: ${it.uid}")
                Result.success(it)
            } ?: Result.failure(Exception("Login failed - null user returned"))
        } catch (e: FirebaseAuthException) {
            Log.e(TAG, "Firebase Auth error logging in: ${e.errorCode} - ${e.message}")
            Result.failure(e)
        } catch (e: Exception) {
            Log.e(TAG, "Error logging in: ${e.javaClass.simpleName} - ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Logout the current user
     */
    fun logout() {
        Log.d(TAG, "Logging out user: ${auth.currentUser?.email}")
        auth.signOut()
    }

    /**
     * Send password reset email
     */
    suspend fun sendPasswordResetEmail(email: String): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Sending password reset email to: $email")

            // Use Tasks.await with timeout to prevent indefinite waiting
            Tasks.await(
                auth.sendPasswordResetEmail(email),
                AUTH_TIMEOUT_SECONDS,
                TimeUnit.SECONDS
            )

            Log.d(TAG, "Password reset email sent successfully")
            Result.success(Unit)
        } catch (e: FirebaseAuthException) {
            Log.e(TAG, "Firebase Auth error sending password reset: ${e.errorCode} - ${e.message}")
            Result.failure(e)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending password reset: ${e.javaClass.simpleName} - ${e.message}")
            Result.failure(e)
        }
    }
}
