package com.atom.diesnookerapp.ui.manageexercises

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition
import com.atom.diesnookerapp.databinding.ItemExerciseDefinitionBinding
// Import for the header binding - this will be generated once the XML is created
import com.atom.diesnookerapp.databinding.ItemManageExerciseCategoryHeaderBinding

// Sealed class (already added in the previous step)
sealed class ManageExerciseListItem {
    data class HeaderItem(
        val categoryId: String,
        val categoryDisplayName: String,
        val isExpanded: Boolean
    ) : ManageExerciseListItem()

    data class ExerciseDataItem(
        val exercise: UserExerciseDefinition
    ) : ManageExerciseListItem()
}

class ManageExercisesAdapter(
    private val onHeaderClick: (ManageExerciseListItem.HeaderItem) -> Unit, // Added
    private val onEditClick: (UserExerciseDefinition) -> Unit,
    private val onDeleteClick: (UserExerciseDefinition) -> Unit
) : ListAdapter<ManageExerciseListItem, RecyclerView.ViewHolder>(DiffCallback) { // Changed signature

    companion object {
        private const val VIEW_TYPE_HEADER = 0
        private const val VIEW_TYPE_EXERCISE = 1

        private val DiffCallback = object : DiffUtil.ItemCallback<ManageExerciseListItem>() {
            override fun areItemsTheSame(oldItem: ManageExerciseListItem, newItem: ManageExerciseListItem): Boolean {
                return when {
                    oldItem is ManageExerciseListItem.HeaderItem && newItem is ManageExerciseListItem.HeaderItem ->
                        oldItem.categoryId == newItem.categoryId
                    oldItem is ManageExerciseListItem.ExerciseDataItem && newItem is ManageExerciseListItem.ExerciseDataItem ->
                        oldItem.exercise.id == newItem.exercise.id
                    else -> false
                }
            }

            override fun areContentsTheSame(oldItem: ManageExerciseListItem, newItem: ManageExerciseListItem): Boolean {
                return oldItem == newItem // Relies on data class equals()
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is ManageExerciseListItem.HeaderItem -> VIEW_TYPE_HEADER
            is ManageExerciseListItem.ExerciseDataItem -> VIEW_TYPE_EXERCISE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_HEADER -> {
                val binding = ItemManageExerciseCategoryHeaderBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                CategoryHeaderViewHolder(binding)
            }
            VIEW_TYPE_EXERCISE -> {
                val binding = ItemExerciseDefinitionBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ExerciseViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)
        when (holder) {
            is CategoryHeaderViewHolder -> holder.bind(item as ManageExerciseListItem.HeaderItem, onHeaderClick)
            is ExerciseViewHolder -> holder.bind(item as ManageExerciseListItem.ExerciseDataItem, onEditClick, onDeleteClick)
        }
    }

    // New ViewHolder for Category Headers
    inner class CategoryHeaderViewHolder(private val binding: ItemManageExerciseCategoryHeaderBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(headerItem: ManageExerciseListItem.HeaderItem, onHeaderClick: (ManageExerciseListItem.HeaderItem) -> Unit) {
            binding.categoryHeaderNameTextView.text = headerItem.categoryDisplayName // Assuming this ID exists in the new layout
            binding.categoryHeaderExpandIcon.setImageResource( // Assuming this ID exists in the new layout
                if (headerItem.isExpanded) R.drawable.ic_expand_less else R.drawable.ic_expand_more
            )
            itemView.setOnClickListener {
                onHeaderClick(headerItem)
            }
        }
    }

    // Modified ExerciseViewHolder
    inner class ExerciseViewHolder(private val binding: ItemExerciseDefinitionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        // Bind method now takes ExerciseDataItem
        fun bind(
            exerciseDataItem: ManageExerciseListItem.ExerciseDataItem, // Changed parameter type
            onEditClick: (UserExerciseDefinition) -> Unit,
            onDeleteClick: (UserExerciseDefinition) -> Unit
        ) {
            val exercise = exerciseDataItem.exercise // Extract the actual UserExerciseDefinition
            binding.exerciseNameTextView.text = exercise.name
            binding.exerciseCategoryTextView.text = exercise.category // This could be removed if category is only in header

            binding.editExerciseButton.setOnClickListener {
                onEditClick(exercise)
            }

            binding.deleteExerciseButton.visibility = View.VISIBLE
            binding.deleteExerciseButton.setOnClickListener {
                onDeleteClick(exercise)
            }
        }
    }
}
