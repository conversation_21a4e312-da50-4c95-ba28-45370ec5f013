package com.atom.diesnookerapp.ui.ergebniserfassung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

class DateAdapter(
    private var dates: List<LocalDate>,
    private val onDateSelected: (LocalDate) -> Unit
) : RecyclerView.Adapter<DateAdapter.ViewHolder>() {

    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val dateText: TextView = view.findViewById(R.id.dateText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_date, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val date = dates[position]
        holder.dateText.text = date.format(dateFormatter)
        holder.cardView.setOnClickListener { onDateSelected(date) }
    }

    override fun getItemCount() = dates.size

    fun updateDates(newDates: List<LocalDate>) {
        dates = newDates
        notifyDataSetChanged()
    }
} 