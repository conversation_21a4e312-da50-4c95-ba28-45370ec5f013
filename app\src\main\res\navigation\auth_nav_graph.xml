<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/auth_nav_graph"
    app:startDestination="@id/loginFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.atom.diesnookerapp.ui.auth.LoginFragment"
        android:label="Login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_registerFragment"
            app:destination="@id/registerFragment" />
        <action
            android:id="@+id/action_loginFragment_to_homeFragment"
            app:destination="@id/navigation_selbsteinschaetzung"
            app:popUpTo="@id/auth_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/registerFragment"
        android:name="com.atom.diesnookerapp.ui.auth.RegisterFragment"
        android:label="Register"
        tools:layout="@layout/fragment_register">
        <action
            android:id="@+id/action_registerFragment_to_homeFragment"
            app:destination="@id/navigation_selbsteinschaetzung"
            app:popUpTo="@id/auth_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>

    <!-- Include the main navigation destinations -->
    <include app:graph="@navigation/nav_graph" />
</navigation>
