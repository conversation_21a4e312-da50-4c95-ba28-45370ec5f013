"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";

function TrainerDashboardPage() {
  const { currentUser, userProfile } = useAuth();

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Trainer Dashboard Home</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-lg text-gray-700">
          Welcome, <span className="font-semibold">{userProfile?.displayName || currentUser?.email}</span>!
        </p>
        <p className="text-md text-gray-600 mt-2">
          This is your dashboard to manage athletes, view their progress, and handle connection requests.
        </p>
        <p className="text-md text-gray-600 mt-1">
          Use the navigation on the left to access different sections.
        </p>
        {/* Summary widgets or quick links can be added here later */}
      </div>
    </div>
  );
}

// Protect this page, only TRAINER or MENTAL_TRAINER roles allowed.
// Redirect to trainer login if not authorized.
export default withAuth(TrainerDashboardPage, {
  roles: ['TRAINER', 'MENTAL_TRAINER'],
  redirectTo: '/trainer/login'
});
