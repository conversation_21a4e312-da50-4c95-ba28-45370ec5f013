"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, ComponentType } from 'react';
import LoadingSpinner from '@/components/ui/LoadingSpinner'; // We'll create this next

interface WithAuthOptions {
  roles?: string[]; // e.g., ['TRAINER', 'MENTAL_TRAINER']
  redirectTo?: string; // Page to redirect to if not authenticated/authorized
}

export default function withAuth<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const WithAuthComponent = (props: P) => {
    const { currentUser, userProfile, loading } = useAuth();
    const router = useRouter();
    const { roles, redirectTo } = options;

    useEffect(() => {
      if (loading) {
        return; // Wait for auth state to load
      }

      if (!currentUser) {
        router.replace(redirectTo || '/login'); // Default to player login
        return;
      }

      // Role check if roles are specified
      if (roles && roles.length > 0) {
        if (!userProfile || !roles.includes(userProfile.role)) {
          // If roles are specified and user doesn't have one, redirect.
          // This could be to a generic access denied page or back to their default dashboard/login.
          // For simplicity, redirecting to player login if trainer role check fails.
          router.replace(redirectTo || (userProfile?.role === 'PLAYER' ? '/dashboard' : '/login'));
        }
      }

    }, [currentUser, userProfile, loading, router, roles, redirectTo]);

    if (loading || !currentUser) {
      // Show a loading spinner or a blank page while checking auth
      return <LoadingSpinner />;
    }

    // Additional role check before rendering the component, in case useEffect hasn't run yet or for strictness
    if (roles && roles.length > 0 && (!userProfile || !roles.includes(userProfile.role))) {
        // This check ensures that if the user is logged in but doesn't have the required role,
        // they don't briefly see the component content.
        // It might be redundant if useEffect handles redirection quickly, but adds robustness.
        return <LoadingSpinner />; // Or an "Access Denied" component
    }


    return <WrappedComponent {...props} />;
  };

  // Set a display name for the HOC for better debugging
  WithAuthComponent.displayName = `WithAuth(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return WithAuthComponent;
}
