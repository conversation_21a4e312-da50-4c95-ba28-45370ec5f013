<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="4dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/taskTitleTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/editTaskButton"
            android:layout_marginEnd="8dp"
            tools:text="Task Title Sample" />

        <TextView
            android:id="@+id/taskCategoryTextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/taskTitleTextView"
            app:layout_constraintEnd_toStartOf="@+id/editTaskButton"
            android:layout_marginEnd="8dp"
            tools:text="Category: Sample Category" />

        <TextView
            android:id="@+id/taskFrequencyTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/taskCategoryTextView"
            tools:text="Frequency: Daily" />

        <TextView
            android:id="@+id/taskPointsTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintStart_toEndOf="@id/taskFrequencyTextView"
            app:layout_constraintTop_toTopOf="@id/taskFrequencyTextView"
            app:layout_constraintBottom_toBottomOf="@id/taskFrequencyTextView"
            tools:text="Points: 10" />

        <ImageButton
            android:id="@+id/editTaskButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Edit Task"
            android:src="@android:drawable/ic_menu_edit"
            app:layout_constraintEnd_toStartOf="@+id/deleteTaskButton"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?attr/colorControlNormal"/>

        <ImageButton
            android:id="@+id/deleteTaskButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Delete Task"
            android:src="@android:drawable/ic_menu_delete"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?attr/colorControlNormal" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
