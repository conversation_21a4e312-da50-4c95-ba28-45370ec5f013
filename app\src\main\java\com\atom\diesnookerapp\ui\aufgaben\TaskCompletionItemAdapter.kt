package com.atom.diesnookerapp.ui.aufgaben

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

// Sealed class for different item types
sealed class TaskCompletionItem {
    data class Header(val categoryName: String) : TaskCompletionItem()
    data class Task(val taskCompletion: TaskCompletionSummary) : TaskCompletionItem()
}

class TaskCompletionItemAdapter(
    private val items: List<TaskCompletionItem>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_HEADER = 0
        private const val VIEW_TYPE_TASK = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (items[position]) {
            is TaskCompletionItem.Header -> VIEW_TYPE_HEADER
            is TaskCompletionItem.Task -> VIEW_TYPE_TASK
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_HEADER -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_task_completion_header, parent, false)
                HeaderViewHolder(view)
            }
            VIEW_TYPE_TASK -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_task_completion, parent, false)
                TaskViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = items[position]) {
            is TaskCompletionItem.Header -> {
                val headerHolder = holder as HeaderViewHolder
                headerHolder.categoryNameText.text = item.categoryName
            }
            is TaskCompletionItem.Task -> {
                val taskHolder = holder as TaskViewHolder
                val task = item.taskCompletion
                taskHolder.taskTitleText.text = task.title
                
                // For the history view, we'll use the original maxCompletions value
                // which should already be correctly set in TaskHistoryEntry creation
                taskHolder.completionText.text = "${task.completions}/${task.maxCompletions}"
                
                // Hide the points text
                taskHolder.pointsText.visibility = View.GONE
            }
        }
    }

    override fun getItemCount() = items.size

    class HeaderViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val categoryNameText: TextView = view.findViewById(R.id.categoryNameText)
    }

    class TaskViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val taskTitleText: TextView = view.findViewById(R.id.taskTitleText)
        val completionText: TextView = view.findViewById(R.id.completionText)
        val pointsText: TextView = view.findViewById(R.id.pointsText)
    }
}