"use client";

import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import withAuth from '@/components/auth/withAuth';
import { useAuth } from '@/context/AuthContext';
import { db } from '@/lib/firebase';
import { doc, getDoc, collection, query, where, orderBy, getDocs, Timestamp } from 'firebase/firestore';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Link from 'next/link';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

// Interfaces (can be moved to a types file later)
interface UserProfile {
  userId: string;
  email: string;
  displayName: string;
  role: string; // PLAYER, TRAINER, MENTAL_TRAINER
  lastUpdated?: Timestamp;
}

interface UserConnection {
  id: string;
  initiatorId: string; // Athlete's UID
  targetId: string;    // Trainer's UID
  status: 'PENDING' | 'ACTIVE' | 'DECLINED' | 'REMOVED';
  trainerType: 'TRAINER' | 'MENTAL_TRAINER'; // Type of role trainer is fulfilling for this connection
  permissions: {
    exerciseAccess: boolean;
    selfAssessmentAccess: boolean;
  };
  createdAt: Timestamp;
}

// Data record interfaces (simplified, match structures from player section)
interface TrainingRecordItem { title: string; score: number; }
interface TrainingRecord { id: string; date: string; type: string; items: TrainingRecordItem[]; lastUpdated: Timestamp; }
interface QuestionRecordItem { title: string; answer: string; }
interface QuestionRecord { id: string; date: string; type: string; questions: QuestionRecordItem[]; lastUpdated: Timestamp; }
interface UserExerciseDefinition { id: string; userId: string; name: string; category: string; description?: string; exerciseType: string; }
interface ExerciseRecord { id: string; userId: string; exerciseId: string; timestamp: Timestamp; score?: number; timeInMinutes?: number; }
interface TaskHistoryEntry { id: string; weekKey: string; totalPoints: number; taskCompletions: any[]; lastUpdated: Timestamp; }
interface TrainingsplanHistoryEntry { id: string; weekStartDate: string; items: any[]; lastUpdated: Timestamp; }


function AthleteDetailPage() {
  const params = useParams();
  const athleteId = params.athleteId as string;
  const router = useRouter();
  const { currentUser, userProfile: currentTrainerProfile } = useAuth();

  const [athleteProfile, setAthleteProfile] = useState<UserProfile | null>(null);
  const [connectionDetails, setConnectionDetails] = useState<UserConnection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Athlete's data states
  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>([]);
  const [questionRecords, setQuestionRecords] = useState<QuestionRecord[]>([]);
  const [exerciseDefinitions, setExerciseDefinitions] = useState<UserExerciseDefinition[]>([]);
  const [exerciseRecords, setExerciseRecords] = useState<ExerciseRecord[]>([]);
  const [taskHistory, setTaskHistory] = useState<TaskHistoryEntry[]>([]);
  const [trainingsplanHistory, setTrainingsplanHistory] = useState<TrainingsplanHistoryEntry[]>([]);

  const [showRawData, setShowRawData] = useState(false);


  useEffect(() => {
    if (!currentUser || !athleteId) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // 1. Fetch Athlete's Profile
        const athleteDocRef = doc(db, "user_profiles", athleteId);
        const athleteDocSnap = await getDoc(athleteDocRef);
        if (!athleteDocSnap.exists()) throw new Error("Athlete profile not found.");
        const fetchedAthleteProfile = athleteDocSnap.data() as UserProfile;
        setAthleteProfile(fetchedAthleteProfile);

        // 2. Fetch Connection Details (to check permissions)
        const connQuery = query(
          collection(db, "user_connections"),
          where("initiatorId", "==", athleteId),
          where("targetId", "==", currentUser.uid),
          where("status", "==", "ACTIVE")
        );
        const connSnapshot = await getDocs(connQuery);
        if (connSnapshot.empty) throw new Error("No active connection found with this athlete or permissions missing.");
        const fetchedConnectionDetails = {id: connSnapshot.docs[0].id, ...connSnapshot.docs[0].data()} as UserConnection;
        setConnectionDetails(fetchedConnectionDetails);

        // 3. Fetch data based on permissions and trainer role
        const canViewSelfAssessment = fetchedConnectionDetails.permissions.selfAssessmentAccess || currentTrainerProfile?.role === 'MENTAL_TRAINER';
        const canViewExercises = fetchedConnectionDetails.permissions.exerciseAccess || currentTrainerProfile?.role === 'TRAINER';

        if (canViewSelfAssessment) {
          const trQuery = query(collection(db, "training_records"), where("userId", "==", athleteId), orderBy("lastUpdated", "desc"));
          const trSnap = await getDocs(trQuery);
          setTrainingRecords(trSnap.docs.map(d => ({ id: d.id, ...d.data() } as TrainingRecord)));

          const qrQuery = query(collection(db, "question_records"), where("userId", "==", athleteId), orderBy("lastUpdated", "desc"));
          const qrSnap = await getDocs(qrQuery);
          setQuestionRecords(qrSnap.docs.map(d => ({ id: d.id, ...d.data() } as QuestionRecord)));
        }

        if (canViewExercises) {
          const exDefQuery = query(collection(db, "user_exercise_definitions"), where("userId", "==", athleteId));
          const exDefSnap = await getDocs(exDefQuery);
          setExerciseDefinitions(exDefSnap.docs.map(d => ({ id: d.id, ...d.data() } as UserExerciseDefinition)));

          const exRecQuery = query(collection(db, "exercise_records"), where("userId", "==", athleteId), orderBy("timestamp", "desc"));
          const exRecSnap = await getDocs(exRecQuery);
          setExerciseRecords(exRecSnap.docs.map(d => ({ id: d.id, ...d.data() } as ExerciseRecord)));

          const taskQuery = query(collection(db, "task_history"), where("userId", "==", athleteId), orderBy("lastUpdated", "desc"));
          const taskSnap = await getDocs(taskQuery);
          setTaskHistory(taskSnap.docs.map(d => ({ id: d.id, ...d.data() } as TaskHistoryEntry)));

          const planQuery = query(collection(db, "trainingsplan_history"), where("userId", "==", athleteId), orderBy("lastUpdated", "desc"));
          const planSnap = await getDocs(planQuery);
          setTrainingsplanHistory(planSnap.docs.map(d => ({ id: d.id, ...d.data() } as TrainingsplanHistoryEntry)));
        }

      } catch (err: any) {
        console.error("Error fetching athlete details:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [currentUser, athleteId, currentTrainerProfile?.role]);

  // Charting functions (simplified, can be expanded)
  const getGenericLineChartData = (records: Array<{lastUpdated?: Timestamp, date?: string, items?: Array<{score: number}>, score?: number, timestamp?: Timestamp}>, label: string, valueExtractor: (record: any) => number) => {
    const sortedRecords = [...records].sort((a, b) => (a.lastUpdated || a.timestamp || Timestamp.now()).toMillis() - (b.lastUpdated || b.timestamp || Timestamp.now()).toMillis());
    return {
      labels: sortedRecords.map(r => new Date((r.lastUpdated || r.timestamp || Timestamp.now()).toDate()).toLocaleDateString('de-DE')),
      datasets: [{ label, data: sortedRecords.map(valueExtractor), fill: false, borderColor: 'rgb(75, 192, 192)', tension: 0.1 }],
    };
  };

  const getExerciseChartData = (records: ExerciseRecord[], exerciseName: string) => {
    const filteredRecords = records
      .filter(r => typeof r.score === 'number')
      .sort((a, b) => {
        // Defensive check for timestamp and toMillis method
        const timeA = a.timestamp && typeof a.timestamp.toMillis === 'function' ? a.timestamp.toMillis() : 0;
        const timeB = b.timestamp && typeof b.timestamp.toMillis === 'function' ? b.timestamp.toMillis() : 0;
        // If timestamps were numbers (e.g. from Date.now()), this would also work:
        // const timeA = a.timestamp instanceof Timestamp ? a.timestamp.toMillis() : Number(a.timestamp || 0);
        // const timeB = b.timestamp instanceof Timestamp ? b.timestamp.toMillis() : Number(b.timestamp || 0);
        return timeA - timeB;
      });

    return {
      labels: filteredRecords.map(r => {
        if (r.timestamp && typeof r.timestamp.toDate === 'function') {
          return new Date(r.timestamp.toDate()).toLocaleDateString('de-DE');
        }
        // Fallback if timestamp is already a number (milliseconds) or other convertible format
        if (typeof r.timestamp === 'number' || r.timestamp instanceof Date) {
          return new Date(r.timestamp).toLocaleDateString('de-DE');
        }
        return 'Invalid Date'; // Fallback for unhandled types
      }),
      datasets: [
        { label: `Score: ${exerciseName}`, data: filteredRecords.map(r => r.score), fill: false, borderColor: 'rgb(238, 187, 195)', tension: 0.1, },
      ],
    };
  };

  const chartOptions = (titleText: string) => ({
    responsive: true, maintainAspectRatio: false, plugins: { legend: { display: true }, title: { display: true, text: titleText } },
    scales: { y: { beginAtZero: true } }
  });


  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;
  if (!athleteProfile || !connectionDetails) return <div className="text-orange-500 p-4">Athlete or connection data not available.</div>;

  const canViewSelfAssessment = connectionDetails.permissions.selfAssessmentAccess || currentTrainerProfile?.role === 'MENTAL_TRAINER';
  const canViewExercises = connectionDetails.permissions.exerciseAccess || currentTrainerProfile?.role === 'TRAINER';

  const allDataForRawView = {
      profile: athleteProfile,
      connection: connectionDetails,
      trainingRecords: canViewSelfAssessment ? trainingRecords : undefined,
      questionRecords: canViewSelfAssessment ? questionRecords : undefined,
      exerciseDefinitions: canViewExercises ? exerciseDefinitions : undefined,
      exerciseRecords: canViewExercises ? exerciseRecords : undefined,
      taskHistory: canViewExercises ? taskHistory : undefined,
      trainingsplanHistory: canViewExercises ? trainingsplanHistory : undefined,
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          Athlete: {athleteProfile.displayName || athleteProfile.email}
        </h1>
        <button
            onClick={() => setShowRawData(!showRawData)}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"
        >
            {showRawData ? "Hide" : "Show"} Raw Data
        </button>
      </div>
      <p className="text-sm text-gray-600 mb-1">Role: {athleteProfile.role}</p>
      <p className="text-sm text-gray-600 mb-4">
        Your connection: {connectionDetails.trainerType} (
        {connectionDetails.permissions.exerciseAccess && "Exercises, "}
        {connectionDetails.permissions.selfAssessmentAccess && "Self-Assessment"}
        )
      </p>

    {showRawData && (
        <div className="bg-gray-50 p-4 rounded-lg shadow my-6">
            <h2 className="text-xl font-semibold mb-2 text-gray-700">Raw Data (JSON)</h2>
            <pre className="text-xs bg-white p-3 rounded overflow-auto max-h-96">
                {JSON.stringify(allDataForRawView, (key, value) => {
                    if (value && value.toDate) { // Convert Firestore Timestamps
                        return value.toDate().toISOString();
                    }
                    return value;
                }, 2)}
            </pre>
        </div>
    )}

      {/* Data Sections - Conditionally Rendered */}
      {canViewSelfAssessment && (
        <section className="mb-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Self-Assessment Data</h2>
          {trainingRecords.length > 0 ? (
            <div className="h-80 mb-6"><Line options={chartOptions('Avg. Training Score')} data={getGenericLineChartData(trainingRecords, 'Avg Score', r => r.items.reduce((s, i) => s + i.score, 0) / (r.items.length || 1))} /></div>
          ) : <p>No training records for chart.</p>}
          <h3 className="text-lg font-medium mb-2">Training Records ({trainingRecords.length})</h3>
          {/* List training records here */}
          <h3 className="text-lg font-medium mb-2 mt-4">Question Records ({questionRecords.length})</h3>
          {/* List question records here */}
        </section>
      )}

      {canViewExercises && (
        <section className="mb-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Exercise & Performance Data</h2>
          {exerciseDefinitions.map(def => {
            const recordsForDef = exerciseRecords.filter(r => r.exerciseId === def.id && typeof r.score === 'number');
            return recordsForDef.length > 1 ? (
              <div key={def.id} className="h-64 mb-4">
                <Line options={chartOptions(`Exercise: ${def.name}`)} data={getExerciseChartData(recordsForDef, def.name)} />
              </div>
            ) : null;
          })}
           <h3 className="text-lg font-medium mb-2">Exercise Definitions ({exerciseDefinitions.length})</h3>
           {/* List exercise definitions */}
           <h3 className="text-lg font-medium mb-2 mt-4">Exercise Records ({exerciseRecords.length})</h3>
           {/* List exercise records */}
           <h3 className="text-lg font-medium mb-2 mt-4">Task History ({taskHistory.length})</h3>
           {/* List task history */}
           <h3 className="text-lg font-medium mb-2 mt-4">Training Plan History ({trainingsplanHistory.length})</h3>
           {/* List training plan history */}
        </section>
      )}

      {!canViewSelfAssessment && !canViewExercises && (
        <p className="text-gray-600">You do not have permission to view detailed data for this athlete, or the athlete has no data.</p>
      )}
       <Link href="/trainer/dashboard/athletes" legacyBehavior>
        <a className="mt-8 inline-block px-6 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700">
          &larr; Back to Athletes List
        </a>
      </Link>
    </div>
  );
}

export default withAuth(AthleteDetailPage, {
  roles: ['TRAINER', 'MENTAL_TRAINER'],
  redirectTo: '/trainer/login',
});
