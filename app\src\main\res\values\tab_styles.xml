<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base TabLayout style -->
    <style name="CustomTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabBackground">@color/white</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabSelectedTextColor">?attr/colorPrimary</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Snooker theme TabLayout style -->
    <style name="CustomTabLayout.Snooker">
        <item name="tabBackground">@color/snooker_card_background_light</item>
        <item name="tabIndicatorColor">@color/snooker_red_500</item>
        <item name="tabSelectedTextColor">@color/snooker_red_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Blue theme TabLayout style -->
    <style name="CustomTabLayout.Blue">
        <item name="tabBackground">@color/blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/blue_500</item>
        <item name="tabSelectedTextColor">@color/blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Dark Blue theme TabLayout style -->
    <style name="CustomTabLayout.DarkBlue">
        <item name="tabBackground">@color/dark_blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/dark_blue_500</item>
        <item name="tabSelectedTextColor">@color/dark_blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Dark theme TabLayout style -->
    <style name="CustomTabLayout.Dark">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>

    <!-- Ocean theme TabLayout style -->
    <style name="CustomTabLayout.Ocean">
        <item name="tabBackground">@color/ocean_card_background_light</item>
        <item name="tabIndicatorColor">@color/ocean_medium</item>
        <item name="tabSelectedTextColor">@color/ocean_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Crimson theme TabLayout style -->
    <style name="CustomTabLayout.Crimson">
        <item name="tabBackground">@color/crimson_card_background_light</item>
        <item name="tabIndicatorColor">@color/crimson_medium</item>
        <item name="tabSelectedTextColor">@color/crimson_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style>

    <!-- Neon theme TabLayout style -->
    <style name="CustomTabLayout.Neon">
        <item name="tabBackground">@color/neon_card_background_light</item>
        <item name="tabIndicatorColor">@color/neon_purple_3</item>
        <item name="tabSelectedTextColor">@color/neon_purple_3</item>
        <item name="tabTextColor">@color/black</item>
    </style>
</resources>
