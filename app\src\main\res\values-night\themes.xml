<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <!-- Snooker Theme (Night) -->
    <style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_200</item>
        <item name="colorPrimaryVariant">@color/snooker_red_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/snooker_green_200</item>
        <item name="colorSecondaryVariant">@color/snooker_green_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_700</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/snooker_green_700</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style>

    <style name="SnookerCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Blue Theme (Night) -->
    <style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/blue_200</item>
        <item name="colorPrimaryVariant">@color/blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/orange_200</item>
        <item name="colorSecondaryVariant">@color/orange_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/BlueCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style>

    <style name="BlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Dark Blue Theme (Night) -->
    <style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_200</item>
        <item name="colorPrimaryVariant">@color/dark_blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/cyan_200</item>
        <item name="colorSecondaryVariant">@color/cyan_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style>

    <style name="DarkBlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Ocean Theme (Night) -->
    <style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/ocean_lightest</item>
        <item name="colorPrimaryVariant">@color/ocean_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/ocean_medium</item>
        <item name="colorSecondaryVariant">@color/ocean_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/ocean_dark</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="materialCardViewStyle">@style/OceanCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="OceanCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Crimson Theme (Night) -->
    <style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/crimson_lightest</item>
        <item name="colorPrimaryVariant">@color/crimson_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/crimson_medium</item>
        <item name="colorSecondaryVariant">@color/crimson_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/crimson_dark</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="CrimsonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Neon Theme (Night) -->
    <style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_2</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/neon_pink_1</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_purple_1</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/neon_purple_1</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="materialCardViewStyle">@style/NeonCardStyleDark</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="NeonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
</resources>
