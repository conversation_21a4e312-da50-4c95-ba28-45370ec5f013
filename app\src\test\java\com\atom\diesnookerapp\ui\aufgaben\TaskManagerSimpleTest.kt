package com.atom.diesnookerapp.ui.aufgaben

import org.junit.Test
import org.junit.Assert.*
import org.threeten.bp.LocalDate

class TaskManagerSimpleTest {
    
    @Test
    fun testTaskCreation() {
        val task = Task(
            id = "test1",
            title = "Test Task",
            description = "Test Description",
            frequency = TaskFrequency.DAILY,
            points = 10,
            category = "Test",
            weeklyFrequencyCount = null,
            completions = mutableListOf(),
            customPoints = mutableMapOf()
        )
        
        assertEquals("test1", task.id)
        assertEquals("Test Task", task.title)
        assertEquals(TaskFrequency.DAILY, task.frequency)
        assertEquals(10, task.points)
    }
    
    @Test
    fun testTaskFrequencyEnum() {
        assertEquals("DAILY", TaskFrequency.DAILY.name)
        assertEquals("WEEKLY", TaskFrequency.WEEKLY.name)
    }
    
    @Test
    fun testLocalDateSerialization() {
        val serializer = TaskManager.LocalDateSerializer()
        val date = LocalDate.of(2023, 10, 15)
        val jsonElement = serializer.serialize(date, null, null)
        
        assertEquals("2023-10-15", jsonElement.asString)
    }
    
    @Test
    fun testLocalDateDeserialization() {
        val deserializer = TaskManager.LocalDateDeserializer()
        val jsonElement = com.google.gson.JsonPrimitive("2023-10-15")
        val date = deserializer.deserialize(jsonElement, null, null)
        
        assertEquals(LocalDate.of(2023, 10, 15), date)
    }
}
