package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.slider.Slider

class TrainingAssessmentAdapter(
    private val items: List<TrainingAssessmentItem>,
    private val onScoreChanged: (TrainingAssessmentItem) -> Unit
) : RecyclerView.Adapter<TrainingAssessmentAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val questionText: TextView = view.findViewById(R.id.descriptionText)
        val scoreSlider: Slider = view.findViewById(R.id.scoreSlider)
        val scoreText: TextView = view.findViewById(R.id.scoreText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_training_assessment, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.questionText.text = item.title
        holder.scoreSlider.value = item.score?.toFloat() ?: 0f
        holder.scoreText.text = item.score?.toString() ?: "Keine Bewertung"

        holder.scoreSlider.clearOnChangeListeners()
        
        holder.scoreSlider.addOnChangeListener { _, value, fromUser ->
            if (fromUser) {
                val currentItem = items[position]
                currentItem.score = value.toInt()
                holder.scoreText.text = "${currentItem.score}/10"
                onScoreChanged(currentItem)
            }
        }
    }

    override fun getItemCount() = items.size
} 