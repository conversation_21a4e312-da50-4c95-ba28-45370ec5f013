package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

class HistoryDetailFragment : Fragment() {
    private lateinit var trainingPreferences: TrainingPreferences
    private lateinit var questionPreferences: QuestionPreferences
    private var recyclerView: RecyclerView? = null
    private var dateText: TextView? = null
    private var selectedDate: LocalDate? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString("date")?.let {
            selectedDate = LocalDate.parse(it)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_history_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())
        questionPreferences = QuestionPreferences(requireContext())

        dateText = view.findViewById(R.id.dateText)
        recyclerView = view.findViewById(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        selectedDate?.let { date ->
            dateText?.text = date.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
            
            val trainingRecords = trainingPreferences.getRecords()
                .filter { it.date == date }
            val questionRecords = questionPreferences.getRecords()
                .filter { it.date == date }

            val adapter = HistoryDetailAdapter(trainingRecords, questionRecords)
            recyclerView?.adapter = adapter
        }
    }

    companion object {
        fun newInstance(date: LocalDate): HistoryDetailFragment {
            return HistoryDetailFragment().apply {
                arguments = Bundle().apply {
                    putString("date", date.toString())
                }
            }
        }
    }
} 