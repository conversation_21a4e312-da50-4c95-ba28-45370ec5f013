package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.databinding.FragmentGenericExerciseListBinding
import com.atom.diesnookerapp.ui.trainingsplan.ExerciseItem
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import kotlinx.coroutines.launch

class GenericExerciseListFragment : Fragment() {

    private var _binding: FragmentGenericExerciseListBinding? = null
    private val binding get() = _binding!!

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var firebaseAuthManager: FirebaseAuthManager
    private lateinit var exerciseAdapter: BreakbuildingAdapter // Reusing BreakbuildingAdapter
    private var exerciseList = mutableListOf<ExerciseItem>()
    private var categoryName: String? = null

    private companion object {
        private const val TAG = "GenericExerciseList"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            categoryName = it.getString("categoryName")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGenericExerciseListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        firebaseAuthManager = FirebaseAuthManager()

        binding.categoryTitleTextView.text = categoryName ?: getString(R.string.unknown_category)

        val exercisePreferences = ExercisePreferences(requireContext()) // Needed by BreakbuildingAdapter
        exerciseAdapter = BreakbuildingAdapter(exerciseList, exercisePreferences) { selectedExercise ->
            // Navigate to ExerciseDetailFragment
            // Ensure action_genericExerciseListFragment_to_exerciseDetailFragment is defined in your nav graph
            findNavController().navigate(
                R.id.action_genericExerciseListFragment_to_exerciseDetailFragment,
                bundleOf(
                    "exerciseId" to selectedExercise.id,
                    "exerciseTitle" to selectedExercise.name,
                    "exerciseType" to selectedExercise.exerciseType
                )
            )
        }

        binding.recyclerView.apply { // Changed from genericExercisesRecyclerView
            layoutManager = LinearLayoutManager(requireContext())
            adapter = exerciseAdapter
        }

        loadExercises()
    }

    private fun loadExercises() {
        if (categoryName == null) {
            Log.w(TAG, "Category name is null, cannot load exercises.")
            Toast.makeText(context, getString(R.string.category_not_specified_toast), Toast.LENGTH_LONG).show()
            // binding.emptyViewGeneric removed
            // binding.recyclerView.visibility = View.GONE // RecyclerView will be empty by default
            return
        }

        if (!firebaseAuthManager.isLoggedIn()) {
            Toast.makeText(context, getString(R.string.please_log_in_to_load_exercises_toast), Toast.LENGTH_SHORT).show()
            // binding.emptyViewGeneric removed
            // binding.recyclerView.visibility = View.GONE
            exerciseList.clear()
            exerciseAdapter.notifyDataSetChanged()
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                Log.d(TAG, "Loading exercises for category: $categoryName")
                val fetchedExercises = trainingsplanManager.getExercisesForCategory(categoryName!!)
                exerciseList.clear()
                if (fetchedExercises.isNotEmpty()) {
                    exerciseList.addAll(fetchedExercises)
                    // binding.emptyViewGeneric.visibility = View.GONE // Removed
                    // binding.recyclerView.visibility = View.VISIBLE // RecyclerView will show items if list is not empty
                } else {
                    Log.d(TAG, "No exercises found for category: $categoryName")
                    Toast.makeText(context, getString(R.string.no_exercises_in_category_toast, categoryName), Toast.LENGTH_SHORT).show() // Using specific toast
                    // binding.emptyViewGeneric.text = getString(R.string.no_exercises_in_category_generic) // Removed
                    // binding.emptyViewGeneric.visibility = View.VISIBLE // Removed
                    // binding.recyclerView.visibility = View.GONE // RecyclerView will be empty
                }
                exerciseAdapter.notifyDataSetChanged()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load exercises for $categoryName: ${e.message}", e)
                Toast.makeText(context, getString(R.string.error_loading_exercises_toast), Toast.LENGTH_LONG).show()
                exerciseList.clear()
                exerciseAdapter.notifyDataSetChanged()
                // binding.emptyViewGeneric.text = getString(R.string.error_loading_exercises_toast) // Removed
                // binding.emptyViewGeneric.visibility = View.VISIBLE // Removed
                // binding.recyclerView.visibility = View.GONE // RecyclerView will be empty
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
