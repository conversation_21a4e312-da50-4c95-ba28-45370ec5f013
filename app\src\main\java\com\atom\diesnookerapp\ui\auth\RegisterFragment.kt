package com.atom.diesnookerapp.ui.auth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Button
import android.widget.ProgressBar
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.FirebaseUserProfile
import com.atom.diesnookerapp.data.firebase.SyncManager
import com.atom.diesnookerapp.data.firebase.UserProfileRepository
import com.atom.diesnookerapp.data.firebase.UserRole
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

class RegisterFragment : Fragment() {

    private lateinit var emailEditText: TextInputEditText
    private lateinit var nameEditText: TextInputEditText
    private lateinit var roleDropdown: AutoCompleteTextView
    private lateinit var passwordEditText: TextInputEditText
    private lateinit var confirmPasswordEditText: TextInputEditText
    private lateinit var registerButton: Button
    private lateinit var loginButton: Button
    private lateinit var progressBar: ProgressBar

    private lateinit var authManager: FirebaseAuthManager
    private lateinit var userProfileRepository: UserProfileRepository
    private var selectedRole: UserRole = UserRole.PLAYER

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_register, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize repositories and managers
        authManager = FirebaseAuthManager(requireContext())
        userProfileRepository = UserProfileRepository(requireContext())

        // Initialize views
        emailEditText = view.findViewById(R.id.emailEditText)
        nameEditText = view.findViewById(R.id.nameEditText)
        roleDropdown = view.findViewById(R.id.roleDropdown)
        passwordEditText = view.findViewById(R.id.passwordEditText)
        confirmPasswordEditText = view.findViewById(R.id.confirmPasswordEditText)
        registerButton = view.findViewById(R.id.registerButton)
        loginButton = view.findViewById(R.id.loginButton)
        progressBar = view.findViewById(R.id.progressBar)

        // Set up role dropdown
        setupRoleDropdown()

        // Set up click listeners
        registerButton.setOnClickListener {
            register()
        }

        loginButton.setOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun setupRoleDropdown() {
        val roles = UserRole.values()
        val roleNames = roles.map { getRoleDisplayName(it) }

        val adapter = ArrayAdapter(requireContext(), R.layout.dropdown_item, roleNames)
        roleDropdown.setAdapter(adapter)

        // Set default selection to PLAYER
        roleDropdown.setText(getRoleDisplayName(UserRole.PLAYER), false)

        roleDropdown.setOnItemClickListener { _, _, position, _ ->
            selectedRole = roles[position]
        }
    }

    private fun getRoleDisplayName(role: UserRole): String {
        return when (role) {
            UserRole.PLAYER -> "Spieler"
            UserRole.TRAINER -> "Trainer"
            UserRole.MENTAL_TRAINER -> "Mental Trainer"
        }
    }

    private fun register() {
        val email = emailEditText.text.toString().trim()
        val name = nameEditText.text.toString().trim()
        val password = passwordEditText.text.toString().trim()
        val confirmPassword = confirmPasswordEditText.text.toString().trim()

        // Validate input
        if (email.isEmpty() || name.isEmpty() || password.isEmpty() || confirmPassword.isEmpty()) {
            Toast.makeText(
                requireContext(),
                "Bitte fülle alle Felder aus",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        if (password != confirmPassword) {
            Toast.makeText(
                requireContext(),
                "Passwörter stimmen nicht überein",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        if (password.length < 6) {
            Toast.makeText(
                requireContext(),
                "Passwort muss mindestens 6 Zeichen lang sein",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        // Show progress
        setLoading(true)

        // Attempt registration
        lifecycleScope.launch {
            val result = authManager.registerUser(email, password)

            if (result.isSuccess) {
                // Create user profile
                val userId = authManager.getCurrentUserId()!!
                val userProfile = FirebaseUserProfile(
                    displayName = name,
                    email = email,
                    role = selectedRole
                ).apply {
                    this.userId = userId
                }

                val profileResult = userProfileRepository.updateCurrentUserProfile(userProfile)

                if (profileResult.isFailure) {
                    Toast.makeText(
                        requireContext(),
                        "Profil konnte nicht erstellt werden: ${profileResult.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                        Toast.LENGTH_SHORT
                    ).show()
                }

                // Start sync service
                val syncManager = SyncManager(requireContext())
                syncManager.scheduleSyncWork()

                // Perform initial sync (upload local data)
                syncManager.performFullSync()

                // Check if we came from the settings screen
                val previousDestination = findNavController().previousBackStackEntry?.destination?.id
                if (previousDestination == R.id.navigation_settings) {
                    // Go back to settings
                    findNavController().popBackStack()
                } else {
                    // Go to home screen
                    findNavController().navigate(R.id.action_registerFragment_to_homeFragment)
                }
            } else {
                Toast.makeText(
                    requireContext(),
                    "Registrierung fehlgeschlagen: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
                setLoading(false)
            }
        }
    }

    private fun setLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        registerButton.isEnabled = !isLoading
        loginButton.isEnabled = !isLoading
    }
}
