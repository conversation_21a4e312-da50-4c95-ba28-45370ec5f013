package com.atom.diesnookerapp.data.firebase

/**
 * Firebase model for user profiles
 */
data class FirebaseUserProfile(
    var displayName: String = "",
    var email: String = "",
    var role: UserRole = UserRole.PLAYER,
    var connectedUsers: List<String> = emptyList() // List of connected user IDs
) : FirebaseModel() {
    companion object {
        fun createDefault(userId: String, email: String): FirebaseUserProfile {
            return FirebaseUserProfile(
                displayName = email.substringBefore('@'),
                email = email,
                role = UserRole.PLAYER
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }
}

/**
 * Enum for user roles
 */
enum class UserRole {
    PLAYER,      // Regular user/athlete
    TRAINER,     // Coach with access to exercise data and training plans
    MENTAL_TRAINER  // Mental coach with access to self-assessment data
}
