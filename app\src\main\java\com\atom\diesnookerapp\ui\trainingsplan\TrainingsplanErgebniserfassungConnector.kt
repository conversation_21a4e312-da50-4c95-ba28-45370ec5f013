package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.widget.Toast

/**
 * This class serves as a connector between the Ergebniserfassung and Trainingsplan modules.
 * It provides functionality to automatically mark exercises as completed in the Trainingsplan
 * when training time is added in Ergebniserfassung.
 */
class TrainingsplanErgebniserfassungConnector(private val context: Context) {
    private val trainingsplanManager = TrainingsplanManager(context)
    
    /**
     * Marks an exercise as completed in the current training plan when time is added in Ergebniserfassung.
     * 
     * @param exerciseId The ID of the exercise to mark as completed
     * @return True if the exercise was found and marked as completed, false otherwise
     */
    fun markExerciseCompletedFromErgebniserfassung(exerciseId: String): <PERSON><PERSON>an {
        val wasMarked = trainingsplanManager.markExerciseCompleted(exerciseId)
        
        if (wasMarked) {
            Toast.makeText(
                context, 
                "Übung wurde im Trainingsplan als erledigt markiert", 
                Toast.LENGTH_SHORT
            ).show()
        }
        
        return wasMarked
    }
}
