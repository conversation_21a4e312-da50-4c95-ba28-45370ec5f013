"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import { collection, query, where, orderBy, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import LoadingSpinner from "@/components/ui/LoadingSpinner";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface TrainingRecordItem {
  title: string;
  score: number;
}

interface TrainingRecord {
  id: string;
  date: string; // Assuming date is stored as string like "YYYY-MM-DD" or can be from Timestamp
  type: string;
  items: TrainingRecordItem[];
  lastUpdated: Timestamp;
}

interface QuestionRecordItem {
  title: string;
  answer: string;
}

interface QuestionRecord {
  id: string;
  date: string; // Similar assumption as TrainingRecord
  type: string;
  questions: QuestionRecordItem[];
  lastUpdated: Timestamp;
}

function SelfAssessmentPage() {
  const { currentUser } = useAuth();
  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>([]);
  const [questionRecords, setQuestionRecords] = useState<QuestionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch Training Records
        const trQuery = query(
          collection(db, "training_records"),
          where("userId", "==", currentUser.uid),
          orderBy("lastUpdated", "desc")
        );
        const trSnapshot = await getDocs(trQuery);
        const fetchedTrainingRecords = trSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as TrainingRecord));
        setTrainingRecords(fetchedTrainingRecords);

        // Fetch Question Records
        const qrQuery = query(
          collection(db, "question_records"),
          where("userId", "==", currentUser.uid),
          orderBy("lastUpdated", "desc")
        );
        const qrSnapshot = await getDocs(qrQuery);
        const fetchedQuestionRecords = qrSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as QuestionRecord));
        setQuestionRecords(fetchedQuestionRecords);

      } catch (err: any) {
        console.error("Error fetching self-assessment data:", err);
        setError("Failed to load data. " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentUser]);

  const trainingChartData = {
    labels: trainingRecords
        .map(r => new Date(r.date || r.lastUpdated.toDate()).toLocaleDateString('de-DE'))
        .reverse(), // Show oldest first
    datasets: [
      {
        label: 'Average Score',
        data: trainingRecords
            .map(r => {
                const totalScore = r.items.reduce((sum, item) => sum + (item.score || 0), 0);
                return r.items.length > 0 ? totalScore / r.items.length : 0;
            })
            .reverse(),
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Self-Assessment Score Trends',
      },
    },
    scales: {
        y: {
            beginAtZero: true,
            max: 10 // Assuming scores are out of 10
        }
    }
  };


  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Selbsteinschätzung</h1>

      <section className="mb-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4 text-gray-700">Score Verlauf (Training Records)</h2>
        {trainingRecords.length > 0 ? (
          <div className="h-80"> {/* Set a fixed height for the chart container */}
            <Line options={chartOptions} data={trainingChartData} />
          </div>
        ) : (
          <p className="text-gray-600">No training records found to display chart.</p>
        )}
        <div className="mt-6 space-y-4">
          {trainingRecords.map(record => (
            <details key={record.id} className="p-3 border rounded-md bg-gray-50">
              <summary className="font-medium cursor-pointer text-gray-700 hover:text-indigo-600">
                {record.type} - {new Date(record.date || record.lastUpdated.toDate()).toLocaleDateString('de-DE')}
              </summary>
              <ul className="mt-2 list-disc list-inside pl-4 text-sm text-gray-600 space-y-1">
                {record.items.map((item, index) => (
                  <li key={index}>{item.title}: {item.score}/10</li>
                ))}
              </ul>
            </details>
          ))}
        </div>
      </section>

      <section className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4 text-gray-700">Fragebogen Antworten (Question Records)</h2>
        {questionRecords.length > 0 ? (
            <div className="space-y-4">
            {questionRecords.map(record => (
                <details key={record.id} className="p-3 border rounded-md bg-gray-50">
                <summary className="font-medium cursor-pointer text-gray-700 hover:text-indigo-600">
                    {record.type} - {new Date(record.date || record.lastUpdated.toDate()).toLocaleDateString('de-DE')}
                </summary>
                <ul className="mt-2 list-disc list-inside pl-4 text-sm text-gray-600 space-y-1">
                    {record.questions.map((q, index) => (
                    <li key={index}><strong>{q.title}:</strong> {q.answer}</li>
                    ))}
                </ul>
                </details>
            ))}
            </div>
        ) : (
            <p className="text-gray-600">No question records found.</p>
        )}
      </section>
    </div>
  );
}

export default withAuth(SelfAssessmentPage, { redirectTo: '/login' });
