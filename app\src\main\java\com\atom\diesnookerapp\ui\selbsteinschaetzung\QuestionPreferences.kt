package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.time.LocalDate

class QuestionPreferences(context: Context) {
    private val prefs = context.getSharedPreferences("questions_prefs", Context.MODE_PRIVATE)
    private val gson = Gson()

    fun saveDaily(record: DailyQuestionRecord) {
        val records = getRecords().toMutableList()
        // Remove existing record for the same date and type if it exists
        records.removeIf { it.date == record.date && it.type == record.type }
        records.add(record)
        
        prefs.edit().putString("daily_records", gson.toJson(records)).apply()
    }

    fun getRecords(): List<DailyQuestionRecord> {
        val json = prefs.getString("daily_records", "[]")
        val type = object : TypeToken<List<DailyQuestionRecord>>() {}.type
        return gson.fromJson(json, type) ?: emptyList()
    }

    fun clearAll() {
        prefs.edit().remove("daily_records").apply()
    }
} 