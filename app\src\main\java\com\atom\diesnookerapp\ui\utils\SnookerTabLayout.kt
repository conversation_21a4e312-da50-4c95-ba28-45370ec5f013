package com.atom.diesnookerapp.ui.utils

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemePreferences
import com.google.android.material.tabs.TabLayout

/**
 * A custom TabLayout that applies the correct theme colors
 */
class SnookerTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    init {
        applyThemeColors()
    }

    private fun applyThemeColors() {
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> {
                // Set the background color of the entire TabLayout
                setBackgroundColor(context.getColor(R.color.snooker_card_background_light))

                // Set the indicator color
                setSelectedTabIndicatorColor(context.getColor(R.color.snooker_red_500))

                // Set the text colors
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.snooker_red_500)
                )

                // Force the tab background to be the same as the TabLayout
                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.snooker_card_background_light))
                }
            }
            ThemePreferences.THEME_BLUE -> {
                setBackgroundColor(context.getColor(R.color.blue_card_background_light))
                setSelectedTabIndicatorColor(context.getColor(R.color.blue_500))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.blue_500)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.blue_card_background_light))
                }
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                setBackgroundColor(context.getColor(R.color.dark_blue_card_background_light))
                setSelectedTabIndicatorColor(context.getColor(R.color.dark_blue_500))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.dark_blue_500)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.dark_blue_card_background_light))
                }
            }
            ThemePreferences.THEME_OCEAN -> {
                setBackgroundColor(context.getColor(R.color.ocean_card_background_light))
                setSelectedTabIndicatorColor(context.getColor(R.color.ocean_medium))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.ocean_medium)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.ocean_card_background_light))
                }
            }
            ThemePreferences.THEME_CRIMSON -> {
                setBackgroundColor(context.getColor(R.color.crimson_card_background_light))
                setSelectedTabIndicatorColor(context.getColor(R.color.crimson_medium))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.crimson_medium)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.crimson_card_background_light))
                }
            }
            ThemePreferences.THEME_NEON -> {
                setBackgroundColor(context.getColor(R.color.neon_card_background_light))
                setSelectedTabIndicatorColor(context.getColor(R.color.neon_purple_3))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.neon_purple_3)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.neon_card_background_light))
                }
            }
            ThemePreferences.THEME_DARK -> {
                setBackgroundColor(context.getColor(R.color.dark_gray))
                setSelectedTabIndicatorColor(context.getColor(R.color.purple_200))
                setTabTextColors(
                    context.getColor(R.color.white),
                    context.getColor(R.color.purple_200)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.dark_gray))
                }
            }
            else -> {
                setBackgroundColor(context.getColor(R.color.white))
                setSelectedTabIndicatorColor(context.getColor(R.color.purple_500))
                setTabTextColors(
                    context.getColor(R.color.black),
                    context.getColor(R.color.purple_500)
                )

                val tabStrip = getChildAt(0) as LinearLayout
                for (i in 0 until tabStrip.childCount) {
                    val tabView = tabStrip.getChildAt(i)
                    tabView.background = ColorDrawable(context.getColor(R.color.white))
                }
            }
        }
    }

    override fun addTab(tab: Tab, position: Int, setSelected: Boolean) {
        super.addTab(tab, position, setSelected)

        // Apply theme to the newly added tab
        val themePreferences = ThemePreferences(context)
        val currentTheme = themePreferences.getThemeMode()

        val tabStrip = getChildAt(0) as LinearLayout
        val tabView = tabStrip.getChildAt(position)

        when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> {
                tabView.background = ColorDrawable(context.getColor(R.color.snooker_card_background_light))
            }
            ThemePreferences.THEME_BLUE -> {
                tabView.background = ColorDrawable(context.getColor(R.color.blue_card_background_light))
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                tabView.background = ColorDrawable(context.getColor(R.color.dark_blue_card_background_light))
            }
            ThemePreferences.THEME_OCEAN -> {
                tabView.background = ColorDrawable(context.getColor(R.color.ocean_card_background_light))
            }
            ThemePreferences.THEME_CRIMSON -> {
                tabView.background = ColorDrawable(context.getColor(R.color.crimson_card_background_light))
            }
            ThemePreferences.THEME_NEON -> {
                tabView.background = ColorDrawable(context.getColor(R.color.neon_card_background_light))
            }
            ThemePreferences.THEME_DARK -> {
                tabView.background = ColorDrawable(context.getColor(R.color.dark_gray))
            }
            else -> {
                tabView.background = ColorDrawable(context.getColor(R.color.white))
            }
        }
    }
}
