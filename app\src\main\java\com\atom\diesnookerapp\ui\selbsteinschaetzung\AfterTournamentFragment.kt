package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.button.MaterialButton
import org.threeten.bp.LocalDate
import org.threeten.bp.ZoneId

class AfterTournamentFragment : Fragment() {
    private var recyclerView: RecyclerView? = null
    private lateinit var trainingPreferences: TrainingPreferences
    private val assessmentItems = listOf(
        TrainingAssessmentItem(1, "Konzentration"),
        TrainingAssessmentItem(2, "Kondition"),
        TrainingAssessmentItem(3, "Gleichgewicht"),
        TrainingAssessmentItem(4, "Augen"),
        TrainingAssessmentItem(5, "Flüssigkeit"),
        TrainingAssessmentItem(6, "Freude"),
        TrainingAssessmentItem(7, "Motivation"),
        TrainingAssessmentItem(8, "Aktivierung"),
        TrainingAssessmentItem(9, "Entspannung"),
        TrainingAssessmentItem(10, "Stressbewältigung"),
        TrainingAssessmentItem(11, "win Konzept"),
        TrainingAssessmentItem(12, "lea Konzept"),
        TrainingAssessmentItem(13, "Stoß"),
        TrainingAssessmentItem(14, "Fokus auf Abstoß"),
        TrainingAssessmentItem(15, "technisch aufwendige Bälle"),
        TrainingAssessmentItem(16, "roll Bälle"),
        TrainingAssessmentItem(17, "Effet Bälle"),
        TrainingAssessmentItem(18, "Bandenbälle"),
        TrainingAssessmentItem(19, "Entscheidende Bälle"),
        TrainingAssessmentItem(20, "Safetys"),
        TrainingAssessmentItem(21, "Matchplay"),
        TrainingAssessmentItem(22, "splits"),
        TrainingAssessmentItem(23, "Positionsspiel"),
        TrainingAssessmentItem(24, "Potting"),
        TrainingAssessmentItem(25, "Rest"),
        TrainingAssessmentItem(26, "Material"),
        TrainingAssessmentItem(27, "Umfeld"),
        TrainingAssessmentItem(28, "Ziel erreicht")
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_after_tournament, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())

        recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        val adapter = TrainingAssessmentAdapter(assessmentItems) { item ->
            println("Item ${item.id} score changed to ${item.score}")
        }

        recyclerView?.adapter = adapter

        view.findViewById<MaterialButton>(R.id.saveButton).setOnClickListener {
            saveAssessment()
        }
    }

    private fun saveAssessment() {
        val record = DailyTrainingRecord(
            date = LocalDate.now(ZoneId.systemDefault()),
            type = TrainingType.AFTER_TOURNAMENT,
            items = assessmentItems
        )
        trainingPreferences.saveDaily(record)
        Toast.makeText(requireContext(), "Bewertungen gespeichert", Toast.LENGTH_SHORT).show()
    }
} 