<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/dialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Übung hinzufügen"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <com.atom.diesnookerapp.ui.utils.SnookerTabLayout
        android:id="@+id/categoryTabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/dialogTitle"
        app:tabMode="scrollable" />

    <FrameLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/categoryTabs">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/exercisesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:clipToPadding="false"
            android:paddingBottom="4dp" />

        <!-- Custom Exercise Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/customExerciseLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/exerciseNameLayout"
                style="@style/CustomTextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Übungsname"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/exerciseNameInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/exerciseTypeLayout"
                style="@style/CustomDropdownTextInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Kategorie"
                app:layout_constraintTop_toBottomOf="@id/exerciseNameLayout">

                <AutoCompleteTextView
                    android:id="@+id/exerciseTypeDropdown"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />
            </com.google.android.material.textfield.TextInputLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <!-- Target Count Section -->
    <LinearLayout
        android:id="@+id/targetCountLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@+id/contentContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Wie oft soll diese Übung absolviert werden?"
            android:textSize="14sp"
            android:layout_weight="1" />

        <NumberPicker
            android:id="@+id/targetCountPicker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/cancelButton"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Abbrechen"
        app:layout_constraintEnd_toStartOf="@+id/addButton"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/targetCountLayout" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/addButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Hinzufügen"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/targetCountLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>
