package com.atom.diesnookerapp.ui.aufgaben

import android.app.AlertDialog
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.NumberPicker
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import org.threeten.bp.LocalDate

class TaskAdapter(
    private var tasks: MutableList<Task>, // Made mutable and var
    private val onTaskCompleted: (Task) -> Unit,
    private val onTaskUncompleted: (Task) -> Unit,
    private val getFrequencyText: (Task) -> String,
    private val taskManager: TaskManager
) : RecyclerView.Adapter<TaskAdapter.ViewHolder>() {

    // Method to update the list of tasks
    fun submitList(newTasks: List<Task>) {
        tasks.clear()
        tasks.addAll(newTasks)
        notifyDataSetChanged()
    }

    // Callback for when a task is completed with custom points
    private var onTaskCompletedWithCustomPoints: ((Task, Int) -> Unit)? = null

    // Set the callback for custom points
    fun setOnTaskCompletedWithCustomPoints(callback: (Task, Int) -> Unit) {
        onTaskCompletedWithCustomPoints = callback
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val taskTitle: TextView = view.findViewById(R.id.taskTitle)
        val taskDescription: TextView = view.findViewById(R.id.taskDescription)
        val taskFrequency: TextView = view.findViewById(R.id.taskFrequency)
        val taskPoints: TextView = view.findViewById(R.id.taskPoints)
        val taskCheckbox: CheckBox = view.findViewById(R.id.taskCheckbox)
        val taskCard: MaterialCardView = view.findViewById(R.id.taskCard)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_task, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val task = tasks[position]
        val today = LocalDate.now()

        holder.taskTitle.text = task.title
        holder.taskDescription.text = task.description

        // Get completion status
        val (currentCompletions, maxCompletions) = taskManager.getTaskCompletionStatus(task.id)

        // Update frequency text to show completion status
        val frequencyText = getFrequencyText(task)
        holder.taskFrequency.text = "$frequencyText ($currentCompletions/$maxCompletions)"

        // Check if there are custom points for today
        val customPoints = taskManager.getCustomPointsForTask(task.id, today)

        if (customPoints != null && taskManager.isTaskCompletedForDate(task.id, today)) {
            holder.taskPoints.text = "$customPoints Punkte"
        } else {
            holder.taskPoints.text = "${task.points} Punkte"
        }

        // Check if the task is completed for today
        val isCompleted = taskManager.isTaskCompletedForDate(task.id, today)

        // Check if the task can be completed today
        val canComplete = taskManager.canCompleteTaskToday(task.id)

        // Remove listener before setting checked state to avoid triggering it
        holder.taskCheckbox.setOnCheckedChangeListener(null)
        holder.taskCheckbox.isChecked = isCompleted
        holder.taskCheckbox.isEnabled = canComplete || isCompleted

        // Set up checkbox listener
        holder.taskCheckbox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // Check if this task should use custom points
                if (taskManager.shouldUseCustomPoints(task.id)) {
                    // Show points selection dialog
                    showPointsSelectionDialog(holder.itemView.context, task)
                } else {
                    // Use default points
                    onTaskCompleted(task)
                }
            } else {
                onTaskUncompleted(task)
            }
            // Don't call notifyItemChanged here as it resets the checkbox state
            // Instead, update just the frequency text to show the new completion status
            val (newCurrentCompletions, newMaxCompletions) = taskManager.getTaskCompletionStatus(task.id)
            holder.taskFrequency.text = "$frequencyText ($newCurrentCompletions/$newMaxCompletions)"
        }

        // Set up card click listener to toggle checkbox
        holder.taskCard.setOnClickListener {
            if (holder.taskCheckbox.isEnabled) {
                // If the checkbox is not checked and this is a custom points task,
                // show the dialog directly instead of toggling the checkbox
                if (!holder.taskCheckbox.isChecked && taskManager.shouldUseCustomPoints(task.id)) {
                    showPointsSelectionDialog(holder.itemView.context, task)
                } else {
                    holder.taskCheckbox.isChecked = !holder.taskCheckbox.isChecked
                }
            }
        }
    }

    override fun getItemCount() = tasks.size

    // Helper method to update the selected points text with a description
    private fun updateSelectedPointsText(textView: TextView, points: Int) {
        textView.text = "$points Punkte"
    }

    // Show dialog to select points (1-5)
    private fun showPointsSelectionDialog(context: android.content.Context, task: Task) {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_select_points, null)

        // Set up the dialog
        val taskTitleText = dialogView.findViewById<TextView>(R.id.taskTitleText)
        val pointsPicker = dialogView.findViewById<NumberPicker>(R.id.pointsPicker)
        val selectedPointsText = dialogView.findViewById<TextView>(R.id.selectedPointsText)
        val cancelButton = dialogView.findViewById<MaterialButton>(R.id.cancelButton)
        val saveButton = dialogView.findViewById<MaterialButton>(R.id.saveButton)

        // Set task title
        taskTitleText.text = task.title

        // Configure the number picker
        pointsPicker.minValue = 1
        pointsPicker.maxValue = 5

        // Check if there are already custom points set for today
        val today = LocalDate.now()
        val existingPoints = taskManager.getCustomPointsForTask(task.id, today)

        // Set the initial value to existing points or default to 3
        pointsPicker.value = existingPoints ?: 3
        pointsPicker.wrapSelectorWheel = false // Prevent wrapping from 5 to 1 or 1 to 5

        // Update the selected points text when the value changes
        updateSelectedPointsText(selectedPointsText, pointsPicker.value)
        pointsPicker.setOnValueChangedListener { _, _, newVal ->
            updateSelectedPointsText(selectedPointsText, newVal)
        }

        // Create the dialog using ThemeHelper to apply the correct theme with default animations
        val dialog = ThemeHelper.createDefaultAnimationAlertDialogBuilder(context)
            .setView(dialogView)
            .setCancelable(false)
            .create()

        // Set up button listeners
        cancelButton.setOnClickListener {
            // Uncheck the checkbox since the user cancelled
            // notifyDataSetChanged() // Removed
            dialog.dismiss()
        }

        saveButton.setOnClickListener {
            // Get the selected points
            val selectedPoints = pointsPicker.value

            // Call the callback with the selected points
            onTaskCompletedWithCustomPoints?.invoke(task, selectedPoints)

            // Update the points display in the UI
            // notifyItemChanged(tasks.indexOf(task)) // Removed

            dialog.dismiss()
        }

        // Show the dialog
        dialog.show()
    }
}