package com.atom.diesnookerapp.ui.aufgaben

import android.animation.ValueAnimator
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import org.threeten.bp.temporal.TemporalAdjusters

class TaskHistoryFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyText: TextView
    private lateinit var taskManager: TaskManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_history, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Initialize views
        recyclerView = view.findViewById(R.id.historyRecyclerView)
        emptyText = view.findViewById(R.id.emptyHistoryText)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Load history data
        loadHistoryData()
    }

    private fun loadHistoryData() {
        val historyEntries = taskManager.getTaskHistory()

        if (historyEntries.isEmpty()) {
            recyclerView.visibility = View.GONE
            emptyText.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyText.visibility = View.GONE

            // Set up adapter
            val adapter = TaskHistoryAdapter(historyEntries)
            recyclerView.adapter = adapter

            // Log history entries for debugging
            Log.d("TaskHistoryFragment", "Loaded ${historyEntries.size} history entries")
            historyEntries.forEachIndexed { index, entry ->
                Log.d("TaskHistoryFragment", "Entry $index: Week ${entry.weekKey}, Tasks: ${entry.taskCompletions.size}, Points: ${entry.totalPoints}")
            }
        }
    }
}

class TaskHistoryAdapter(
    private val historyEntries: List<TaskHistoryEntry>
) : RecyclerView.Adapter<TaskHistoryAdapter.ViewHolder>() {

    // Track expanded items
    private val expandedItems = mutableSetOf<Int>()

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val weekText: TextView = view.findViewById(R.id.weekText)
        val dateRangeText: TextView = view.findViewById(R.id.dateRangeText)
        val totalPointsText: TextView = view.findViewById(R.id.totalPointsText)
        val taskCompletionsRecyclerView: RecyclerView = view.findViewById(R.id.taskCompletionsRecyclerView)
        val historyCard: MaterialCardView = view.findViewById(R.id.historyCard)
        val headerLayout: LinearLayout = view.findViewById(R.id.headerLayout)
        val contentLayout: LinearLayout = view.findViewById(R.id.contentLayout)
        val expandCollapseIcon: ImageView = view.findViewById(R.id.expandCollapseIcon)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_task_history, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val entry = historyEntries[position]
        val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")

        // The entry.endDate is the date when the archiving happened (typically the start of a new week)
        // We need to get the Monday of the previous week, since that's the week being archived
        // First get the Monday of the current week, then go back 7 days to get the Monday of the previous week

        // Handle null endDate by using current date as fallback
        val endDate = entry.endDate ?: LocalDate.now()
        val currentWeekMonday = endDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        val previousWeekMonday = currentWeekMonday.minusDays(7)

        // Format the week text to show "Woche vom" with the start date of the previous week
        holder.weekText.text = "Woche vom ${previousWeekMonday.format(dateFormatter)}"

        // Hide the date range text since we don't want to display the time span
        holder.dateRangeText.visibility = View.GONE

        // Set points
        holder.totalPointsText.text = "Gesammelte Punkte: ${entry.totalPoints}"

        // Set up nested RecyclerView for task completions
        holder.taskCompletionsRecyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)

        // Create a list of task completions for the nested adapter
        val taskCompletionItems = mutableListOf<TaskCompletionItem>()

        // Group tasks by category
        val tasksByCategory = entry.taskCompletions.groupBy { it.category }

        tasksByCategory.forEach { (category, tasks) ->
            // Add category header
            val categoryName = when (category) {
                "concentration" -> "Konzentration"
                "technik" -> "Technik"
                "matchplay" -> "Matchplay"
                "safeties" -> "Safeties"
                "breakbuilding" -> "Breakbuilding"
                "mental" -> "Mental"
                "koerpersprache" -> "Körpersprache"
                "wichtige_baelle" -> "Wichtige Bälle"
                "fitness_ernaehrung" -> "Fitness & Ernährung"
                else -> category
            }

            taskCompletionItems.add(TaskCompletionItem.Header(categoryName = categoryName))

            // Add tasks in this category
            tasks.forEach { task ->
                taskCompletionItems.add(TaskCompletionItem.Task(taskCompletion = task))
            }
        }

        // Set the adapter for the nested RecyclerView
        holder.taskCompletionsRecyclerView.adapter = TaskCompletionItemAdapter(taskCompletionItems)

        // Log the number of task completion items for debugging
        Log.d("TaskHistoryFragment", "Task completion items for entry ${position}: ${taskCompletionItems.size}")

        // Set expanded state
        val isExpanded = expandedItems.contains(position)
        if (isExpanded) {
            holder.contentLayout.visibility = View.VISIBLE
            // Reset height to wrap_content
            val layoutParams = holder.contentLayout.layoutParams
            layoutParams.height = LinearLayout.LayoutParams.WRAP_CONTENT
            holder.contentLayout.layoutParams = layoutParams
        } else {
            holder.contentLayout.visibility = View.GONE
        }
        holder.expandCollapseIcon.rotation = if (isExpanded) 180f else 0f

        // Set click listener for expand/collapse
        holder.headerLayout.setOnClickListener {
            toggleExpansion(holder, position)
        }
    }

    private fun toggleExpansion(holder: ViewHolder, position: Int) {
        val isExpanded = expandedItems.contains(position)

        if (isExpanded) {
            // Collapse
            expandedItems.remove(position)
            animateContentHeight(holder.contentLayout, holder.contentLayout.height, 0)
            animateArrow(holder.expandCollapseIcon, false)
        } else {
            // Expand
            expandedItems.add(position)
            // First make it visible with height 0
            holder.contentLayout.visibility = View.VISIBLE
            holder.contentLayout.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
            val targetHeight = holder.contentLayout.measuredHeight
            holder.contentLayout.layoutParams.height = 0
            // Then animate to the full height
            animateContentHeight(holder.contentLayout, 0, targetHeight)
            animateArrow(holder.expandCollapseIcon, true)
        }
    }

    private fun animateContentHeight(view: View, startHeight: Int, endHeight: Int) {
        val animator = ValueAnimator.ofInt(startHeight, endHeight)
        animator.addUpdateListener { animation ->
            val value = animation.animatedValue as Int
            val layoutParams = view.layoutParams
            layoutParams.height = value
            view.layoutParams = layoutParams

            // When collapsing and reaching 0, set visibility to GONE
            if (endHeight == 0 && value == 0) {
                view.visibility = View.GONE
            }
        }
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.start()
    }

    private fun animateArrow(view: View, isExpanded: Boolean) {
        val startRotation = if (isExpanded) 0f else 180f
        val endRotation = if (isExpanded) 180f else 0f

        val animator = ValueAnimator.ofFloat(startRotation, endRotation)
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.addUpdateListener { animation ->
            view.rotation = animation.animatedValue as Float
        }
        animator.start()
    }

    override fun getItemCount() = historyEntries.size
}