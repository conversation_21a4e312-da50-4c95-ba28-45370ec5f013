<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Einstellungen"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@+id/titleTextView"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/accountCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Konto"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/emailTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Nicht angemeldet"
                            android:textSize="16sp" />

                        <Button
                            android:id="@+id/loginButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Anmelden" />

                        <Button
                            android:id="@+id/logoutButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Abmelden"
                            android:visibility="gone" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/syncCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Synchronisierung"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/syncStatusTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Inaktiv"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/lastSyncTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="Letzte Synchronisierung: Nie"
                            android:textSize="14sp" />

                        <Button
                            android:id="@+id/syncNowButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:enabled="false"
                            android:text="Jetzt synchronisieren" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/themeCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Farbschema"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Wähle das Farbschema der App."
                            android:textSize="16sp" />

                        <RadioGroup
                            android:id="@+id/themeRadioGroup"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <RadioButton
                                android:id="@+id/systemThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Systemeinstellung" />

                            <RadioButton
                                android:id="@+id/lightThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Hell" />

                            <include
                                android:id="@+id/lightThemePreviewInclude"
                                layout="@layout/theme_preview_light" />

                            <RadioButton
                                android:id="@+id/darkThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Dunkel" />

                            <include
                                android:id="@+id/darkThemePreviewInclude"
                                layout="@layout/theme_preview_dark" />

                            <RadioButton
                                android:id="@+id/snookerThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Snooker" />

                            <include
                                android:id="@+id/snookerThemePreviewInclude"
                                layout="@layout/theme_preview_snooker" />

                            <RadioButton
                                android:id="@+id/blueThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Blau" />

                            <include
                                android:id="@+id/blueThemePreviewInclude"
                                layout="@layout/theme_preview_blue" />

                            <RadioButton
                                android:id="@+id/darkBlueThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Dunkelblau" />

                            <include
                                android:id="@+id/darkBlueThemePreviewInclude"
                                layout="@layout/theme_preview_dark_blue" />

                            <RadioButton
                                android:id="@+id/oceanThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Ocean" />

                            <include
                                android:id="@+id/oceanThemePreviewInclude"
                                layout="@layout/theme_preview_ocean" />

                            <RadioButton
                                android:id="@+id/crimsonThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Crimson" />

                            <include
                                android:id="@+id/crimsonThemePreviewInclude"
                                layout="@layout/theme_preview_crimson" />

                            <RadioButton
                                android:id="@+id/neonThemeRadioButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Neon" />

                            <include
                                android:id="@+id/neonThemePreviewInclude"
                                layout="@layout/theme_preview_neon" />
                        </RadioGroup>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/webAccessCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Web-Zugriff"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Greife auf deine Daten über einen Webbrowser zu."
                            android:textSize="16sp" />

                        <Button
                            android:id="@+id/openWebButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Website öffnen" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/userManagementCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Trainer-Verbindungen"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Verwalte Trainer, die Zugriff auf deine Daten haben."
                            android:textSize="16sp" />

                        <Button
                            android:id="@+id/userManagementButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Trainer verwalten" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            <!-- Add some bottom padding for better scrolling -->
            <View
                android:layout_width="match_parent"
                android:layout_height="16dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
