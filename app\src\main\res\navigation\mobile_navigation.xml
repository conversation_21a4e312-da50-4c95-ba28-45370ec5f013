<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">

    <fragment
        android:id="@+id/navigation_exercise_history"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseHistoryFragment"
        android:label="Übungshistorie"
        tools:layout="@layout/fragment_exercise_history">
        
        <action
            android:id="@+id/action_exercise_history_to_detail"
            app:destination="@id/navigation_exercise_history_detail" />
    </fragment>

    <fragment
        android:id="@+id/navigation_exercise_history_detail"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseHistoryDetailFragment"
        android:label="Übungsdetails"
        tools:layout="@layout/fragment_exercise_history_detail">
        <argument
            android:name="date"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/navigation_ergebniserfassung"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ErgebniserfassungFragment"
        android:label="Ergebniserfassung"
        tools:layout="@layout/fragment_ergebniserfassung">
        
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_exercise_history"
            app:destination="@id/navigation_exercise_history" />
        
    </fragment>

    <fragment
        android:id="@+id/navigation_task_history"
        android:name="com.atom.diesnookerapp.ui.aufgaben.TaskHistoryFragment"
        android:label="Aufgaben Verlauf"
        tools:layout="@layout/fragment_task_history" />
    
    <fragment
        android:id="@+id/navigation_aufgaben"
        android:name="com.atom.diesnookerapp.ui.aufgaben.AufgabenFragment"
        android:label="Aufgaben"
        tools:layout="@layout/fragment_aufgaben">
        <action
            android:id="@+id/action_navigation_aufgaben_to_konzentrationFragment"
            app:destination="@id/konzentrationFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_manage_tasks"
            app:destination="@id/navigation_manage_tasks" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_taskHistoryFragment"
            app:destination="@id/navigation_task_history" />
    </fragment>

    <fragment
        android:id="@+id/navigation_manage_tasks"
        android:name="com.atom.diesnookerapp.ui.aufgaben.ManageTasksFragment"
        android:label="Manage Tasks"
        tools:layout="@layout/fragment_manage_tasks" />
    
    <fragment
        android:id="@+id/konzentrationFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.KonzentrationFragment"
        android:label="Konzentration"
        tools:layout="@layout/fragment_task_list" />
    
</navigation>