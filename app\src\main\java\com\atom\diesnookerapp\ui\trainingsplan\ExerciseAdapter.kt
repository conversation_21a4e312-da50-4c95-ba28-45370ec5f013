package com.atom.diesnookerapp.ui.trainingsplan

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class ExerciseAdapter(
    private var exercises: List<ExerciseItem>,
    private val onItemClick: (ExerciseItem) -> Unit,
    val selectedExercisesList: MutableList<ExerciseItem> = mutableListOf()
) : RecyclerView.Adapter<ExerciseAdapter.ViewHolder>() {

    // Map to track count for each exercise
    private val exerciseCounts = mutableMapOf<String, Int>()

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val checkbox: CheckBox = view.findViewById(R.id.exerciseCheckbox)
        val decreaseButton: ImageButton = view.findViewById(R.id.decreaseButton)
        val increaseButton: ImageButton = view.findViewById(R.id.increaseButton)
        val countText: TextView = view.findViewById(R.id.countText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.exercise_selection_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val exercise = exercises[position]
        holder.checkbox.text = exercise.name
        
        // Check if this exercise is selected
        val isSelected = selectedExercisesList.any { it.id == exercise.id }
        holder.checkbox.isChecked = isSelected
        
        // Get count for this exercise (default to 1)
        val count = exerciseCounts[exercise.id] ?: 1
        
        // Update UI based on selection state
        if (isSelected) {
            holder.countText.visibility = View.VISIBLE
            holder.increaseButton.visibility = View.VISIBLE
            holder.countText.text = count.toString()
            
            // Only show decrease button if count > 1
            holder.decreaseButton.visibility = if (count > 1) View.VISIBLE else View.GONE
        } else {
            holder.countText.visibility = View.GONE
            holder.increaseButton.visibility = View.GONE
            holder.decreaseButton.visibility = View.GONE
        }
        
        // Handle checkbox click
        holder.checkbox.setOnClickListener {
            val checked = holder.checkbox.isChecked
            if (checked) {
                // Add to selected list if not already there
                if (!selectedExercisesList.contains(exercise)) {
                    selectedExercisesList.add(exercise)
                    exerciseCounts[exercise.id] = 1
                }
            } else {
                // Remove from selected list
                selectedExercisesList.remove(exercise)
                exerciseCounts.remove(exercise.id)
            }
            notifyItemChanged(position)
            onItemClick(exercise)
        }
        
        // Handle increase button click
        holder.increaseButton.setOnClickListener {
            val newCount = (exerciseCounts[exercise.id] ?: 1) + 1
            exerciseCounts[exercise.id] = newCount
            notifyItemChanged(position)
        }
        
        // Handle decrease button click
        holder.decreaseButton.setOnClickListener {
            val currentCount = exerciseCounts[exercise.id] ?: 1
            if (currentCount > 1) {
                exerciseCounts[exercise.id] = currentCount - 1
                notifyItemChanged(position)
            }
        }
    }

    override fun getItemCount() = exercises.size

    fun updateData(newExercises: List<ExerciseItem>) {
        exercises = newExercises
        notifyDataSetChanged()
    }
    
    // Get the count for a specific exercise
    fun getExerciseCount(exerciseId: String): Int {
        return exerciseCounts[exerciseId] ?: 1
    }
    
    // Get all selected exercises with their counts
    fun getSelectedExercisesWithCounts(): List<Pair<ExerciseItem, Int>> {
        return selectedExercisesList.map { 
            it to (exerciseCounts[it.id] ?: 1)
        }
    }
}
