package com.atom.diesnookerapp.ui.trainingsplan

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class TrainingsplanAdapter(
    private var items: List<TrainingsplanItem>,
    private val onItemCheckedChange: (TrainingsplanItem, Boolean) -> Unit,
    private val onItemDelete: (TrainingsplanItem) -> Unit
) : RecyclerView.Adapter<TrainingsplanAdapter.ViewHolder>() {

    // Map to store item IDs and their corresponding ViewHolders
    private val viewHolderMap = mutableMapOf<String, ViewHolder>()

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val exerciseName: TextView = view.findViewById(R.id.exerciseName)
        val exerciseType: TextView = view.findViewById(R.id.exerciseType)
        val completionCountText: TextView = view.findViewById(R.id.completionCountText)
        val checkBox: CheckBox = view.findViewById(R.id.checkBox)
        val deleteButton: ImageButton = view.findViewById(R.id.deleteButton)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.trainingsplan_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]

        // Store reference to this ViewHolder
        viewHolderMap[item.id] = holder

        holder.exerciseName.text = item.name
        holder.exerciseType.text = item.exerciseType ?: "Benutzerdefiniert"

        // Set completion count text
        holder.completionCountText.text = "${item.completionCount}/${item.targetCount}"

        // Temporarily remove the listener to prevent triggering it during binding
        holder.checkBox.setOnCheckedChangeListener(null)

        // Update checkbox state based on completion count
        holder.checkBox.isChecked = item.isChecked

        // Re-add the listener after setting the checked state
        holder.checkBox.setOnCheckedChangeListener { _, isChecked ->
            // Only process if this is a user action (not during binding)
            onItemCheckedChange(item, isChecked)
            // Update the completion count text after the callback
            holder.completionCountText.text = "${item.completionCount}/${item.targetCount}"
        }

        holder.deleteButton.setOnClickListener {
            onItemDelete(item)
        }
    }

    override fun getItemCount() = items.size

    fun updateData(newItems: List<TrainingsplanItem>) {
        items = newItems
        // Clear the ViewHolder map when data is updated
        viewHolderMap.clear()
        notifyDataSetChanged()
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        // Remove listeners when view is recycled to prevent memory leaks
        holder.checkBox.setOnCheckedChangeListener(null)
    }
}
