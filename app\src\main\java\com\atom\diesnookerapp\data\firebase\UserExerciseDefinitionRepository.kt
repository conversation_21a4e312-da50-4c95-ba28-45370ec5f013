package com.atom.diesnookerapp.data.firebase

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.tasks.await

private const val COLLECTION_NAME = "user_exercise_definitions"

class UserExerciseDefinitionRepository {

    private val db = Firebase.firestore.collection(COLLECTION_NAME)

    suspend fun getExercisesForUser(userId: String): Result<List<UserExerciseDefinition>> {
        return try {
            val querySnapshot = db.whereEqualTo("userId", userId).get().await()
            val exercises = querySnapshot.documents.mapNotNull { document ->
                document.toObject(UserExerciseDefinition::class.java)
            }
            Result.success(exercises)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun saveExercise(exercise: UserExerciseDefinition): Result<Unit> {
        return try {
            val docId = if (exercise.id.isBlank()) db.document().id else exercise.id

            // Ensure the userId is not blank. This is critical.
            if (exercise.userId.isBlank()) {
                return Result.failure(IllegalArgumentException("Cannot save exercise with blank userId. Exercise ID: $docId"))
            }

            val exerciseDataToSave = mapOf(
                "id" to docId,
                "userId" to exercise.userId,
                "name" to exercise.name,
                "category" to exercise.category,
                "description" to exercise.description,
                "exerciseType" to exercise.exerciseType
                // Explicitly do NOT include isCustom or isDeletable here
            )
            db.document(docId).set(exerciseDataToSave).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun deleteExercise(exerciseId: String): Result<Unit> {
        return try {
            db.document(exerciseId).delete().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
