<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Die Snooker App - History</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <h1>Die Snooker App</h1>
            <nav>
                <ul>
                    <li><a href="#" data-section="selfassessment" class="active">Selbsteinschätzung</a></li>
                    <li><a href="#" data-section="exercises">Übungen</a></li>
                    <li><a href="#" data-section="tasks">Aufgaben</a></li>
                    <li><a href="#" data-section="trainingsplan">Trainingsplan</a></li>
                    <li><a href="#" data-section="usermanagement">Trainer-Verbindungen</a></li>
                </ul>
            </nav>
            <div id="user-info">
                <span id="user-email"></span>
                <button id="logout-btn" class="btn">Abmelden</button>
            </div>
        </aside>
        <main>
            <div id="login-container">
                <h2>Anmelden</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="email">E-Mail</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Passwort</label>
                        <input type="password" id="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Anmelden</button>
                    <div class="form-footer">
                        <p>Sind Sie Trainer? <a href="trainer-register.html">Als Trainer registrieren</a></p>
                    </div>
                </form>
            </div>
            <div id="app-container" class="hidden">
                <section id="selfassessment-section" class="active">
                    <h2>Selbsteinschätzung</h2>
                    <canvas id="training-chart" height="100"></canvas>
                    <div class="history-list" id="training-list"></div>
                    <hr style="margin: 40px 0;">
                    <h2>Fragen</h2>
                    <div class="history-list" id="questions-list"></div>
                </section>
                <section id="exercises-section">
                    <h2>Übungen</h2>
                    <canvas id="exercises-chart" height="100"></canvas>
                    <div class="history-list" id="exercises-list"></div>
                </section>
                <section id="tasks-section">
                    <h2>Aufgaben History</h2>
                    <div class="history-list" id="tasks-list"></div>
                </section>
                <section id="trainingsplan-section">
                    <h2>Trainingsplan Monatsübersicht</h2>
                    <div class="history-list" id="trainingsplan-list"></div>
                </section>

                <section id="usermanagement-section">
                    <h2>Trainer-Verbindungen</h2>

                    <div class="card" id="add-trainer-card">
                        <h3>Trainer hinzufügen</h3>
                        <div class="form-group">
                            <label for="trainer-email">E-Mail des Trainers</label>
                            <input type="email" id="trainer-email" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="trainer-type">Trainer-Typ</label>
                            <select id="trainer-type">
                                <option value="TRAINER">Trainer</option>
                                <option value="MENTAL_TRAINER">Mental Trainer</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Zugriffsberechtigungen</label>
                            <div class="checkbox-group">
                                <label>
                                    <input type="checkbox" id="exercise-access" checked>
                                    Übungen und Trainingsplan
                                </label>
                                <label>
                                    <input type="checkbox" id="selfassessment-access">
                                    Selbsteinschätzung
                                </label>
                            </div>
                        </div>
                        <button id="add-trainer-btn" class="btn btn-primary">Hinzufügen</button>
                    </div>

                    <h3>Aktive Verbindungen</h3>
                    <div class="history-list" id="connections-list"></div>
                    <div id="empty-connections" class="empty-state">Keine Verbindungen vorhanden</div>
                </section>
            </div>
        </main>
    </div>
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- App scripts -->
    <script src="app.js"></script>
    <script src="user-management.js"></script>
    <script src="user-management-dialogs.js"></script>
</body>
</html>
