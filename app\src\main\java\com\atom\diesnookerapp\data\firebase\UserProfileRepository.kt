package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for user profiles
 */
class UserProfileRepository(private val context: Context) :
    FirebaseRepository<FirebaseUserProfile>("user_profiles") {

    companion object {
        private const val TAG = "UserProfileRepository"
    }

    private val authManager = FirebaseAuthManager()
    private val firestore = FirebaseFirestore.getInstance()

    /**
     * Get the current user's profile
     */
    suspend fun getCurrentUserProfile(): Result<FirebaseUserProfile> = withContext(Dispatchers.IO) {
        val userId = authManager.getCurrentUserId() ?: return@withContext Result.failure(Exception("User not logged in"))

        return@withContext try {
            val document = getCollection().document(userId).get().await()

            if (document.exists()) {
                val profile = document.toObject(FirebaseUserProfile::class.java)?.apply {
                    id = document.id
                    if (this.userId.isEmpty()) {
                        this.userId = userId
                    }
                }
                Result.success(profile!!)
            } else {
                // Create a default profile if none exists
                val email = authManager.getCurrentUser()?.email ?: ""
                val defaultProfile = FirebaseUserProfile.createDefault(userId, email)

                try {
                    // Save the default profile
                    getCollection().document(userId).set(defaultProfile).await()
                    Log.d(TAG, "Created default profile for user $userId")
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating default profile: ${e.message}")
                    // Continue even if profile creation fails - we'll return the default profile anyway
                }

                Result.success(defaultProfile)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user profile: ${e.message}")

            // If we can't get the profile, create a default one in memory
            // This ensures the app can continue to function even if Firestore access fails
            val email = authManager.getCurrentUser()?.email ?: ""
            val defaultProfile = FirebaseUserProfile.createDefault(userId, email)
            Result.success(defaultProfile)
        }
    }

    /**
     * Get a user profile by ID
     */
    suspend fun getUserProfileById(userId: String): Result<FirebaseUserProfile> = withContext(Dispatchers.IO) {
        return@withContext try {
            val document = getCollection().document(userId).get().await()

            if (document.exists()) {
                val profile = document.toObject(FirebaseUserProfile::class.java)?.apply { id = document.id }
                Result.success(profile!!)
            } else {
                Result.failure(Exception("User profile not found"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user profile: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Find a user profile by email
     */
    suspend fun findUserProfileByEmail(email: String): Result<FirebaseUserProfile?> = withContext(Dispatchers.IO) {
        return@withContext try {
            val snapshot = getCollection()
                .whereEqualTo("email", email)
                .get()
                .await()

            if (!snapshot.isEmpty) {
                val profile = snapshot.documents[0].toObject(FirebaseUserProfile::class.java)?.apply {
                    id = snapshot.documents[0].id
                }
                Result.success(profile)
            } else {
                Result.success(null) // No user found with this email
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error finding user profile by email: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Update the current user's profile
     */
    suspend fun updateCurrentUserProfile(profile: FirebaseUserProfile): Result<FirebaseUserProfile> =
        withContext(Dispatchers.IO) {
            val userId = authManager.getCurrentUserId() ?: return@withContext Result.failure(Exception("User not logged in"))

            return@withContext try {
                // Ensure userId is set correctly
                profile.userId = userId
                profile.lastUpdated = System.currentTimeMillis()

                // Check if the document exists first
                val docRef = getCollection().document(userId)
                val docSnapshot = docRef.get().await()

                if (docSnapshot.exists()) {
                    // Update existing document
                    docRef.update(
                        mapOf(
                            "displayName" to profile.displayName,
                            "email" to profile.email,
                            "role" to profile.role,
                            "connectedUsers" to profile.connectedUsers,
                            "userId" to userId,
                            "lastUpdated" to profile.lastUpdated
                        )
                    ).await()
                } else {
                    // Create new document
                    docRef.set(profile).await()
                }

                Log.d(TAG, "Successfully updated/created profile for user $userId")
                Result.success(profile)
            } catch (e: Exception) {
                Log.e(TAG, "Error updating user profile: ${e.message}")
                // Return the profile anyway to prevent app crashes
                // The app can continue to function with the in-memory profile
                Result.success(profile)
            }
        }
}
