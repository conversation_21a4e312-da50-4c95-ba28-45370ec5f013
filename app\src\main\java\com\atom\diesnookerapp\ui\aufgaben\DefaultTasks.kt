package com.atom.diesnookerapp.ui.aufgaben

// Task and TaskFrequency should be automatically imported if in the same package.
// If not, explicit imports would be:
// import com.atom.diesnookerapp.ui.aufgaben.Task
// import com.atom.diesnookerapp.ui.aufgaben.TaskFrequency

object DefaultTasks {

    val allDefaultTasks: List<Task> = listOf(
        // --- Konzentration Tasks ---
        Task(id = "focus_on_shot", title = "Fokus auf Abstoß", description = "20 mal blau lang, nur auf Abstoß fokussieren", frequency = TaskFrequency.DAILY, points = 1, category = "concentration", weeklyFrequencyCount = null),
        Task(id = "all_balls_equal", title = "Alle Bälle sind gleich", description = "50 Schwarze vom Spot", frequency = TaskFrequency.WEEKLY, points = 2, category = "concentration", weeklyFrequencyCount = 2),
        Task(id = "visualize_cue_ball", title = "Ablage Weiß vorvisualisieren", description = "10min pro Tag Snooker im Kopf spielen", frequency = TaskFrequency.DAILY, points = 1, category = "concentration", weeklyFrequencyCount = null),
        Task(id = "focus_on_table", title = "Alles um den Tisch herum ausblenden", description = "5min auf einen Gegenstand voll fokussieren", frequency = TaskFrequency.DAILY, points = 1, category = "concentration", weeklyFrequencyCount = null),
        Task(id = "balance_unterberger", title = "Gleichgewicht", description = "Unterberger", frequency = TaskFrequency.WEEKLY, points = 2, category = "concentration", weeklyFrequencyCount = 3),
        Task(id = "balance", title = "Gleichgewicht", description = "10min Gleichgewicht trainieren", frequency = TaskFrequency.DAILY, points = 1, category = "concentration", weeklyFrequencyCount = null),
        Task(id = "general", title = "Allgemein", description = "15min pro Tag Übungen wie Fingerjogging etc.", frequency = TaskFrequency.DAILY, points = 1, category = "concentration", weeklyFrequencyCount = null),

        // --- Technik Tasks ---
        Task(id = "weiss_hinten_treffen", title = "Weiß hinten treffen", description = "Beim Einspielen blau gerade spielen mit Video", frequency = TaskFrequency.WEEKLY, points = 1, category = "technik", weeklyFrequencyCount = 1),
        Task(id = "ruhig_unten_bleiben", title = "Ruhig bleiben, unten bleiben", description = "Immer versuchen, auf Video bewerten 1-5", frequency = TaskFrequency.WEEKLY, points = 1, category = "technik", weeklyFrequencyCount = 1),

        // --- Matchplay Tasks ---
        Task(id = "entscheidungen_frame", title = "Entscheidungen richtig treffen", description = "Frame auseinander nehmen", frequency = TaskFrequency.WEEKLY, points = 2, category = "matchplay", weeklyFrequencyCount = 3),
        Task(id = "entscheidungen_video", title = "Entscheidungen richtig treffen", description = "Videoanalyse 30min", frequency = TaskFrequency.WEEKLY, points = 3, category = "matchplay", weeklyFrequencyCount = 2),
        Task(id = "entscheidungen_tip", title = "Entscheidungen richtig treffen", description = "Bei den drei Frames mit Tip weiß anzeigen", frequency = TaskFrequency.WEEKLY, points = 1, category = "matchplay", weeklyFrequencyCount = 1),
        Task(id = "matchpraxis", title = "Matchpraxis", description = "5h Sparring/Wettkampf", frequency = TaskFrequency.WEEKLY, points = 5, category = "matchplay", weeklyFrequencyCount = 1),
        Task(id = "richtig_abschaetzen", title = "Richtig abschätzen, wie man spielen sollte", description = "Nach jedem Training Selbsteinschätzung ausfüllen", frequency = TaskFrequency.DAILY, points = 1, category = "matchplay", weeklyFrequencyCount = null),

        // --- Wichtige Bälle Tasks ---
        Task(id = "stoss_vorbereitung", title = "Stoß Vorbereitung", description = "Stoß Ablauf immer gleich durchführen, bei Videocheck auch prüfen 1-5", frequency = TaskFrequency.WEEKLY, points = 1, category = "wichtige_baelle", weeklyFrequencyCount = 1),
        Task(id = "entscheidende_baelle", title = "Entscheidende Bälle", description = "30min bpbe rmat spielen", frequency = TaskFrequency.WEEKLY, points = 2, category = "wichtige_baelle", weeklyFrequencyCount = 2),

        // --- Safeties Tasks ---
        Task(id = "wenig_baelle", title = "Mit wenig Bällen", description = "Respotted black 20min", frequency = TaskFrequency.WEEKLY, points = 2, category = "safeties", weeklyFrequencyCount = 2),
        Task(id = "escapes", title = "Escapes", description = "Versch Escapes hinlegen 20min", frequency = TaskFrequency.WEEKLY, points = 2, category = "safeties", weeklyFrequencyCount = 2),

        // --- Mental Tasks ---
        Task(id = "mit_druck", title = "Mit Druck umgehen", description = "20min Video aufnehmen", frequency = TaskFrequency.WEEKLY, points = 1, category = "mental", weeklyFrequencyCount = 4),
        Task(id = "unterbewusstsein", title = "Unterbewusstsein stärken", description = "Jeden Tag Flowscript, Katastrophe und Vorbild (auf Weg zum Training) hören", frequency = TaskFrequency.DAILY, points = 3, category = "mental", weeklyFrequencyCount = null),
        Task(id = "flow", title = "Flow", description = "Zu Flowscript 10min genau Vorstellen wie sich alles anfühlt", frequency = TaskFrequency.DAILY, points = 1, category = "mental", weeklyFrequencyCount = null),
        Task(id = "positives_denken", title = "Positives Denken", description = "Immer dran erinnern", frequency = TaskFrequency.DAILY, points = 1, category = "mental", weeklyFrequencyCount = null),
        Task(id = "voll_da_sein", title = "Von Anfang an voll da sein", description = "Warm Up plan pro korrekter Durchführung", frequency = TaskFrequency.DAILY, points = 1, category = "mental", weeklyFrequencyCount = null),
        Task(id = "selbstbewusstsein", title = "Selbstbewusstsein", description = "Text eingeben", frequency = TaskFrequency.DAILY, points = 1, category = "mental", weeklyFrequencyCount = null),

        // --- Körpersprache Tasks ---
        Task(id = "aeusserliche_ruhe", title = "Äußerliche Ruhe", description = "Nach einfachen Fehlern im Training für 30-60sec auf Stuhl setzen und Lächeln/positiv denken", frequency = TaskFrequency.WEEKLY, points = 2, category = "koerpersprache", weeklyFrequencyCount = 1),
        Task(id = "aufrechte_koerperhaltung", title = "Aufrechte Körperhaltung", description = "Selbstbewusst um den Tisch laufen", frequency = TaskFrequency.DAILY, points = 1, category = "koerpersprache", weeklyFrequencyCount = null),
        Task(id = "aergern_nach_wettkampf", title = "Ärgern nach Wettkampf", description = "Siehe positives Denken", frequency = TaskFrequency.WEEKLY, points = 1, category = "koerpersprache", weeklyFrequencyCount = 1),
        Task(id = "laecheln", title = "Lächeln", description = "Bei Videocheck auf Mimik achten, 1-5 Punkte", frequency = TaskFrequency.WEEKLY, points = 1, category = "koerpersprache", weeklyFrequencyCount = 1),

        // --- Breakbuilding Tasks ---
        Task(id = "zhao_x", title = "30min Zhao X.", description = "2x die Woche", frequency = TaskFrequency.WEEKLY, points = 1, category = "breakbuilding", weeklyFrequencyCount = 2),

        // --- Fitness & Ernährung Tasks ---
        Task(id = "gym", title = "Gym", description = "Krafttraining", frequency = TaskFrequency.WEEKLY, points = 2, category = "fitness_ernaehrung", weeklyFrequencyCount = 2),
        Task(id = "joggen", title = "Joggen", description = "Ausdauertraining", frequency = TaskFrequency.WEEKLY, points = 3, category = "fitness_ernaehrung", weeklyFrequencyCount = 1),
        Task(id = "ernaehrung", title = "Ernährung", description = "Omega 3 und Magnesium reich ernähren", frequency = TaskFrequency.DAILY, points = 1, category = "fitness_ernaehrung", weeklyFrequencyCount = null)
    )
}
