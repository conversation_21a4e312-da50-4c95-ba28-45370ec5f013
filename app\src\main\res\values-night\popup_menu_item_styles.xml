<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base PopupMenu item style for night mode -->
    <style name="PopupMenuItemStyle" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Snooker theme PopupMenu item style for night mode -->
    <style name="PopupMenuItemStyle_Snooker" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_snooker_dark</item>
    </style>

    <!-- Blue theme PopupMenu item style for night mode -->
    <style name="PopupMenuItemStyle_Blue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_blue_dark</item>
    </style>

    <!-- Dark Blue theme PopupMenu item style for night mode -->
    <style name="PopupMenuItemStyle_DarkBlue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark_blue_dark</item>
    </style>

    <!-- Dark theme PopupMenu item style for night mode -->
    <style name="PopupMenuItemStyle_Dark" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
</resources>
