package com.atom.diesnookerapp.ui.auth

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.ConnectionStatus
import com.atom.diesnookerapp.data.firebase.FirebaseUserConnection
import com.atom.diesnookerapp.data.firebase.UserRole
import com.google.android.material.chip.Chip

class UserConnectionAdapter(
    private val onEditClick: (FirebaseUserConnection) -> Unit,
    private val onRemoveClick: (FirebaseUserConnection) -> Unit
) : ListAdapter<FirebaseUserConnection, UserConnectionAdapter.ViewHolder>(ConnectionDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_user_connection, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val connection = getItem(position)
        holder.bind(connection, onEditClick, onRemoveClick)
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val nameTextView: TextView = itemView.findViewById(R.id.nameTextView)
        private val emailTextView: TextView = itemView.findViewById(R.id.emailTextView)
        private val roleTextView: TextView = itemView.findViewById(R.id.roleTextView)
        private val permissionsTextView: TextView = itemView.findViewById(R.id.permissionsTextView)
        private val statusChip: Chip = itemView.findViewById(R.id.statusChip)
        private val editButton: Button = itemView.findViewById(R.id.editButton)
        private val removeButton: Button = itemView.findViewById(R.id.removeButton)

        fun bind(
            connection: FirebaseUserConnection,
            onEditClick: (FirebaseUserConnection) -> Unit,
            onRemoveClick: (FirebaseUserConnection) -> Unit
        ) {
            nameTextView.text = connection.targetName
            emailTextView.text = connection.targetEmail
            roleTextView.text = getRoleDisplayName(connection.targetRole)

            // Set permissions text
            val permissions = mutableListOf<String>()
            if (connection.trainerAccess) {
                permissions.add("Übungen und Trainingsplan")
            }
            if (connection.mentalTrainerAccess) {
                permissions.add("Selbsteinschätzung")
            }
            permissionsTextView.text = permissions.joinToString(", ")

            // Set status chip
            statusChip.text = getStatusDisplayName(connection.status)
            val (chipColor, textColor) = getStatusColors(connection.status, itemView.context)
            statusChip.chipBackgroundColor = ColorStateList.valueOf(chipColor)
            statusChip.setTextColor(textColor)

            // Set button click listeners
            editButton.setOnClickListener { onEditClick(connection) }
            removeButton.setOnClickListener { onRemoveClick(connection) }

            // Disable edit button if connection is not active
            editButton.isEnabled = connection.status == ConnectionStatus.ACTIVE
        }

        private fun getRoleDisplayName(role: UserRole): String {
            return when (role) {
                UserRole.PLAYER -> "Spieler"
                UserRole.TRAINER -> "Trainer"
                UserRole.MENTAL_TRAINER -> "Mental Trainer"
            }
        }

        private fun getStatusDisplayName(status: ConnectionStatus): String {
            return when (status) {
                ConnectionStatus.PENDING -> "Ausstehend"
                ConnectionStatus.ACTIVE -> "Aktiv"
                ConnectionStatus.REJECTED -> "Abgelehnt"
                ConnectionStatus.REVOKED -> "Widerrufen"
            }
        }

        private fun getStatusColors(status: ConnectionStatus, context: android.content.Context): Pair<Int, Int> {
            return when (status) {
                ConnectionStatus.PENDING -> Pair(
                    ContextCompat.getColor(context, android.R.color.holo_orange_light),
                    ContextCompat.getColor(context, android.R.color.black)
                )
                ConnectionStatus.ACTIVE -> Pair(
                    ContextCompat.getColor(context, android.R.color.holo_green_light),
                    ContextCompat.getColor(context, android.R.color.black)
                )
                ConnectionStatus.REJECTED -> Pair(
                    ContextCompat.getColor(context, android.R.color.holo_red_light),
                    ContextCompat.getColor(context, android.R.color.white)
                )
                ConnectionStatus.REVOKED -> Pair(
                    ContextCompat.getColor(context, android.R.color.darker_gray),
                    ContextCompat.getColor(context, android.R.color.white)
                )
            }
        }
    }

    class ConnectionDiffCallback : DiffUtil.ItemCallback<FirebaseUserConnection>() {
        override fun areItemsTheSame(oldItem: FirebaseUserConnection, newItem: FirebaseUserConnection): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: FirebaseUserConnection, newItem: FirebaseUserConnection): Boolean {
            return oldItem == newItem
        }
    }
}
