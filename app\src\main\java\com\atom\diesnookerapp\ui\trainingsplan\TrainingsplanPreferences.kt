package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.threeten.bp.LocalDate

class TrainingsplanPreferences(context: Context) {
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("TrainingsplanPrefs", Context.MODE_PRIVATE)
    private val gson = Gson()

    fun saveTrainingsplan(trainingsplan: Trainingsplan) {
        val json = gson.toJson(trainingsplan)
        sharedPreferences.edit().putString("currentTrainingsplan", json).apply()
    }

    fun getTrainingsplan(): Trainingsplan? {
        val json = sharedPreferences.getString("currentTrainingsplan", null)
        return if (json != null) {
            gson.fromJson(json, Trainingsplan::class.java)
        } else {
            null
        }
    }

    fun saveTrainingsplanHistory(history: List<Trainingsplan>) {
        val json = gson.toJson(history)
        sharedPreferences.edit().putString("trainingsplanHistory", json).apply()
    }

    fun getTrainingsplanHistory(): List<Trainingsplan> {
        val json = sharedPreferences.getString("trainingsplanHistory", null)
        return if (json != null) {
            val type = object : TypeToken<List<Trainingsplan>>() {}.type
            gson.fromJson(json, type)
        } else {
            emptyList()
        }
    }

    fun clearCurrentTrainingsplan() {
        sharedPreferences.edit().remove("currentTrainingsplan").apply()
    }
    fun clearHistory(){
        sharedPreferences.edit().remove("trainingsplanHistory").apply()
    }
}
