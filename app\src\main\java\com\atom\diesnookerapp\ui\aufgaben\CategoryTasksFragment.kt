package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class CategoryTasksFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var categoryTitleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager

    // Use navArgs delegate to get arguments
    private val args: CategoryTasksFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment - reuse fragment_task_list
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        taskManager = TaskManager(requireContext())

        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView) // Assuming this ID exists in fragment_task_list.xml
        categoryTitleTextView = view.findViewById(R.id.categoryTitleTextView) // Assuming this ID exists

        // Set the category title from arguments
        categoryTitleTextView.text = args.categoryDisplayName

        setupRecyclerView()
        // Initial load of tasks is handled in onResume to ensure consistency
    }

    override fun onResume() {
        super.onResume()
        // Ensure taskManager and taskAdapter are initialized before loading.
        // taskManager is initialized in onViewCreated.
        // taskAdapter is initialized in setupRecyclerView, which is called from onViewCreated.
        if (::taskManager.isInitialized && ::taskAdapter.isInitialized) {
            loadTasksAndUpdateAdapter()
            updatePointsDisplay()
        }
    }

    private fun setupRecyclerView() {
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        // Initialize adapter with an empty list. It will be populated in onResume/loadTasks.
        taskAdapter = TaskAdapter(
            mutableListOf(), // Initial empty list
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
                loadTasksAndUpdateAdapter()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
                loadTasksAndUpdateAdapter()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "Täglich"
                    TaskFrequency.WEEKLY -> {
                        val count = task.weeklyFrequencyCount ?: 1
                        "Wöchentlich (${count}x)"
                    }
                }
            },
            taskManager = taskManager
        )
        taskAdapter.setOnTaskCompletedWithCustomPoints { task, points ->
            taskManager.markTaskCompletedWithCustomPoints(task.id, LocalDate.now(), points)
            updatePointsDisplay()       // To refresh points total
            loadTasksAndUpdateAdapter() // To refresh the list & checkbox state from TaskManager
        }
        recyclerView.adapter = taskAdapter
    }

    private fun loadTasksAndUpdateAdapter() {
        val categoryId = args.categoryId
        val tasks = taskManager.getTasksByCategory(categoryId)
        taskAdapter.submitList(tasks)
    }

    private fun updatePointsDisplay() {
        // Check if pointsTextView has been initialized to prevent crashes if view is destroyed
        if(::pointsTextView.isInitialized) {
            val totalPoints = taskManager.getTotalPoints() // This gets all points, might want category specific later
            pointsTextView.text = "Gesammelte Punkte: $totalPoints"
        }
    }
}
