package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.ui.trainingsplan.ExerciseItem
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import com.google.android.material.button.MaterialButton
import com.google.android.material.textview.MaterialTextView
import androidx.core.os.bundleOf
import kotlinx.coroutines.launch

class BreakbuildingFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var firebaseAuthManager: FirebaseAuthManager
    private lateinit var breakbuildingAdapter: BreakbuildingAdapter
    private var exerciseList = mutableListOf<ExerciseItem>()
    private var categoryName: String? = null
    // It's good practice to have a reference to your RecyclerView if you need to interact with it,
    // e.g. to set visibility of an empty view.
    // private lateinit var recyclerView: RecyclerView 

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            categoryName = it.getString("categoryName")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_breakbuilding, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        firebaseAuthManager = FirebaseAuthManager()

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        // this.recyclerView = recyclerView // Assign if needed elsewhere
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        val exercisePreferences = ExercisePreferences(requireContext()) // Assuming this is still needed by adapter

        breakbuildingAdapter = BreakbuildingAdapter(exerciseList, exercisePreferences) { exerciseItem ->
            findNavController().navigate(
                R.id.action_breakbuildingFragment_to_exerciseDetailFragment, // Ensure this action ID is correct
                bundleOf(
                    "exerciseId" to exerciseItem.id,
                    "exerciseTitle" to exerciseItem.name, // Name from ExerciseItem
                    "exerciseType" to exerciseItem.exerciseType
                )
            )
        }
        recyclerView.adapter = breakbuildingAdapter

        // Set the title of the fragment/toolbar if applicable
        // (activity as? AppCompatActivity)?.supportActionBar?.title = categoryName ?: "Exercises"
        // Or find a TextView in the layout to set the title
        // view.findViewById<MaterialTextView>(R.id.fragmentTitleTextView)?.text = categoryName ?: "Exercises"


        loadExercises()
    }

    private fun loadExercises() {
        if (categoryName == null) {
            Toast.makeText(context, "Category not specified.", Toast.LENGTH_LONG).show()
            // Potentially update UI to show an error or navigate back
            return
        }

        if (!firebaseAuthManager.isLoggedIn()) {
            Toast.makeText(context, "Please log in to view exercises.", Toast.LENGTH_SHORT).show()
            exerciseList.clear()
            breakbuildingAdapter.notifyDataSetChanged()
            // Update UI to show login prompt or empty state
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val fetchedExercises = trainingsplanManager.getExercisesForCategory(categoryName!!)
                exerciseList.clear()
                if (fetchedExercises.isNotEmpty()) {
                    exerciseList.addAll(fetchedExercises)
                } else {
                    Toast.makeText(context, "No exercises found for $categoryName.", Toast.LENGTH_SHORT).show()
                    // Update UI for empty state, e.g., show a message
                }
                breakbuildingAdapter.notifyDataSetChanged()
            } catch (e: Exception) {
                Toast.makeText(context, "Failed to load exercises: ${e.message}", Toast.LENGTH_LONG).show()
                exerciseList.clear()
                breakbuildingAdapter.notifyDataSetChanged()
                // Update UI for error state
            }
        }
    }
}