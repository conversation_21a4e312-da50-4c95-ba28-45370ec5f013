<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Übungstitel"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:layout_margin="16dp"/>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Heutige Ergebnisse"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?android:attr/listDivider"
                android:layout_marginBottom="8dp" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/lastScoreText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:lineSpacingExtra="8dp"
                    android:paddingBottom="16dp" />

            </ScrollView>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/addScoreButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Ergebnis hinzufügen"
            app:icon="@drawable/ic_add"
            app:iconGravity="textStart" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/addTimeButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Zeit hinzufügen"
            app:icon="@drawable/ic_timer"
            app:iconGravity="textStart" />

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/showGraphButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:text="Graph anzeigen"
        app:icon="@drawable/ic_graph"
        app:iconGravity="textStart" />

</LinearLayout>
