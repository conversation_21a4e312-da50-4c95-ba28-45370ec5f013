package com.atom.diesnookerapp.data.firebase

/**
 * Firebase model for user connections
 * Represents a connection between a player and a trainer
 */
data class FirebaseUserConnection(
    var initiatorId: String = "", // Player's user ID (who initiated the connection)
    var targetId: String = "",    // Trainer's user ID
    var status: ConnectionStatus = ConnectionStatus.PENDING,
    var trainerAccess: Boolean = false,      // Access to exercise data and training plans
    var mentalTrainerAccess: Boolean = false, // Access to self-assessment data
    var initiatorName: String = "",  // Player's display name (for UI convenience)
    var targetName: String = "",     // Trainer's display name (for UI convenience)
    var targetEmail: String = "",    // Trainer's email (for invitation and UI)
    var targetRole: UserRole = UserRole.TRAINER, // Role of the target user
    var connectionDate: Long = System.currentTimeMillis() // When the connection was established
) : FirebaseModel() {
    companion object {
        /**
         * Create a connection ID from two user IDs
         * Format: playerId_trainerId
         */
        fun createConnectionId(playerId: String, trainerId: String): String {
            return "${playerId}_${trainerId}"
        }
    }
}

/**
 * Enum for connection status
 */
enum class ConnectionStatus {
    PENDING,  // Invitation sent but not accepted
    ACTIVE,   // Connection accepted and active
    REJECTED, // Connection rejected
    REVOKED   // Connection revoked by the player
}
