package com.atom.diesnookerapp.ui.trainingsplan

import android.animation.ValueAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.format.DateTimeFormatter
import java.util.Locale

class TrainingsplanHistoryAdapter(private var history: List<Trainingsplan>) :
    RecyclerView.Adapter<TrainingsplanHistoryAdapter.HistoryViewHolder>() {

    // Track expanded items
    private val expandedItems = mutableSetOf<Int>()

    class HistoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val monthTextView: TextView = itemView.findViewById(R.id.weekTextView) // ID kept for backward compatibility
        val itemsTextView: TextView = itemView.findViewById(R.id.itemsTextView)
        val completionStatsTextView: TextView = itemView.findViewById(R.id.completionStatsTextView)
        val headerLayout: LinearLayout = itemView.findViewById(R.id.headerLayout)
        val contentLayout: LinearLayout = itemView.findViewById(R.id.contentLayout)
        val expandCollapseIcon: ImageView = itemView.findViewById(R.id.expandCollapseIcon)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.trainingsplan_history_item, parent, false)
        return HistoryViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: HistoryViewHolder, position: Int) {
        val currentPlan = history[position]
        val monthFormatter = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.GERMAN)

        // Set month name
        holder.monthTextView.text = currentPlan.weekStartDate.format(monthFormatter)

        // Calculate completion statistics
        val totalItems = currentPlan.items.size
        val completedItems = currentPlan.items.count { it.isChecked }
        val completionPercentage = if (totalItems > 0) {
            (completedItems.toFloat() / totalItems * 100).toInt()
        } else {
            0
        }

        // Set completion stats text
        holder.completionStatsTextView.text = "Abgeschlossen: $completedItems von $totalItems Übungen ($completionPercentage%)"

        // Set items text - only show completed status
        val itemsText = currentPlan.items.joinToString("\n") {
            "• ${it.name} ${if (it.isChecked) "✓" else "○"}" +
            (if (it.exerciseType != null) " (${it.exerciseType})" else "")
        }
        holder.itemsTextView.text = itemsText

        // Set expanded state
        val isExpanded = expandedItems.contains(position)
        if (isExpanded) {
            holder.contentLayout.visibility = View.VISIBLE
            // Reset height to wrap_content
            val layoutParams = holder.contentLayout.layoutParams
            layoutParams.height = LinearLayout.LayoutParams.WRAP_CONTENT
            holder.contentLayout.layoutParams = layoutParams
        } else {
            holder.contentLayout.visibility = View.GONE
        }
        holder.expandCollapseIcon.rotation = if (isExpanded) 180f else 0f

        // Set click listener for expand/collapse
        holder.headerLayout.setOnClickListener {
            toggleExpansion(holder, position)
        }
    }

    private fun toggleExpansion(holder: HistoryViewHolder, position: Int) {
        val isExpanded = expandedItems.contains(position)

        if (isExpanded) {
            // Collapse
            expandedItems.remove(position)
            animateContentHeight(holder.contentLayout, holder.contentLayout.height, 0)
            animateArrow(holder.expandCollapseIcon, false)
        } else {
            // Expand
            expandedItems.add(position)
            // First make it visible with height 0
            holder.contentLayout.visibility = View.VISIBLE
            holder.contentLayout.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
            val targetHeight = holder.contentLayout.measuredHeight
            holder.contentLayout.layoutParams.height = 0
            // Then animate to the full height
            animateContentHeight(holder.contentLayout, 0, targetHeight)
            animateArrow(holder.expandCollapseIcon, true)
        }
    }

    private fun animateContentHeight(view: View, startHeight: Int, endHeight: Int) {
        val animator = ValueAnimator.ofInt(startHeight, endHeight)
        animator.addUpdateListener { animation ->
            val value = animation.animatedValue as Int
            val layoutParams = view.layoutParams
            layoutParams.height = value
            view.layoutParams = layoutParams

            // When collapsing and reaching 0, set visibility to GONE
            if (endHeight == 0 && value == 0) {
                view.visibility = View.GONE
            }
        }
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.start()
    }

    private fun animateArrow(view: View, isExpanded: Boolean) {
        val startRotation = if (isExpanded) 0f else 180f
        val endRotation = if (isExpanded) 180f else 0f

        val animator = ValueAnimator.ofFloat(startRotation, endRotation)
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.addUpdateListener { animation ->
            view.rotation = animation.animatedValue as Float
        }
        animator.start()
    }

    override fun getItemCount() = history.size

    fun updateData(newHistory: List<Trainingsplan>) {
        val oldHistory = history
        history = newHistory

        // Calculate diff and animate changes
        val diffCallback = object : DiffUtil.Callback() {
            override fun getOldListSize() = oldHistory.size
            override fun getNewListSize() = newHistory.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return oldHistory[oldItemPosition].weekStartDate == newHistory[newItemPosition].weekStartDate
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldPlan = oldHistory[oldItemPosition]
                val newPlan = newHistory[newItemPosition]

                // Compare relevant fields
                return oldPlan.weekStartDate == newPlan.weekStartDate &&
                       oldPlan.items.size == newPlan.items.size &&
                       oldPlan.items.count { it.isChecked } == newPlan.items.count { it.isChecked }
            }
        }

        val diffResult = DiffUtil.calculateDiff(diffCallback)
        diffResult.dispatchUpdatesTo(this)
    }
}
