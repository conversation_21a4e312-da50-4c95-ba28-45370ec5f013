package com.atom.diesnookerapp.ui.ergebniserfassung

import android.animation.ValueAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView
import org.threeten.bp.LocalDate
import org.threeten.bp.YearMonth
import org.threeten.bp.format.DateTimeFormatter
import java.util.Locale

class MonthDateAdapter(
    private var dates: List<LocalDate>,
    private val onDateSelected: (LocalDate) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_MONTH = 0
        private const val VIEW_TYPE_DATE = 1
    }

    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val monthFormatter = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.GERMAN)

    // Data structure to hold our grouped items
    private val items = mutableListOf<DateItem>()

    // Map to track expanded state of each month
    private val expandedMonths = mutableMapOf<YearMonth, Boolean>()

    // Cache for visible items
    private var visibleItems = listOf<DateItem>()

    init {
        updateData(dates)
    }

    // Define sealed class for different item types
    sealed class DateItem {
        data class MonthHeader(val month: YearMonth) : DateItem()
        data class DateEntry(val date: LocalDate) : DateItem()
    }

    // ViewHolder for date items
    class DateViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val dateText: TextView = view.findViewById(R.id.dateText)
    }

    // ViewHolder for month headers
    class MonthViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.monthCardView)
        val monthText: TextView = view.findViewById(R.id.monthText)
        val entriesCountText: TextView = view.findViewById(R.id.entriesCountText)
        val expandCollapseIcon: ImageView = view.findViewById(R.id.expandCollapseIcon)
        val headerLayout: LinearLayout = view.findViewById(R.id.headerLayout)
    }

    override fun getItemViewType(position: Int): Int {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }

        return when (visibleItems[position]) {
            is DateItem.MonthHeader -> VIEW_TYPE_MONTH
            is DateItem.DateEntry -> VIEW_TYPE_DATE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_MONTH -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_month_header, parent, false)
                MonthViewHolder(view)
            }
            VIEW_TYPE_DATE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_date, parent, false)
                DateViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }

        when (val item = visibleItems[position]) {
            is DateItem.MonthHeader -> {
                holder as MonthViewHolder
                val month = item.month

                // Set month name
                holder.monthText.text = month.format(monthFormatter)

                // Count entries in this month
                val entriesCount = items.count {
                    it is DateItem.DateEntry && YearMonth.from(it.date) == month
                }
                holder.entriesCountText.text = "$entriesCount Einträge"

                // Set expand/collapse icon
                val isExpanded = expandedMonths[month] ?: false
                // Set initial rotation without animation
                holder.expandCollapseIcon.rotation = if (isExpanded) 180f else 0f

                // Set click listener for expand/collapse
                holder.headerLayout.setOnClickListener {
                    toggleMonthExpansion(month)
                }
            }
            is DateItem.DateEntry -> {
                holder as DateViewHolder
                val date = item.date

                holder.dateText.text = date.format(dateFormatter)
                holder.cardView.setOnClickListener { onDateSelected(date) }
            }
        }
    }

    // Get visible items based on expansion state
    private fun getVisibleItems(): List<DateItem> {
        val visibleItems = mutableListOf<DateItem>()

        // Group items by month
        val itemsByMonth = items.groupBy { item ->
            when (item) {
                is DateItem.MonthHeader -> item.month
                is DateItem.DateEntry -> YearMonth.from(item.date)
            }
        }

        // Process each month
        val sortedMonths = itemsByMonth.keys.sortedDescending()
        sortedMonths.forEach { month ->
            // Add month header
            val monthHeader = itemsByMonth[month]?.firstOrNull { it is DateItem.MonthHeader }
            if (monthHeader != null) {
                visibleItems.add(monthHeader)
            }

            // Add date items if month is expanded
            val isExpanded = expandedMonths[month] ?: false
            if (isExpanded) {
                val dateItems = itemsByMonth[month]?.filter { it is DateItem.DateEntry }?.sortedByDescending {
                    (it as DateItem.DateEntry).date
                } ?: emptyList()
                visibleItems.addAll(dateItems)
            }
        }

        return visibleItems
    }

    // Update visible items cache
    private fun updateVisibleItems() {
        visibleItems = getVisibleItems()
    }

    override fun getItemCount(): Int {
        // Make sure visible items are up to date
        if (visibleItems.isEmpty()) {
            updateVisibleItems()
        }
        return visibleItems.size
    }

    private fun toggleMonthExpansion(month: YearMonth) {
        // Toggle expansion state
        val isCurrentlyExpanded = expandedMonths[month] ?: false
        expandedMonths[month] = !isCurrentlyExpanded

        // Get old list for diff calculation
        val oldList = visibleItems

        // Recalculate visible items
        updateVisibleItems()

        // Calculate diff and animate changes
        val diffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun getOldListSize() = oldList.size
            override fun getNewListSize() = visibleItems.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldList[oldItemPosition]
                val newItem = visibleItems[newItemPosition]

                return when {
                    oldItem is DateItem.MonthHeader && newItem is DateItem.MonthHeader ->
                        oldItem.month == newItem.month
                    oldItem is DateItem.DateEntry && newItem is DateItem.DateEntry ->
                        oldItem.date == newItem.date
                    else -> false
                }
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return areItemsTheSame(oldItemPosition, newItemPosition)
            }
        }, true)

        // Apply animations
        diffResult.dispatchUpdatesTo(this)

        // Animate the arrow rotation
        val viewHolders = (0 until itemCount).mapNotNull { position ->
            if (visibleItems[position] is DateItem.MonthHeader &&
                (visibleItems[position] as DateItem.MonthHeader).month == month) {
                return@mapNotNull findViewHolderForAdapterPosition(position) as? MonthViewHolder
            }
            null
        }

        viewHolders.forEach { holder ->
            animateArrow(holder.expandCollapseIcon, !isCurrentlyExpanded)
        }
    }

    private fun animateArrow(view: View, isExpanded: Boolean) {
        val startRotation = if (isExpanded) 0f else 180f
        val endRotation = if (isExpanded) 180f else 0f

        val animator = ValueAnimator.ofFloat(startRotation, endRotation)
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.duration = 300
        animator.addUpdateListener { animation ->
            view.rotation = animation.animatedValue as Float
        }
        animator.start()
    }

    // Reference to the RecyclerView
    private var recyclerView: RecyclerView? = null

    // Called when adapter is attached to a RecyclerView
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    // Called when adapter is detached from a RecyclerView
    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    // Helper method to find ViewHolder for adapter position
    private fun findViewHolderForAdapterPosition(position: Int): RecyclerView.ViewHolder? {
        return recyclerView?.findViewHolderForAdapterPosition(position)
    }

    fun updateData(newDates: List<LocalDate>) {
        dates = newDates
        items.clear()

        // Group dates by month
        val datesByMonth = dates.groupBy { YearMonth.from(it) }

        // Sort months in descending order (newest first)
        val sortedMonths = datesByMonth.keys.sortedDescending()

        // Add month headers and date items
        sortedMonths.forEach { month ->
            // Add month header
            items.add(DateItem.MonthHeader(month))

            // Set initial expanded state to false (collapsed)
            if (!expandedMonths.containsKey(month)) {
                expandedMonths[month] = false
            }

            // Add date items for this month
            datesByMonth[month]?.sortedDescending()?.forEach { date ->
                items.add(DateItem.DateEntry(date))
            }
        }

        // Update visible items
        updateVisibleItems()
        notifyDataSetChanged()
    }
}
