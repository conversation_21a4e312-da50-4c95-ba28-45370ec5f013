# Die Snooker App - Cloud Sync

This project adds cloud synchronization capabilities to Die Snooker App, allowing history data to be accessible through a website.

## Features

- Firebase Authentication for secure user access
- Firestore Database for cloud storage of history data
- Background synchronization of app data
- Web interface for viewing history data

## Setup Instructions

### 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and follow the setup wizard
3. Once your project is created, click "Add app" and select Android
4. Enter your package name: `com.atom.diesnookerapp`
5. Download the `google-services.json` file and place it in the `app/` directory of your project

### 2. Enable Authentication

1. In the Firebase Console, go to "Authentication"
2. Click "Get started"
3. Enable the "Email/Password" sign-in method

### 3. Set Up Firestore Database

1. In the Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Start in production mode
4. Choose a location close to your users
5. Set up security rules:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

### 4. Deploy the Web Application

1. Install the Firebase CLI:
   ```
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```
   firebase login
   ```

3. Navigate to the `web` directory and initialize Firebase:
   ```
   cd web
   firebase init
   ```

4. Select "Hosting" when prompted
5. Select your Firebase project
6. Use "." as your public directory
7. Configure as a single-page app: No
8. Set up automatic builds and deploys: No

9. Update the Firebase configuration in `app.js` with your project's configuration (from the Firebase Console)

10. Deploy to Firebase Hosting:
    ```
    firebase deploy
    ```

11. Your web app is now available at the URL provided in the deployment output

## Using the App

### Android App

1. Build and install the app on your device
2. Register a new account or log in with an existing account
3. Your data will automatically sync to the cloud when you're online
4. You can manually sync by going to the Profile screen and tapping "Sync Now"

### Web Interface

1. Go to the URL of your deployed web app
2. Log in with the same account you used in the Android app
3. View your history data organized by category
4. Click on items to expand and see details

## Troubleshooting

- If sync isn't working, check that you have an internet connection
- Make sure you've placed the `google-services.json` file in the correct location
- Check the Firebase Console for any authentication or database errors
- If the web app isn't showing data, make sure you're logged in with the same account as the Android app

## Security Considerations

- All data is associated with a user ID and can only be accessed by that user
- Authentication is required for both the Android app and web interface
- Data is encrypted in transit using HTTPS
- Firestore security rules prevent unauthorized access to data
"# DieSnookerApp" 
