package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView

class HistoryDetailAdapter(
    private val trainingRecords: List<DailyTrainingRecord>,
    private val questionRecords: List<DailyQuestionRecord>
) : RecyclerView.Adapter<HistoryDetailAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val titleText: TextView = view.findViewById(R.id.titleText)
        val contentText: TextView = view.findViewById(R.id.contentText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_history_detail, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val allRecords = trainingRecords + questionRecords
        val record = allRecords[position]

        when (record) {
            is DailyTrainingRecord -> {
                holder.titleText.text = when (record.type) {
                    TrainingType.BEFORE_TRAINING -> "Vor Training"
                    TrainingType.AFTER_TRAINING -> "Nach Training"
                    TrainingType.BEFORE_TOURNAMENT -> "Vor Turnier"
                    TrainingType.AFTER_TOURNAMENT -> "Nach Turnier"
                }
                val scores = record.items.joinToString("\n") { item -> 
                    "${item.title}: ${item.score ?: "Keine Bewertung"}"
                }
                holder.contentText.text = scores
            }
            is DailyQuestionRecord -> {
                holder.titleText.text = when (record.type) {
                    QuestionType.BEFORE_TRAINING -> "Fragen vor Training"
                    QuestionType.AFTER_TRAINING -> "Fragen nach Training"
                    QuestionType.BEFORE_TOURNAMENT -> "Fragen vor Turnier"
                    QuestionType.AFTER_TOURNAMENT -> "Fragen nach Turnier"
                }
                val answers = record.questions.joinToString("\n") { question ->
                    "${question.title}\nAntwort: ${question.answer ?: "Keine Antwort"}"
                }
                holder.contentText.text = answers
            }
        }
    }

    override fun getItemCount() = trainingRecords.size + questionRecords.size
} 