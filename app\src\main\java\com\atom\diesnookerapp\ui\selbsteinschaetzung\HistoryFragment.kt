package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.format.DateTimeFormatter
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton

class HistoryFragment : Fragment() {
    private lateinit var trainingPreferences: TrainingPreferences
    private lateinit var questionPreferences: QuestionPreferences
    private var recyclerView: RecyclerView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_history, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())
        questionPreferences = QuestionPreferences(requireContext())

        // Set title
        view.findViewById<androidx.appcompat.widget.AppCompatTextView>(R.id.titleText).text = "Verlauf"

        recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        view.findViewById<ExtendedFloatingActionButton>(R.id.graphButton).setOnClickListener {
            findNavController().navigate(R.id.action_historyFragment_to_graphFragment)
        }

        val trainingRecords = trainingPreferences.getRecords()
        val questionRecords = questionPreferences.getRecords()

        val adapter = HistoryAdapter(trainingRecords, questionRecords) { date ->
            findNavController().navigate(
                R.id.action_historyFragment_to_historyDetailFragment,
                Bundle().apply {
                    putString("date", date.toString())
                }
            )
        }
        recyclerView?.adapter = adapter
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.menu_history, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_clear -> {
                showClearConfirmationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showClearConfirmationDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Verlauf löschen")
            .setMessage("Möchten Sie wirklich den gesamten Verlauf löschen?")
            .setPositiveButton("Löschen") { _, _ ->
                trainingPreferences.clearAll()
                questionPreferences.clearAll()
                refreshList()
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun refreshList() {
        val trainingRecords = trainingPreferences.getRecords()
        val questionRecords = questionPreferences.getRecords()
        val adapter = HistoryAdapter(trainingRecords, questionRecords) { date ->
            findNavController().navigate(
                R.id.action_historyFragment_to_historyDetailFragment,
                Bundle().apply {
                    putString("date", date.toString())
                }
            )
        }
        recyclerView?.adapter = adapter
    }
}