package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinitionRepository
import kotlinx.coroutines.launch
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import java.util.Locale

class ExerciseHistoryDetailFragment : Fragment() {
    private lateinit var exercisePreferences: ExercisePreferences
    private lateinit var adapter: ExerciseHistoryAdapter
    private var selectedDate: LocalDate? = null
    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy", Locale.GERMAN)

    private val userExerciseDefinitionRepository = UserExerciseDefinitionRepository()
    private val authManager = FirebaseAuthManager()
    private var exerciseDefinitions: Map<String, UserExerciseDefinition> = emptyMap()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString("date")?.let {
            selectedDate = LocalDate.parse(it)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exercise_history_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        exercisePreferences = ExercisePreferences(requireContext())

        view.findViewById<TextView>(R.id.dateText).text = selectedDate?.format(dateFormatter)

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        adapter = ExerciseHistoryAdapter(emptyList())
        recyclerView.adapter = adapter

        fetchExerciseDefinitionsAndLoadHistory()
    }

    private fun fetchExerciseDefinitionsAndLoadHistory() {
        lifecycleScope.launch {
            val userId = authManager.getCurrentUserId()
            if (userId == null) {
                // Handle not logged in user
                return@launch
            }

            val result = userExerciseDefinitionRepository.getExercisesForUser(userId)
            if (result.isSuccess) {
                exerciseDefinitions = result.getOrThrow().associateBy { it.id }
                selectedDate?.let { loadExercisesForDate(it) }
            } else {
                // Handle error
            }
        }
    }

    private fun loadExercisesForDate(date: LocalDate) {
        val startOfDay = date.atStartOfDay()
        val endOfDay = date.plusDays(1).atStartOfDay()

        val records = exercisePreferences.getRecords()
            .filter { record ->
                record.timestamp.isAfter(startOfDay) &&
                        record.timestamp.isBefore(endOfDay)
            }
            .groupBy { it.exerciseId }
            .map { (exerciseId, exerciseRecords) ->
                val definition = exerciseDefinitions[exerciseId]
                val title = definition?.name ?: exerciseId.replace('_', ' ').capitalize()
                val category = definition?.category ?: "Andere"

                val attempts = exerciseRecords
                    .sortedBy { it.timestamp }
                    .mapIndexed { index, record ->
                        Attempt(
                            number = index + 1,
                            score = record.score,
                            timeInMinutes = record.timeInMinutes,
                            spelt = record.spelt,
                            isCompletionMarker = record.isCompletionMarker
                        )
                    }

                ExerciseHistoryItem(
                    exerciseId = exerciseId,
                    exerciseTitle = title,
                    category = category,
                    attempts = attempts
                )
            }
            .groupBy { it.category }
            .flatMap { (category, items) ->
                listOf(
                    ExerciseHistoryItem(
                        exerciseId = "",
                        exerciseTitle = "",
                        category = category,
                        attempts = emptyList(),
                        isHeader = true
                    )
                ) + items.sortedBy { it.exerciseTitle }
            }

        adapter.updateItems(records)
    }
}