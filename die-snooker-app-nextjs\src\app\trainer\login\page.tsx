"use client";

import { useState, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import Link from 'next/link';

export default function TrainerLoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check if user is a trainer
      const userProfileRef = doc(db, "user_profiles", user.uid);
      const userProfileSnap = await getDoc(userProfileRef);

      if (!userProfileSnap.exists()) {
        setError('Kein Trainer-Profil gefunden. Bitte registrieren Sie sich zuerst als Trainer.');
        await auth.signOut();
        return;
      }

      const userProfile = userProfileSnap.data();
      if (userProfile.role !== 'TRAINER' && userProfile.role !== 'MENTAL_TRAINER') {
        setError('Zugriff verweigert. Nur Trainer können sich hier anmelden.');
        await auth.signOut();
        return;
      }

      if (!userProfile.emailVerified && !user.emailVerified) {
        setError('Bitte bestätigen Sie zuerst Ihre E-Mail-Adresse, bevor Sie sich anmelden.');
        await auth.signOut();
        return;
      }

      router.push('/trainer/dashboard');
    } catch (err: any) {
      // Translate common Firebase errors to German
      let errorMessage = err.message;
      if (err.code === 'auth/user-not-found') {
        errorMessage = 'Trainer nicht gefunden. Bitte überprüfen Sie Ihre E-Mail-Adresse.';
      } else if (err.code === 'auth/wrong-password') {
        errorMessage = 'Falsches Passwort. Bitte versuchen Sie es erneut.';
      } else if (err.code === 'auth/invalid-email') {
        errorMessage = 'Ungültige E-Mail-Adresse.';
      } else if (err.code === 'auth/too-many-requests') {
        errorMessage = 'Zu viele Anmeldeversuche. Bitte versuchen Sie es später erneut.';
      }

      setError(errorMessage);
      console.error("Trainer login error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-indigo-100">
      <div className="bg-white p-8 rounded-xl shadow-lg w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Die Snooker App</h1>
          <h2 className="text-xl font-semibold text-purple-600">Trainer-Anmeldung</h2>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
            <p className="text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              E-Mail-Adresse
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Passwort
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
              placeholder="••••••••"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {loading ? 'Anmeldung läuft...' : 'Anmelden'}
          </button>
        </form>

        <div className="mt-6 space-y-3 text-center">
          <p className="text-sm text-gray-600">
            Noch kein Trainer-Konto? <Link href="/trainer/register" className="font-medium text-purple-600 hover:text-purple-500 transition-colors">Hier registrieren</Link>
          </p>
          <div className="border-t border-gray-200 pt-3">
            <p className="text-sm text-gray-600">
              Sind Sie Athlet? <Link href="/login" className="font-medium text-indigo-600 hover:text-indigo-500 transition-colors">Athleten-Anmeldung</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
