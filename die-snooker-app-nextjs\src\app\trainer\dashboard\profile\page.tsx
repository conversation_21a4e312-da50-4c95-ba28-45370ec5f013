"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import { doc, updateDoc, serverTimestamp } from "firebase/firestore";
import { useState, FormEvent, useEffect } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

function TrainerProfilePage() {
  const { currentUser, userProfile, loading: authLoading } = useAuth();

  const [displayName, setDisplayName] = useState('');
  const [experience, setExperience] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    if (userProfile) {
      setDisplayName(userProfile.displayName || '');
      setExperience(userProfile.experience || '');
    }
  }, [userProfile]);

  const handleProfileUpdate = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;

    setIsSubmitting(true);
    setMessage(null);
    try {
      const profileRef = doc(db, "user_profiles", currentUser.uid);
      await updateDoc(profileRef, {
        displayName: displayName, // Allow updating display name as well
        experience: experience,
        lastUpdated: serverTimestamp(),
      });
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
      // Note: AuthContext will eventually pick up the change if userProfile is re-fetched or if we manually update it.
      // For immediate reflection, you might need to update the local userProfile state in AuthContext or trigger a refetch.
    } catch (err: any) {
      console.error("Error updating profile:", err);
      setMessage({ type: 'error', text: `Failed to update profile. ${err.message}` });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (authLoading || !userProfile) return <LoadingSpinner />;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">My Trainer Profile</h1>

      {message && (
        <div className={`p-3 mb-4 rounded-md text-sm ${message.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {message.text}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleProfileUpdate} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email (cannot be changed)
            </label>
            <input
              type="email"
              id="email"
              value={currentUser?.email || ''}
              disabled
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="displayName" className="block text-sm font-medium text-gray-700">
              Display Name
            </label>
            <input
              type="text"
              id="displayName"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700">
              Role (cannot be changed)
            </label>
            <input
              type="text"
              id="role"
              value={userProfile.role || ''}
              disabled
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="experience" className="block text-sm font-medium text-gray-700">
              Experience / Specialization
            </label>
            <textarea
              id="experience"
              value={experience}
              onChange={(e) => setExperience(e.target.value)}
              rows={4}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              placeholder="Describe your coaching experience, areas of expertise, etc."
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting || authLoading}
              className="w-full md:w-auto px-6 py-2 bg-purple-600 text-white font-semibold rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-400"
            >
              {isSubmitting ? 'Updating...' : 'Update Profile'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default withAuth(TrainerProfilePage, {
  roles: ['TRAINER', 'MENTAL_TRAINER'],
  redirectTo: '/trainer/login'
});
