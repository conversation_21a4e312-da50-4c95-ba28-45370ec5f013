* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #e0e7ff 0%, #f4f4f4 100%);
    color: #222;
    min-height: 100vh;
}

.container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 260px;
    background: #232946;
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding: 32px 16px;
    box-shadow: 2px 0 12px rgba(0,0,0,0.04);
}

.sidebar h1 {
    font-size: 1.7rem;
    margin-bottom: 2rem;
    letter-spacing: 1px;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.sidebar nav ul li {
    margin-bottom: 10px;
}

.sidebar nav ul li a {
    display: block;
    color: #fff;
    background: transparent;
    padding: 12px 18px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.2s;
}

.sidebar nav ul li a.active,
.sidebar nav ul li a:hover {
    background: #eebbc3;
    color: #232946;
}

#user-info {
    margin-top: auto;
    text-align: center;
}

#user-email {
    display: block;
    margin-bottom: 10px;
    font-size: 1rem;
    color: #eebbc3;
}

main {
    flex: 1;
    padding: 40px 5vw;
    background: #f4f4f4;
    min-height: 100vh;
}

#login-container {
    max-width: 400px;
    margin: 60px auto;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 32px rgba(35,41,70,0.08);
    padding: 32px 28px;
}

.btn {
    background: #232946;
    color: #fff;
    border: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s;
}

.btn-primary {
    background: #eebbc3;
    color: #232946;
}

.btn:hover, .btn-primary:hover {
    background: #b8c1ec;
    color: #232946;
}

section {
    display: none;
}

section.active {
    display: block;
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px);}
    to { opacity: 1; transform: translateY(0);}
}

.history-list {
    margin-top: 32px;
}

.history-item {
    background: #fff;
    border: 1px solid #e0e7ff;
    border-radius: 10px;
    padding: 24px 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.04);
    transition: box-shadow 0.2s;
}

.history-item:hover {
    box-shadow: 0 6px 24px rgba(35,41,70,0.10);
}

.history-item h3 {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
}

.history-item-details {
    display: none;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e0e7ff;
}

.history-item.expanded .history-item-details {
    display: block;
}

.toggle-btn {
    background: #eebbc3;
    color: #232946;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.2s;
}

.toggle-btn:hover {
    background: #b8c1ec;
}

.hidden {
    display: none;
}

/* Chart styling */
/* --- Übungen Page Styling --- */
.exercise-category {
    margin-bottom: 32px;
}

.exercise-category-header {
    cursor: pointer;
    background: linear-gradient(90deg, #e0e7ff 60%, #b8c1ec 100%);
    padding: 18px 28px;
    border-radius: 14px;
    margin-top: 32px;
    margin-bottom: 0;
    user-select: none;
    font-size: 1.25rem;
    font-weight: 600;
    box-shadow: 0 2px 12px rgba(35,41,70,0.06);
    transition: background 0.2s, box-shadow 0.2s;
}

.exercise-category-header.expanded {
    background: linear-gradient(90deg, #b8c1ec 60%, #e0e7ff 100%);
    box-shadow: 0 4px 24px rgba(35,41,70,0.10);
}

.exercise-category-exercises {
    margin-top: 12px;
    transition: max-height 0.3s cubic-bezier(.4,0,.2,1);
    overflow: hidden;
}

.history-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.06);
    margin-bottom: 18px;
    padding: 0;
    transition: border 0.2s, box-shadow 0.2s;
    border: 2px solid transparent;
    cursor: pointer;
}

.history-item.expanded {
    border: 2px solid #b8c1ec;
    box-shadow: 0 6px 24px rgba(35,41,70,0.12);
    background: #f7f7fb;
}

.history-item h3 {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
    padding: 18px 24px;
    border-radius: 12px 12px 0 0;
    background: transparent;
    display: flex;
    align-items: center;
    transition: background 0.2s;
}

.history-item.expanded h3 {
    background: #e0e7ff;
}

.history-item-details {
    display: none;
    padding: 0 24px 18px 24px;
    border-radius: 0 0 12px 12px;
    background: transparent;
    animation: fadeIn 0.3s;
}

.history-item.expanded .history-item-details {
    display: block;
    background: #f7f7fb;
}

select {
    font-size: 1rem;
    border: 1px solid #b8c1ec;
    border-radius: 6px;
    padding: 6px 14px;
    background: #fff;
    margin-bottom: 10px;
    margin-top: 8px;
    margin-right: 10px;
}

canvas {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.04);
    margin-bottom: 32px;
    padding: 16px;
    max-width: 100%;
}

.card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.04);
    padding: 24px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e7ff;
    border-radius: 6px;
    font-size: 1rem;
}

.checkbox-group {
    margin-top: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 8px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.connection-item {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.04);
    padding: 20px;
    margin-bottom: 16px;
}

.connection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.connection-name {
    font-size: 1.1rem;
    font-weight: 600;
}

.connection-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: #ffeeba;
    color: #856404;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.status-revoked {
    background-color: #e2e3e5;
    color: #383d41;
}

.connection-email,
.connection-role,
.connection-permissions {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.connection-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
}

.connection-actions button {
    margin-left: 8px;
}

.empty-state {
    text-align: center;
    padding: 32px;
    color: #6c757d;
    font-style: italic;
    display: none;
}

/* Trainer dashboard styles */
.player-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.player-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.04);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.player-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(35,41,70,0.1);
}

.player-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.player-email {
    color: #6c757d;
    margin-bottom: 16px;
}

.player-data-preview {
    display: flex;
    justify-content: space-between;
}

.data-stat {
    text-align: center;
    flex: 1;
}

.data-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #232946;
}

.data-label {
    font-size: 0.8rem;
    color: #6c757d;
}

@media (max-width: 700px) {
    .exercise-category-header {
        font-size: 1rem;
        padding: 14px 12px;
    }
    .history-item h3 {
        font-size: 1rem;
        padding: 14px 12px;
    }
    .history-item-details {
        padding: 0 12px 12px 12px;
    }
    .player-list {
        grid-template-columns: 1fr;
    }
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    padding: 24px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}

.modal-actions button {
    margin-left: 12px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.form-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 0.9rem;
}

.form-footer a {
    color: #232946;
    text-decoration: underline;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}
