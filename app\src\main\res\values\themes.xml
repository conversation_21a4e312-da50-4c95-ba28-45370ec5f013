<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <!-- Snooker Theme -->
    <style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_500</item>
        <item name="colorPrimaryVariant">@color/snooker_red_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/snooker_green_500</item>
        <item name="colorSecondaryVariant">@color/snooker_green_700</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_200</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/snooker_green_200</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style>

    <style name="SnookerCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Blue Theme -->
    <style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/blue_500</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/orange_500</item>
        <item name="colorSecondaryVariant">@color/orange_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/BlueCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style>

    <style name="BlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Dark Blue Theme -->
    <style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_500</item>
        <item name="colorPrimaryVariant">@color/dark_blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/cyan_500</item>
        <item name="colorSecondaryVariant">@color/cyan_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style>

    <style name="DarkBlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Ocean Theme -->
    <style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/ocean_medium</item>
        <item name="colorPrimaryVariant">@color/ocean_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/ocean_light</item>
        <item name="colorSecondaryVariant">@color/ocean_lightest</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_lightest</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/ocean_lightest</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="materialCardViewStyle">@style/OceanCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="OceanCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Crimson Theme -->
    <style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/crimson_medium</item>
        <item name="colorPrimaryVariant">@color/crimson_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/crimson_light</item>
        <item name="colorSecondaryVariant">@color/crimson_lightest</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/crimson_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="CrimsonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Neon Theme -->
    <style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_3</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/neon_pink_2</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/neon_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        <!-- Card customization -->
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="materialCardViewStyle">@style/NeonCardStyle</item>

        <!-- Popup menu styling -->
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>

    <style name="NeonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
</resources>
