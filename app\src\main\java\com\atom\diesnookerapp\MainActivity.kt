package com.atom.diesnookerapp

import android.os.Bundle
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.view.WindowCompat
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemePreferences

class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        // Apply saved theme before setting content view
        applyTheme()

        super.onCreate(savedInstanceState)

        // Make sure the status bar is properly handled
        WindowCompat.setDecorFitsSystemWindows(window, true)

        // Set status bar to be transparent
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )

        setContentView(R.layout.activity_main)

        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController

        val navView: BottomNavigationView = findViewById(R.id.nav_view)
        navView.setupWithNavController(navController)
    }

    private fun applyTheme() {
        val themePreferences = ThemePreferences(this)
        val themeMode = themePreferences.getThemeMode()
        val customTheme = themePreferences.getCustomTheme()

        // First, reset any night mode to follow system
        // This helps prevent theme conflicts
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)

        when (themeMode) {
            // Standard themes use AppCompatDelegate
            AppCompatDelegate.MODE_NIGHT_NO,
            AppCompatDelegate.MODE_NIGHT_YES,
            AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM -> {
                AppCompatDelegate.setDefaultNightMode(themeMode)
            }
            // Custom themes
            ThemePreferences.THEME_SNOOKER -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_Snooker)
            }
            ThemePreferences.THEME_BLUE -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_Blue)
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_DarkBlue)
            }
            ThemePreferences.THEME_OCEAN -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_Ocean)
            }
            ThemePreferences.THEME_CRIMSON -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_Crimson)
            }
            ThemePreferences.THEME_NEON -> {
                // Force light mode first to clear any dark theme settings
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                setTheme(R.style.Theme_DieSnookerApp_Neon)
            }
            // Default to system theme if unknown
            else -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
        }
    }
}