package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView

class QuestionsAdapter(
    private val items: List<QuestionItem>,
    private val onItemClick: (QuestionItem) -> Unit
) : RecyclerView.Adapter<QuestionsAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val titleText: TextView = view.findViewById(R.id.titleText)
        val answerText: TextView = view.findViewById(R.id.answerText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_question, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.titleText.text = item.title
        holder.answerText.text = item.answer.ifEmpty { "Keine Antwort" }
        holder.cardView.setOnClickListener { onItemClick(item) }
    }

    override fun getItemCount() = items.size
} 