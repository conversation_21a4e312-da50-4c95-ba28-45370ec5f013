<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/darkBlueThemePreview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Dark Blue Theme"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:background="@color/dark_blue_500" />

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:background="@color/cyan_500" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/dark_blue_200" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/dark_blue_500" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/dark_blue_700" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/cyan_200" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_weight="1"
                android:background="@color/cyan_700" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
