<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="12dp"
    android:paddingBottom="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/categoryHeaderNameTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAppearance="?attr/textAppearanceSubtitle1"
        android:textStyle="bold"
        tools:text="Category Name" />

    <ImageView
        android:id="@+id/categoryHeaderExpandIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_expand_more"
        app:tint="?attr/colorControlNormal"
        android:contentDescription="Expand/Collapse Category" />

</LinearLayout>
