"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import {
  collection, query, where, getDocs, doc, updateDoc, serverTimestamp, Timestamp
} from "firebase/firestore";
import { useEffect, useState } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface ConnectionRequest {
  id: string; // Firestore document ID
  initiatorId: string; // Player's UID
  targetId: string;    // Trainer's UID (currentUser.uid)
  status: 'PENDING' | 'ACTIVE' | 'DECLINED';
  trainerType: 'TRAINER' | 'MENTAL_TRAINER';
  permissions: {
    exerciseAccess: boolean;
    selfAssessmentAccess: boolean;
  };
  createdAt: Timestamp;
  initiatorEmail?: string;
  initiatorName?: string;
}

function ConnectionRequestsPage() {
  const { currentUser } = useAuth();
  const [requests, setRequests] = useState<ConnectionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionMessage, setActionMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const fetchRequests = async () => {
    if (!currentUser) return;
    setLoading(true);
    setError(null);
    try {
      const q = query(
        collection(db, "user_connections"),
        where("targetId", "==", currentUser.uid),
        where("status", "==", "PENDING") // Only fetch pending requests
      );
      const querySnapshot = await getDocs(q);
      const fetchedRequests = querySnapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() } as ConnectionRequest))
                                           .sort((a,b) => (a.createdAt?.toMillis() || 0) - (b.createdAt?.toMillis() || 0)); // oldest first
      setRequests(fetchedRequests);
    } catch (err: any) {
      console.error("Error fetching connection requests:", err);
      setError("Failed to load requests. " + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [currentUser]);

  const handleUpdateRequest = async (requestId: string, newStatus: 'ACTIVE' | 'DECLINED') => {
    setLoading(true);
    setActionMessage(null);
    try {
      const requestRef = doc(db, "user_connections", requestId);
      await updateDoc(requestRef, {
        status: newStatus,
        updatedAt: serverTimestamp()
      });
      setActionMessage({ type: 'success', text: `Request ${newStatus === 'ACTIVE' ? 'accepted' : 'declined'} successfully.` });
      fetchRequests(); // Refresh list
    } catch (err: any) {
      console.error(`Error updating request ${requestId} to ${newStatus}:`, err);
      setActionMessage({ type: 'error', text: `Failed to update request. ${err.message}` });
      setLoading(false);
    }
  };

  if (loading && requests.length === 0) return <LoadingSpinner />; // Show spinner only if initial load and no data yet.
  if (error && !actionMessage) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Player Connection Requests</h1>

      {actionMessage && (
        <div className={`p-3 mb-4 rounded-md text-sm ${actionMessage.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {actionMessage.text}
        </div>
      )}

      {requests.length === 0 && !loading && (
        <p className="text-gray-600 bg-white p-6 rounded-lg shadow-md">No pending connection requests.</p>
      )}

      <div className="space-y-4">
        {requests.map(req => (
          <div key={req.id} className="bg-white p-4 rounded-lg shadow-md">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <p className="font-medium text-gray-800">
                        From: {req.initiatorName || req.initiatorEmail || 'Unknown Player'}
                    </p>
                    <p className="text-sm text-gray-600">
                        Requested for: {req.trainerType} role
                    </p>
                    <p className="text-sm text-gray-600">
                        Permissions Requested:
                        {req.permissions.exerciseAccess && " Übungen & Trainingsplan,"}
                        {req.permissions.selfAssessmentAccess && " Selbsteinschätzung"}
                    </p>
                    <p className="text-xs text-gray-500">
                        Received: {req.createdAt ? new Date(req.createdAt.toDate()).toLocaleString('de-DE') : 'N/A'}
                    </p>
                </div>
                <div className="mt-3 md:mt-0 flex space-x-3">
                    <button
                        onClick={() => handleUpdateRequest(req.id, 'ACTIVE')}
                        disabled={loading}
                        className="px-4 py-2 bg-green-500 text-white text-sm font-semibold rounded-md hover:bg-green-600 disabled:bg-gray-300"
                    >
                        Accept
                    </button>
                    <button
                        onClick={() => handleUpdateRequest(req.id, 'DECLINED')}
                        disabled={loading}
                        className="px-4 py-2 bg-red-500 text-white text-sm font-semibold rounded-md hover:bg-red-600 disabled:bg-gray-300"
                    >
                        Decline
                    </button>
                </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default withAuth(ConnectionRequestsPage, {
  roles: ['TRAINER', 'MENTAL_TRAINER'],
  redirectTo: '/trainer/login'
});
