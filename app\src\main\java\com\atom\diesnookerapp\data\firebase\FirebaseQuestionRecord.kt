package com.atom.diesnookerapp.data.firebase

import com.atom.diesnookerapp.ui.selbsteinschaetzung.DailyQuestionRecord
import com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionItem
import com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionType

/**
 * Firebase model for question records
 */
data class FirebaseQuestionRecord(
    var date: String = "", // ISO format date string
    var type: String = "", // Enum name as string
    var questions: List<FirebaseQuestionItem> = emptyList()
) : FirebaseModel() {

    companion object {
        fun fromDailyQuestionRecord(record: DailyQuestionRecord, userId: String): FirebaseQuestionRecord {
            return FirebaseQuestionRecord(
                date = record.date.toString(),
                type = record.type.name,
                questions = record.questions.map { FirebaseQuestionItem.fromQuestionItem(it) }
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }

    fun toDailyQuestionRecord(): DailyQuestionRecord {
        return DailyQuestionRecord(
            date = org.threeten.bp.LocalDate.parse(date),
            type = QuestionType.valueOf(type),
            questions = questions.map { it.toQuestionItem() }
        )
    }
}

/**
 * Firebase model for question items
 */
data class FirebaseQuestionItem(
    var id: Int = 0,
    var title: String = "",
    var answer: String = ""
) {
    companion object {
        fun fromQuestionItem(item: QuestionItem): FirebaseQuestionItem {
            return FirebaseQuestionItem(
                id = item.id,
                title = item.title,
                answer = item.answer
            )
        }
    }

    fun toQuestionItem(): QuestionItem {
        return QuestionItem(
            id = id,
            title = title,
            answer = answer
        )
    }
}
