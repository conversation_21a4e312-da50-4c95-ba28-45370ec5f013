package com.atom.diesnookerapp.ui.auth

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.data.firebase.SyncManager
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ProfileFragment : Fragment() {

    private lateinit var emailTextView: TextView
    private lateinit var syncStatusTextView: TextView
    private lateinit var lastSyncTextView: TextView
    private lateinit var syncResultTextView: TextView
    private lateinit var syncNowButton: Button
    private lateinit var logoutButton: Button
    private lateinit var progressBar: ProgressBar

    private val authManager = FirebaseAuthManager()
    private lateinit var syncManager: SyncManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_profile, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        syncManager = SyncManager(requireContext())

        // Initialize views
        emailTextView = view.findViewById(R.id.emailTextView)
        syncStatusTextView = view.findViewById(R.id.syncStatusTextView)
        lastSyncTextView = view.findViewById(R.id.lastSyncTextView)
        syncResultTextView = view.findViewById(R.id.syncResultTextView)
        syncNowButton = view.findViewById(R.id.syncNowButton)
        logoutButton = view.findViewById(R.id.logoutButton)
        progressBar = view.findViewById(R.id.progressBar)

        // Set up UI
        updateUI()

        // Set up click listeners
        syncNowButton.setOnClickListener {
            performSync()
        }

        logoutButton.setOnClickListener {
            logout()
        }
    }

    private fun updateUI() {
        // Update email
        emailTextView.text = authManager.getCurrentUser()?.email ?: "Nicht angemeldet"

        // Update sync status
        syncStatusTextView.text = if (authManager.isLoggedIn()) "Aktiv" else "Inaktiv"

        // Update last sync time
        val lastSyncTime = getLastSyncTime()
        lastSyncTextView.text = if (lastSyncTime > 0) {
            val dateFormat = SimpleDateFormat("dd.MM.yyyy HH:mm", Locale.getDefault())
            dateFormat.format(Date(lastSyncTime))
        } else {
            "Nie"
        }

        // Update sync result
        val lastSyncResult = getLastSyncResult()
        syncResultTextView.text = lastSyncResult.ifEmpty { "-" }
    }

    private fun performSync() {
        if (!authManager.isLoggedIn()) {
            Toast.makeText(
                requireContext(),
                "Du musst angemeldet sein, um zu synchronisieren",
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        setLoading(true)

        lifecycleScope.launch {
            val result = syncManager.performFullSync()

            if (result.isSuccess) {
                val syncResult = result.getOrThrow()
                val resultText = if (syncResult.totalCleanupCount > 0) {
                    "Hochgeladen: ${syncResult.totalUploaded}, " +
                    "Heruntergeladen: ${syncResult.totalDownloaded}, " +
                    "Duplikate entfernt: ${syncResult.totalCleanupCount}"
                } else {
                    "Hochgeladen: ${syncResult.totalUploaded}, " +
                    "Heruntergeladen: ${syncResult.totalDownloaded}"
                }

                // Save sync result
                saveLastSyncResult(resultText)
                saveLastSyncTime(System.currentTimeMillis())

                Toast.makeText(
                    requireContext(),
                    "Synchronisierung erfolgreich",
                    Toast.LENGTH_SHORT
                ).show()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Synchronisierung fehlgeschlagen: ${result.exceptionOrNull()?.message ?: "Unbekannter Fehler"}",
                    Toast.LENGTH_SHORT
                ).show()
            }

            updateUI()
            setLoading(false)
        }
    }

    private fun logout() {
        // Cancel sync work
        syncManager.cancelSyncWork()

        // Logout
        authManager.logout()

        // Navigate to login
        findNavController().navigate(R.id.loginFragment)
    }

    private fun setLoading(isLoading: Boolean) {
        progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        syncNowButton.isEnabled = !isLoading
        logoutButton.isEnabled = !isLoading
    }

    private fun saveLastSyncTime(time: Long) {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        prefs.edit().putLong("last_sync_time", time).apply()
    }

    private fun getLastSyncTime(): Long {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        return prefs.getLong("last_sync_time", 0)
    }

    private fun saveLastSyncResult(result: String) {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        prefs.edit().putString("last_sync_result", result).apply()
    }

    private fun getLastSyncResult(): String {
        val prefs = requireContext().getSharedPreferences("sync_prefs", Context.MODE_PRIVATE)
        return prefs.getString("last_sync_result", "") ?: ""
    }
}
