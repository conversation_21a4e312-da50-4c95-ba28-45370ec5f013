package com.atom.diesnookerapp.ui.aufgaben

// Task data class should already exist from previous work in the same package.
// No explicit import needed if Task.kt is in com.atom.diesnookerapp.ui.aufgaben

sealed class ManageTaskListItem {
    data class HeaderItem(
        val categoryId: String,
        val categoryDisplayName: String,
        var isExpanded: Boolean = true // Default to expanded
    ) : ManageTaskListItem() {
        // Standard data class equals/hashCode will include isExpanded.
        // This means if isExpanded changes, DiffUtil will see it as a content change.
    }

    data class TaskDataItem(
        val task: Task
    ) : ManageTaskListItem() {
        // Task is a data class, its equals/hashCode is used for content comparison.
        // For item identity in DiffUtil, we'd typically compare task.id.
    }
}
