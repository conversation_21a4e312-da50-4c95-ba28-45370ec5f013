<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stunden:"
        android:labelFor="@+id/edittext_manual_hours" />

    <EditText
        android:id="@+id/edittext_manual_hours"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="number"
        android:maxLength="2"
        android:hint="0" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Minuten:"
        android:layout_marginTop="16dp"
        android:labelFor="@+id/edittext_manual_minutes" />

    <EditText
        android:id="@+id/edittext_manual_minutes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="number"
        android:maxLength="2"
        android:hint="0" />

</LinearLayout>
