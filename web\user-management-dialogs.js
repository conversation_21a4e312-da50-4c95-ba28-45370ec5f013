// Handle adding a trainer
function handleAddTrainer() {
    console.log("handleAddTrainer function called");

    // Check if elements exist
    if (!trainerEmailInput || !trainerTypeSelect || !exerciseAccessCheckbox || !selfAssessmentAccessCheckbox || !addTrainerBtn) {
        console.error("Required DOM elements not found for adding trainer");
        alert("Es ist ein Fehler aufgetreten. Bitte laden Sie die Seite neu.");
        return;
    }

    const email = trainerEmailInput.value.trim();
    const trainerType = trainerTypeSelect.value;
    const exerciseAccess = exerciseAccessCheckbox.checked;
    const selfAssessmentAccess = selfAssessmentAccessCheckbox.checked;

    console.log("Adding trainer with email:", email);
    console.log("Trainer type:", trainerType);
    console.log("Exercise access:", exerciseAccess);
    console.log("Self-assessment access:", selfAssessmentAccess);

    if (!email) {
        alert('Bitte gib eine E-Mail-Adresse ein.');
        return;
    }

    if (!exerciseAccess && !selfAssessmentAccess) {
        alert('Bitte wähle mindestens eine Berechtigung aus.');
        return;
    }

    // Disable button during operation
    addTrainerBtn.disabled = true;
    addTrainerBtn.textContent = "Wird hinzugefügt...";

    // First, find the user by email
    db.collection('user_profiles')
        .where('email', '==', email)
        .get()
        .then(snapshot => {
            if (snapshot.empty) {
                alert('Kein Benutzer mit dieser E-Mail-Adresse gefunden.');
                addTrainerBtn.disabled = false;
                addTrainerBtn.textContent = "Hinzufügen";
                return;
            }

            const targetUser = snapshot.docs[0].data();
            const targetUserId = snapshot.docs[0].id;

            console.log("Found target user:", targetUserId);
            console.log("Target user role:", targetUser.role);

            // Check if user has the correct role
            if (targetUser.role !== trainerType) {
                alert(`Der Benutzer mit der E-Mail ${email} ist kein ${getRoleDisplayName(trainerType)}.`);
                addTrainerBtn.disabled = false;
                addTrainerBtn.textContent = "Hinzufügen";
                return;
            }

            // Check if connection already exists
            const connectionId = `${auth.currentUser.uid}_${targetUserId}`;
            console.log("Connection ID:", connectionId);

            db.collection('user_connections')
                .doc(connectionId)
                .get()
                .then(doc => {
                    if (doc.exists) {
                        alert('Eine Verbindung zu diesem Trainer existiert bereits.');
                        addTrainerBtn.disabled = false;
                        addTrainerBtn.textContent = "Hinzufügen";
                        return;
                    }

                    // Create connection
                    const connection = {
                        initiatorId: auth.currentUser.uid,
                        initiatorName: auth.currentUser.displayName || auth.currentUser.email.split('@')[0],
                        initiatorEmail: auth.currentUser.email,
                        targetId: targetUserId,
                        targetName: targetUser.displayName || email.split('@')[0],
                        targetEmail: email,
                        targetRole: trainerType,
                        status: 'ACTIVE', // Changed from PENDING to ACTIVE for immediate use
                        trainerAccess: exerciseAccess,
                        mentalTrainerAccess: selfAssessmentAccess,
                        connectionDate: Date.now(),
                        lastUpdated: Date.now()
                    };

                    console.log("Creating connection:", connection);

                    db.collection('user_connections')
                        .doc(connectionId)
                        .set(connection)
                        .then(() => {
                            console.log("Connection created successfully");
                            alert('Trainer erfolgreich hinzugefügt.');

                            // Clear form
                            trainerEmailInput.value = '';

                            // Reload connections
                            loadUserConnections();

                            // Re-enable button
                            addTrainerBtn.disabled = false;
                            addTrainerBtn.textContent = "Hinzufügen";
                        })
                        .catch(error => {
                            console.error('Error adding connection:', error);
                            alert(`Fehler beim Hinzufügen des Trainers: ${error.message}`);
                            addTrainerBtn.disabled = false;
                            addTrainerBtn.textContent = "Hinzufügen";
                        });
                })
                .catch(error => {
                    console.error('Error checking existing connection:', error);
                    alert(`Fehler beim Überprüfen bestehender Verbindungen: ${error.message}`);
                    addTrainerBtn.disabled = false;
                    addTrainerBtn.textContent = "Hinzufügen";
                });
        })
        .catch(error => {
            console.error('Error finding user by email:', error);
            alert(`Fehler beim Suchen des Benutzers: ${error.message}`);
            addTrainerBtn.disabled = false;
            addTrainerBtn.textContent = "Hinzufügen";
        });
}

// Show edit connection dialog
function showEditConnectionDialog(connectionId, connection) {
    // Create dialog
    const dialog = document.createElement('div');
    dialog.className = 'modal';
    dialog.innerHTML = `
        <div class="modal-content">
            <h3>Berechtigungen bearbeiten</h3>
            <div class="form-group">
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="edit-exercise-access" ${connection.trainerAccess ? 'checked' : ''}>
                        Übungen und Trainingsplan
                    </label>
                    <label>
                        <input type="checkbox" id="edit-selfassessment-access" ${connection.mentalTrainerAccess ? 'checked' : ''}>
                        Selbsteinschätzung
                    </label>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary cancel-btn">Abbrechen</button>
                <button class="btn btn-primary save-btn">Speichern</button>
            </div>
        </div>
    `;

    // Add to DOM
    document.body.appendChild(dialog);

    // Add event listeners
    const cancelBtn = dialog.querySelector('.cancel-btn');
    const saveBtn = dialog.querySelector('.save-btn');

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(dialog);
    });

    saveBtn.addEventListener('click', () => {
        const exerciseAccess = dialog.querySelector('#edit-exercise-access').checked;
        const selfAssessmentAccess = dialog.querySelector('#edit-selfassessment-access').checked;

        if (!exerciseAccess && !selfAssessmentAccess) {
            alert('Bitte wähle mindestens eine Berechtigung aus.');
            return;
        }

        // Update connection
        db.collection('user_connections')
            .doc(connectionId)
            .update({
                trainerAccess: exerciseAccess,
                mentalTrainerAccess: selfAssessmentAccess,
                lastUpdated: Date.now()
            })
            .then(() => {
                alert('Berechtigungen erfolgreich aktualisiert.');

                // Reload connections
                loadUserConnections();

                // Close dialog
                document.body.removeChild(dialog);
            })
            .catch(error => {
                console.error('Error updating connection:', error);
                alert(`Fehler beim Aktualisieren der Berechtigungen: ${error.message}`);
            });
    });
}

// Show remove connection dialog
function showRemoveConnectionDialog(connectionId, connection) {
    if (confirm(`Möchtest du die Verbindung zu ${connection.targetName} wirklich entfernen?`)) {
        db.collection('user_connections')
            .doc(connectionId)
            .delete()
            .then(() => {
                alert('Verbindung erfolgreich entfernt.');

                // Reload connections
                loadUserConnections();
            })
            .catch(error => {
                console.error('Error removing connection:', error);
                alert(`Fehler beim Entfernen der Verbindung: ${error.message}`);
            });
    }
}

// Helper function to get status display info
function getStatusInfo(status) {
    switch (status) {
        case 'PENDING':
            return { text: 'Ausstehend', class: 'status-pending' };
        case 'ACTIVE':
            return { text: 'Aktiv', class: 'status-active' };
        case 'REJECTED':
            return { text: 'Abgelehnt', class: 'status-rejected' };
        case 'REVOKED':
            return { text: 'Widerrufen', class: 'status-revoked' };
        default:
            return { text: status, class: '' };
    }
}

// Helper function to get role display name
function getRoleDisplayName(role) {
    switch (role) {
        case 'PLAYER':
            return 'Spieler';
        case 'TRAINER':
            return 'Trainer';
        case 'MENTAL_TRAINER':
            return 'Mental Trainer';
        default:
            return role;
    }
}
