package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.atom.diesnookerapp.ui.trainingsplan.Trainingsplan
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for training plans
 */
class TrainingsplanRepository(private val context: Context) :
    FirebaseRepository<FirebaseTrainingsplan>("trainingsplan_history") {

    companion object {
        private const val TAG = "TrainingsplanRepository"
    }

    private val trainingsplanManager = TrainingsplanManager(context)
    private val authManager = FirebaseAuthManager()

    /**
     * Find all existing trainingsplan entries by year and month
     */
    suspend fun findExistingRecords(year: Int, month: Int): Result<List<FirebaseTrainingsplan>> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))

        return try {
            val snapshot = getCollection()
                .whereEqualTo("userId", userId)
                .get()
                .await()

            // Filter records by year and month from the weekStartDate field
            val records = snapshot.documents.mapNotNull { doc ->
                val record = doc.toObject(FirebaseTrainingsplan::class.java)?.apply { id = doc.id }

                // Parse the weekStartDate to check year and month
                if (record != null) {
                    val dateParts = record.weekStartDate.split("-")
                    if (dateParts.size >= 2) {
                        val recordYear = dateParts[0].toIntOrNull()
                        val recordMonth = dateParts[1].toIntOrNull()

                        if (recordYear == year && recordMonth == month) {
                            return@mapNotNull record
                        }
                    }
                }

                null
            }

            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save or update a trainingsplan in Firebase and remove duplicates
     */
    suspend fun saveOrUpdateRecord(plan: FirebaseTrainingsplan): Result<FirebaseTrainingsplan> {
        return try {
            // Parse the weekStartDate to get year and month
            val dateParts = plan.weekStartDate.split("-")
            if (dateParts.size < 2) {
                return Result.failure(Exception("Invalid weekStartDate format"))
            }

            val year = dateParts[0].toIntOrNull() ?: return Result.failure(Exception("Invalid year in weekStartDate"))
            val month = dateParts[1].toIntOrNull() ?: return Result.failure(Exception("Invalid month in weekStartDate"))

            // Find all existing plans with same year and month
            val existingRecordsResult = findExistingRecords(year, month)

            if (existingRecordsResult.isSuccess) {
                val existingRecords = existingRecordsResult.getOrThrow()

                if (existingRecords.isNotEmpty()) {
                    // Find the most recent record
                    val mostRecentRecord = existingRecords.maxByOrNull { it.lastUpdated }

                    if (mostRecentRecord != null) {
                        // Update the most recent record
                        plan.id = mostRecentRecord.id

                        // Delete all other duplicates
                        existingRecords
                            .filter { it.id != mostRecentRecord.id }
                            .forEach {
                                delete(it.id)
                                Log.d(TAG, "Deleted duplicate trainingsplan with weekStartDate: ${it.weekStartDate}")
                            }
                    }
                }

                // Save the record (will update if id is set, create new if not)
                save(plan)
            } else {
                // If finding existing records failed, just try to save
                save(plan)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Clean up duplicate records in Firestore
     */
    suspend fun cleanupDuplicates(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val result = getAll(FirebaseTrainingsplan::class.java)

            if (result.isSuccess) {
                val allRecords = result.getOrThrow()

                // Group records by year and month from weekStartDate
                val recordGroups = allRecords.groupBy { plan ->
                    val dateParts = plan.weekStartDate.split("-")
                    if (dateParts.size >= 2) {
                        "${dateParts[0]}-${dateParts[1]}"
                    } else {
                        // Fallback for invalid dates
                        plan.weekStartDate
                    }
                }

                var deletedCount = 0

                // For each group, keep only the most recent record and delete others
                recordGroups.forEach { (yearMonth, records) ->
                    if (records.size > 1) {
                        // Sort by lastUpdated (descending) and keep the first one
                        val sortedRecords = records.sortedByDescending { it.lastUpdated }
                        val recordToKeep = sortedRecords.first()

                        // Delete all other records
                        sortedRecords.drop(1).forEach {
                            delete(it.id)
                            deletedCount++
                            Log.d(TAG, "Cleaned up duplicate trainingsplan for $yearMonth")
                        }
                    }
                }

                return@withContext Result.success(deletedCount)
            } else {
                return@withContext Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            return@withContext Result.failure(e)
        }
    }

    /**
     * Sync local training plan history to Firebase
     */
    suspend fun syncToFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val localHistory = trainingsplanManager.getTrainingsplanHistory()
            var syncCount = 0

            // Sync current training plan if it exists
            val currentPlan = trainingsplanManager.getCurrentTrainingsplan()
            if (currentPlan != null) {
                val firebasePlan = FirebaseTrainingsplan.fromTrainingsplan(
                    currentPlan,
                    authManager.getCurrentUserId()!!
                )
                saveOrUpdateRecord(firebasePlan)
                syncCount++
            }

            // Sync history
            localHistory.forEach { plan ->
                val firebasePlan = FirebaseTrainingsplan.fromTrainingsplan(
                    plan,
                    authManager.getCurrentUserId()!!
                )
                saveOrUpdateRecord(firebasePlan)
                syncCount++
            }

            Result.success(syncCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync Firebase training plan history to local storage
     */
    suspend fun syncFromFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            // First, clean up any duplicates in Firestore
            cleanupDuplicates()

            val result = getAll(FirebaseTrainingsplan::class.java)

            if (result.isSuccess) {
                val firebasePlans = result.getOrThrow()
                val localHistory = trainingsplanManager.getTrainingsplanHistory().toMutableList()

                // Convert Firebase plans to local plans
                val newPlans = firebasePlans.map { it.toTrainingsplan() }

                // Merge with local history (replace existing, add new)
                newPlans.forEach { newPlan ->
                    // Compare by year and month to handle the transition from weekly to monthly plans
                    val existingIndex = localHistory.indexOfFirst {
                        it.weekStartDate.year == newPlan.weekStartDate.year &&
                        it.weekStartDate.month == newPlan.weekStartDate.month
                    }

                    if (existingIndex >= 0) {
                        localHistory[existingIndex] = newPlan
                    } else {
                        localHistory.add(newPlan)
                    }
                }

                // Save merged history
                val trainingsplanPreferences = com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanPreferences(context)
                trainingsplanPreferences.saveTrainingsplanHistory(localHistory)

                Result.success(newPlans.size)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save a training plan both locally and to Firebase
     */
    suspend fun saveTrainingsplan(plan: Trainingsplan, isCurrent: Boolean = false): Result<Trainingsplan> =
        withContext(Dispatchers.IO) {
            try {
                // Save locally
                if (isCurrent) {
                    trainingsplanManager.saveCurrentTrainingsplan(plan)
                } else {
                    val history = trainingsplanManager.getTrainingsplanHistory().toMutableList()
                    // Compare by year and month to handle the transition from weekly to monthly plans
                    val existingIndex = history.indexOfFirst {
                        it.weekStartDate.year == plan.weekStartDate.year &&
                        it.weekStartDate.month == plan.weekStartDate.month
                    }

                    if (existingIndex >= 0) {
                        history[existingIndex] = plan
                    } else {
                        history.add(plan)
                    }

                    val trainingsplanPreferences = com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanPreferences(context)
                    trainingsplanPreferences.saveTrainingsplanHistory(history)
                }

                // Save to Firebase if logged in
                if (authManager.isLoggedIn()) {
                    val firebasePlan = FirebaseTrainingsplan.fromTrainingsplan(
                        plan,
                        authManager.getCurrentUserId()!!
                    )
                    saveOrUpdateRecord(firebasePlan)
                }

                Result.success(plan)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
}
