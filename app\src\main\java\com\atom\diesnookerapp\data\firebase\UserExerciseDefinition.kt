package com.atom.diesnookerapp.data.firebase

data class UserExerciseDefinition(
    val id: String = "",
    val userId: String = "", // Added default for flexible deserialization
    val name: String = "",
    val category: String = "",
    val description: String? = null,
    val exerciseType: String = "normal", // normal, timeonly, splits, stellungsspiel
    val isCustom: Boolean = true, // Added back with default for flexible deserialization
    val isDeletable: Boolean = true // Added back with default for flexible deserialization
)
