package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.PopupMenu
import androidx.fragment.app.Fragment
import com.atom.diesnookerapp.R
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.utils.MPPointF
import com.google.android.material.button.MaterialButton
import org.threeten.bp.format.DateTimeFormatter
import kotlin.math.abs
import android.app.AlertDialog
import android.widget.ListView
import android.widget.ArrayAdapter
import android.widget.CheckBox
// Add this import with the other imports at the top of the file
import android.view.Gravity
import android.widget.LinearLayout
import org.threeten.bp.LocalDate
import com.github.mikephil.charting.components.AxisBase
import android.content.Context
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.highlight.Highlight
import android.graphics.Paint
import android.util.Log
import android.widget.DatePicker
import android.app.DatePickerDialog
import java.util.Calendar
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RoundRectShape
import com.atom.diesnookerapp.ui.settings.ThemePreferences
import com.atom.diesnookerapp.ui.settings.ThemeHelper

private lateinit var yourDateList: List<LocalDate>



class GraphDetailFragment : Fragment() {
    private var selectedType: TrainingType? = null
    private lateinit var trainingPreferences: TrainingPreferences
    private lateinit var lineChart: LineChart
    private var legendDialog: AlertDialog? = null  // Replace PopupMenu with AlertDialog
    private val datasetVisibilityMap = mutableMapOf<String, Boolean>()
    // Store the original dataset order
    private var datasetOrder: List<String> = listOf()
    private var selectedTimeframe: Timeframe = Timeframe.ALL_TIME // Default timeframe
    private var startDate: LocalDate = LocalDate.now().minusMonths(1)
    private var endDate: LocalDate = LocalDate.now()




    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString("type")?.let {
            selectedType = TrainingType.valueOf(it)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_graph_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())

        val titleText = view.findViewById<TextView>(R.id.titleText)
        lineChart = view.findViewById(R.id.lineChart)
        val legendButton = view.findViewById<MaterialButton>(R.id.legendButton)
        val timeframeButton = view.findViewById<MaterialButton>(R.id.timeframeButton)

        legendButton.setOnClickListener {
            showLegendDialog()
        }

        // Set up the timeframe button click listener
        timeframeButton.setOnClickListener {
            showTimeframeMenu(it)
        }

        titleText.text = when (selectedType) {
            TrainingType.BEFORE_TRAINING -> "Vor Training"
            TrainingType.AFTER_TRAINING -> "Nach Training"
            TrainingType.BEFORE_TOURNAMENT -> "Vor Turnier"
            TrainingType.AFTER_TOURNAMENT -> "Nach Turnier"
            null -> "Graph"
        }

        updateTimeframeButtonText() // Set initial button text
        setupChart()
        // Call filterDataByTimeframe to initialize the chart with the default timeframe
        filterDataByTimeframe(selectedTimeframe)
    }

    private fun setupChart() {
        lineChart.apply {
            description.isEnabled = false
            legend.isEnabled = false  // Disable default legend since we're using our own
            setDrawGridBackground(false)

            // Enable scaling and scrolling
            setScaleEnabled(true)
            setPinchZoom(true)
            isDoubleTapToZoomEnabled = true
            isDragEnabled = true  // Enable horizontal scrolling

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                granularity = 1f
                setDrawGridLines(false)
                labelRotationAngle = -45f  // Rotate labels upwards for better readability

                // Set the custom date formatter
                val dateFormatter = DateTimeFormatter.ofPattern("dd. MMM yyyy") // Adjust the pattern as needed
                valueFormatter = DateValueFormatter(dateFormatter)
            }

            axisLeft.apply {
                axisMinimum = 0f
                axisMaximum = 10f
                granularity = 1f
            }

            axisRight.isEnabled = false

            // Set minimum visible entries
            setVisibleXRangeMinimum(5f)  // Show at least 5 entries
            setVisibleXRangeMaximum(10f) // Show maximum 10 entries at once
        }
    }

    private fun updateChartData(filteredRecords: List<DailyTrainingRecord>) {
        Log.d("GraphDetailFragment", "Filtered Records: $filteredRecords")

        // Check if there are any filtered records
        if (filteredRecords.isEmpty()) {
            lineChart.setNoDataText("Keine Daten verfügbar")
            lineChart.clear() // Clear the chart if no data is available
            lineChart.invalidate() // Refresh the chart
            return
        }

        // Use filteredRecords instead of getting records again
        val allItems = filteredRecords.flatMap { it.items }.distinctBy { it.id }

        // Populate yourDateList based on the filtered records
        yourDateList = filteredRecords.map { it.date }

        // Create datasets only for visible items
        val dataSets = allItems.mapIndexedNotNull { index, item ->
            val entries = mutableListOf<Entry>()
            filteredRecords.forEachIndexed { recordIndex, record ->
                record.items.find { it.id == item.id }?.let { foundItem ->
                    if (foundItem.score != null) {
                        // Pass the item title and color as data to the Entry
                        entries.add(
                            Entry(
                                recordIndex.toFloat(),
                                foundItem.score!!.toFloat(),
                                Pair(item.title, getColorForIndex(index)) // Store title and color
                            )
                        )
                    }
                }
            }

            // Only create a dataset if there are entries and the dataset is visible
            if (entries.isNotEmpty() && (datasetVisibilityMap[item.title] ?: true)) {
                val label = item.title // Use the title for the label
                LineDataSet(entries, label).apply {
                    color = getColorForIndex(index)
                    setDrawCircles(true)
                    circleRadius = 4f
                    circleColors = listOf(getColorForIndex(index))
                    lineWidth = 2f
                    mode = LineDataSet.Mode.LINEAR
                    setDrawValues(false)
                    isVisible = datasetVisibilityMap[label] ?: true // Restore visibility state
                }
            } else {
                null // Skip creating a dataset if not visible or no entries
            }
        }

        // Ensure dataset order is only set once
        if (datasetOrder.isEmpty()) {
            datasetOrder = dataSets.map { it.label }
        }

        // Sort datasets according to initial order instead of dynamic sorting
        val sortedDataSets = datasetOrder.mapNotNull { label ->
            dataSets.find { it.label == label }
        }

        lineChart.data = LineData(sortedDataSets)
        lineChart.invalidate()

        // Set the custom marker view
        val markerView = CustomMarkerView(requireContext(), R.layout.marker_view, lineChart)
        lineChart.marker = markerView
    }


    private fun showLegendDialog() {
        val dataSets = datasetOrder.mapNotNull { label ->
            lineChart.data?.dataSets?.find { it.label == label } as? LineDataSet
        }
        if (dataSets.isEmpty()) return

        val items = datasetOrder.toTypedArray() // Keep labels in the same order
        val checkedItems = items.map { datasetVisibilityMap[it] ?: true }.toBooleanArray()

        legendDialog?.dismiss()

        // Inflate the custom dialog layout
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_legend, null)
        val checkboxContainer = dialogView.findViewById<LinearLayout>(R.id.checkboxContainer)

        // Clear previous checkboxes
        checkboxContainer.removeAllViews()

        // Dynamically add checkboxes
        items.forEachIndexed { index, item ->
            val checkBox = LayoutInflater.from(requireContext()).inflate(R.layout.item_legend, checkboxContainer, false)
            val checkBoxView = checkBox.findViewById<CheckBox>(R.id.checkBox)
            val colorIndicator = checkBox.findViewById<View>(R.id.colorIndicator)
            val scoreName = checkBox.findViewById<TextView>(R.id.scoreName) // Ensure this line is present
            //val legendCount = checkBox.findViewById<TextView>(R.id.legendCount) wieder reinmachen für count

            checkBoxView.isChecked = checkedItems[index]
            colorIndicator.setBackgroundColor(getColorForIndex(datasetOrder.indexOf(item)))
            scoreName.text = item // Set the score name
            //legendCount.text = "(${dataSets[index].entryCount})" // Display the count | wieder reinmachen für count

            checkBoxView.setOnCheckedChangeListener { _, isChecked ->
                datasetVisibilityMap[item] = isChecked
                dataSets[index].isVisible = isChecked
                filterDataByTimeframe(selectedTimeframe)
            }

            checkboxContainer.addView(checkBox)
        }

        // Determine which dialog style to use based on the current theme
        val dialogStyle = getDialogStyleForCurrentTheme()

        legendDialog = AlertDialog.Builder(requireContext(), dialogStyle)
            .setView(dialogView)
            .setPositiveButton("Schließen", null)
            .create()

        legendDialog?.show()
    }

    private fun getDialogStyleForCurrentTheme(): Int {
        // Get the current theme from ThemePreferences
        val themePreferences = ThemePreferences(requireContext())
        val currentTheme = themePreferences.getThemeMode()

        return when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> R.style.DropdownDialog_Snooker
            ThemePreferences.THEME_BLUE -> R.style.DropdownDialog_Blue
            ThemePreferences.THEME_DARK_BLUE -> R.style.DropdownDialog_DarkBlue
            ThemePreferences.THEME_DARK -> R.style.DropdownDialog_Dark
            else -> R.style.DropdownDialog
        }
    }

    private fun showTimeframeMenu(view: View) {
        // Use ThemeHelper to create a themed PopupMenu
        val popupMenu = ThemeHelper.createThemedPopupMenu(requireContext(), view)
        popupMenu.menuInflater.inflate(R.menu.menu_timeframe, popupMenu.menu)  // Using the full timeframe menu
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_last_week -> setTimeframe(Timeframe.LAST_WEEK)
                R.id.action_last_month -> setTimeframe(Timeframe.LAST_MONTH)
                R.id.action_last_quarter -> setTimeframe(Timeframe.LAST_QUARTER)
                R.id.action_last_year -> setTimeframe(Timeframe.LAST_YEAR)
                R.id.action_all_time -> setTimeframe(Timeframe.ALL_TIME)
                R.id.action_custom -> {
                    selectedTimeframe = Timeframe.CUSTOM
                    showCustomTimeframeDialog()
                }
            }
            true
        }
        popupMenu.show()
    }

    private fun setTimeframe(timeframe: Timeframe) {
        selectedTimeframe = timeframe
        when (timeframe) {
            Timeframe.LAST_WEEK -> setTimeframeDates(LocalDate.now().minusWeeks(1), LocalDate.now())
            Timeframe.LAST_MONTH -> setTimeframeDates(LocalDate.now().minusMonths(1), LocalDate.now())
            Timeframe.LAST_QUARTER -> setTimeframeDates(LocalDate.now().minusMonths(3), LocalDate.now())
            Timeframe.LAST_YEAR -> setTimeframeDates(LocalDate.now().minusYears(1), LocalDate.now())
            Timeframe.ALL_TIME -> {
                val firstDate = trainingPreferences.getRecords()
                    .minOfOrNull { it.date }
                    ?: LocalDate.now().minusYears(1)
                setTimeframeDates(firstDate, LocalDate.now())
            }
            Timeframe.CUSTOM -> { } // Handled separately
        }
        updateTimeframeButtonText()
    }

    private fun setTimeframeDates(start: LocalDate, end: LocalDate) {
        startDate = start
        endDate = end
        filterDataByTimeframe(selectedTimeframe)
    }

    private fun updateTimeframeButtonText() {
        view?.findViewById<MaterialButton>(R.id.timeframeButton)?.text = when (selectedTimeframe) {
            Timeframe.LAST_WEEK -> "Letzte Woche"
            Timeframe.LAST_MONTH -> "Letzter Monat"
            Timeframe.LAST_QUARTER -> "Letztes Quartal"
            Timeframe.LAST_YEAR -> "Letztes Jahr"
            Timeframe.ALL_TIME -> "Gesamter Zeitraum"
            Timeframe.CUSTOM -> {
                val formatter = DateTimeFormatter.ofPattern("dd.MM.yy")
                "${startDate.format(formatter)} - ${endDate.format(formatter)}"
            }
        }
    }

    private fun showCustomTimeframeDialog() {
        val view = layoutInflater.inflate(R.layout.dialog_custom_timeframe, null)
        val startDateButton = view.findViewById<MaterialButton>(R.id.startDateButton)
        val endDateButton = view.findViewById<MaterialButton>(R.id.endDateButton)

        var selectedStartDate = startDate
        var selectedEndDate = endDate

        fun updateDateButtons() {
            val formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
            startDateButton.text = selectedStartDate.format(formatter)
            endDateButton.text = selectedEndDate.format(formatter)
        }

        updateDateButtons()

        startDateButton.setOnClickListener {
            showDatePicker(selectedStartDate) { date ->
                selectedStartDate = date
                updateDateButtons()
            }
        }

        endDateButton.setOnClickListener {
            showDatePicker(selectedEndDate) { date ->
                selectedEndDate = date
                updateDateButtons()
            }
        }

        // Use ThemeHelper to create a themed AlertDialog with default animations
        ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setTitle("Zeitraum auswählen")
            .setView(view)
            .setPositiveButton("OK") { _, _ ->
                setTimeframeDates(selectedStartDate, selectedEndDate)
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun showDatePicker(initialDate: LocalDate, onDateSelected: (LocalDate) -> Unit) {
        val calendar = Calendar.getInstance()
        calendar.set(initialDate.year, initialDate.monthValue - 1, initialDate.dayOfMonth)

        // Use ThemeHelper to create a themed DatePickerDialog
        ThemeHelper.createThemedDatePickerDialog(
            requireContext(),
            { _, year, month, dayOfMonth ->
                onDateSelected(LocalDate.of(year, month + 1, dayOfMonth))
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun filterDataByTimeframe(timeframe: Timeframe) {
        val allRecords = trainingPreferences.getRecords()
        val records = allRecords.filter { record ->
            record.type == selectedType &&
            !record.date.isBefore(startDate) &&
            !record.date.isAfter(endDate)
        }
        updateChartData(records)
    }

    // Define an enum for timeframes
    enum class Timeframe {
        LAST_WEEK,
        LAST_MONTH,
        LAST_QUARTER,
        LAST_YEAR,
        ALL_TIME,
        CUSTOM
    }

    private fun getColorForIndex(index: Int): Int {
        val colors = listOf(
            Color.rgb(244, 67, 54),   // Red
            Color.rgb(33, 150, 243),  // Blue
            Color.rgb(76, 175, 80),   // Green
            Color.rgb(255, 152, 0),   // Orange
            Color.rgb(156, 39, 176),  // Purple
            Color.rgb(0, 188, 212),   // Cyan
            Color.rgb(255, 235, 59),  // Yellow
            Color.rgb(233, 30, 99),   // Pink
            Color.rgb(0, 150, 136),   // Teal
            Color.rgb(158, 158, 158), // Grey
            Color.rgb(121, 85, 72),   // Brown
            Color.rgb(63, 81, 181),   // Indigo
            Color.rgb(139, 195, 74),  // Light Green
            Color.rgb(255, 87, 34),   // Deep Orange
            Color.rgb(103, 58, 183),  // Deep Purple
            Color.rgb(3, 169, 244),   // Light Blue
            Color.rgb(205, 220, 57),  // Lime
            Color.rgb(233, 30, 99),   // Pink
            Color.rgb(0, 137, 123),   // Dark Teal
            Color.rgb(96, 125, 139),  // Blue Grey
            Color.rgb(183, 28, 28),   // Dark Red
            Color.rgb(13, 71, 161),   // Dark Blue
            Color.rgb(27, 94, 32),    // Dark Green
            Color.rgb(230, 81, 0),    // Dark Orange
            Color.rgb(74, 20, 140),   // Dark Purple
            Color.rgb(0, 96, 100),    // Dark Cyan
            Color.rgb(245, 127, 23),  // Amber
            Color.rgb(173, 20, 87)    // Dark Pink
        )
        return colors[index % colors.size]
    }

    // Helper function to convert dp to pixels
    private fun Int.dpToPx(): Int {
        return (this * resources.displayMetrics.density).toInt()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        legendDialog?.dismiss()
        legendDialog = null
    }
}


class DateValueFormatter(private val dateFormatter: DateTimeFormatter) : ValueFormatter() {
    override fun getAxisLabel(value: Float, axis: AxisBase?): String {
        // Convert the float value to an integer index
        val index = value.toInt()
        // Assuming you have a list of dates corresponding to your entries
        return if (index >= 0 && index < yourDateList.size) {
            dateFormatter.format(yourDateList[index]) // Format the date
        } else {
            ""
        }
    }
}

// Custom MarkerView for displaying data points
class CustomMarkerView(
        context: Context,
        layoutResource: Int,
        private val lineChart: LineChart
    ) : MarkerView(context, layoutResource) {
        private val tvContent: TextView = findViewById(R.id.tvContent)
        private var currentEntry: Entry? = null // Add this variable

        override fun refreshContent(e: Entry?, highlight: Highlight?) {
            currentEntry = e // Store the current entry
            if (e == null) return

            val data = e.data
            if (data is Pair<*, *>) {
                val label = data.first as? String
                val color = data.second as? Int

                // Set the text content
                tvContent.text = "${e.y} ($label)"

                // Set the background color
                if (color != null) {
                    val cornerRadius = 4f.dpToPx()
                    val outerRadii = floatArrayOf(
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius
                    )
                    val shape = RoundRectShape(outerRadii, null, null)
                    val background = ShapeDrawable(shape)
                    background.paint.color = color
                    tvContent.background = background
                } else {
                    Log.w("CustomMarkerView", "Failed to get color for label background")
                    // Set a default background color if no color is found
                    val cornerRadius = 4f.dpToPx()
                    val outerRadii = floatArrayOf(
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius,
                        cornerRadius
                    )
                    val shape = RoundRectShape(outerRadii, null, null)
                    val background = ShapeDrawable(shape)
                    background.paint.color = Color.GRAY // Default color
                    tvContent.background = background
                }
            } else {
                Log.w("CustomMarkerView", "Invalid data type in Entry. Expected Pair<String, Int>")
            }

            super.refreshContent(e, highlight)
        }

        override fun getOffset(): MPPointF {
            // Get the current dataset for the marker view
            val currentDataSet = lineChart.data?.dataSets?.find { it.isVisible && it.label == (currentEntry?.data as? Pair<*, *>)?.first }

            // Check if the current entry is the last entry in the dataset
            val isLastEntry = currentDataSet?.entryCount?.let { currentEntry?.x?.toInt() == it - 1 } ?: false
            // Check if the current entry is the first entry in the dataset
            val isFirstEntry = currentDataSet?.entryCount?.let { currentEntry?.x?.toInt() == 0 } ?: false


            // Measure the width of the label text
            val labelText = "${currentEntry?.y} (${(currentEntry?.data as? Pair<*, *>)?.first ?: ""})"
            val paint = Paint().apply {
                textSize = tvContent.textSize
                typeface = tvContent.typeface// Use the same text size and typeface as the TextView
            }
            val labelWidth = paint.measureText(labelText)

            // Adjust the offset based on the label width
            val xOffset = when {
                isLastEntry -> -((width / 2f) + (labelWidth / 2f)) // Left of last entry
                isFirstEntry -> -(width / 2f) + (labelWidth / 2f)// Right of first entry
                else -> -(width / 2f) // Center for other entries
            }

            return MPPointF(xOffset, -height.toFloat())
        }

        // Helper function to convert dp to pixels
        private fun Float.dpToPx(): Float {
            return (this * resources.displayMetrics.density)
        }

        // Helper function to convert dp to pixels
        private fun Int.dpToPx(): Float {
            return (this * resources.displayMetrics.density)
        }

}