package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView

class AssessmentAdapter(
    private val items: List<AssessmentItem>,
    private val onItemClick: (AssessmentItem) -> Unit
) : RecyclerView.Adapter<AssessmentAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardView)
        val titleText: TextView = view.findViewById(R.id.titleText)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_assessment, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.titleText.text = item.title
        holder.cardView.setOnClickListener { onItemClick(item) }
    }

    override fun getItemCount() = items.size
}

data class AssessmentItem(
    val id: String,
    val title: String
) 