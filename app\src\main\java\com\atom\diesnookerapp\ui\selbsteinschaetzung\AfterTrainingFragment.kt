package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.button.MaterialButton
import org.threeten.bp.LocalDate
import org.threeten.bp.ZoneId

class AfterTrainingFragment : Fragment() {
    private var recyclerView: RecyclerView? = null
    private lateinit var trainingPreferences: TrainingPreferences
    private val assessmentItems = listOf(
        TrainingAssessmentItem(1, "Konzentration"),
        TrainingAssessmentItem(2, "Kondition"),
        TrainingAssessmentItem(3, "Gleichgewicht"),
        TrainingAssessmentItem(4, "Augen"),
        TrainingAssessmentItem(5, "Flüssigkeit"),
        TrainingAssessmentItem(6, "Freude"),
        TrainingAssessmentItem(7, "Motivation"),
        TrainingAssessmentItem(8, "Aktivierung"),
        TrainingAssessmentItem(9, "Entspannung"),
        TrainingAssessmentItem(10, "Stressbewältigung"),
        TrainingAssessmentItem(11, "Stoß"),
        TrainingAssessmentItem(12, "Fokus auf Abstoß"),
        TrainingAssessmentItem(13, "technisch aufwendige Bälle"),
        TrainingAssessmentItem(14, "roll Bälle"),
        TrainingAssessmentItem(15, "Effet Bälle"),
        TrainingAssessmentItem(16, "Bandenbälle"),
        TrainingAssessmentItem(17, "Safetys"),
        TrainingAssessmentItem(18, "splits"),
        TrainingAssessmentItem(19, "Positionsspiel"),
        TrainingAssessmentItem(20, "Potting"),
        TrainingAssessmentItem(21, "Rest"),
        TrainingAssessmentItem(22, "Material"),
        TrainingAssessmentItem(23, "Umfeld")
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_after_training, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())

        recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        val adapter = TrainingAssessmentAdapter(assessmentItems) { item ->
            println("Item ${item.id} score changed to ${item.score}")
        }

        recyclerView?.adapter = adapter

        view.findViewById<MaterialButton>(R.id.saveButton).setOnClickListener {
            saveAssessment()
        }
    }

    private fun saveAssessment() {
        val record = DailyTrainingRecord(
            date = LocalDate.now(ZoneId.systemDefault()),
            type = TrainingType.AFTER_TRAINING,
            items = assessmentItems
        )
        trainingPreferences.saveDaily(record)
        Toast.makeText(requireContext(), "Bewertungen gespeichert", Toast.LENGTH_SHORT).show()
    }
} 