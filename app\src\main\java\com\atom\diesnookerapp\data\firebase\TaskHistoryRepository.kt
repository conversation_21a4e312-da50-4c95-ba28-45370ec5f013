package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.atom.diesnookerapp.ui.aufgaben.TaskHistoryEntry
import com.atom.diesnookerapp.ui.aufgaben.TaskManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for task history
 */
class TaskHistoryRepository(private val context: Context) :
    FirebaseRepository<FirebaseTaskHistoryEntry>("task_history") {

    companion object {
        private const val TAG = "TaskHistoryRepository"
    }

    private val taskManager = TaskManager(context)
    private val authManager = FirebaseAuthManager()

    /**
     * Find all existing task history entries by weekKey
     */
    suspend fun findExistingRecords(weekKey: String): Result<List<FirebaseTaskHistoryEntry>> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))

        return try {
            val snapshot = getCollection()
                .whereEqualTo("userId", userId)
                .whereEqualTo("weekKey", weekKey)
                .get()
                .await()

            val records = snapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseTaskHistoryEntry::class.java)?.apply { id = doc.id }
            }

            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save or update a task history entry in Firebase and remove duplicates
     */
    suspend fun saveOrUpdateRecord(entry: FirebaseTaskHistoryEntry): Result<FirebaseTaskHistoryEntry> {
        return try {
            // Find all existing entries with same weekKey
            val existingRecordsResult = findExistingRecords(entry.weekKey)

            if (existingRecordsResult.isSuccess) {
                val existingRecords = existingRecordsResult.getOrThrow()

                if (existingRecords.isNotEmpty()) {
                    // Find the most recent record
                    val mostRecentRecord = existingRecords.maxByOrNull { it.lastUpdated }

                    if (mostRecentRecord != null) {
                        // Update the most recent record
                        entry.id = mostRecentRecord.id

                        // Delete all other duplicates
                        existingRecords
                            .filter { it.id != mostRecentRecord.id }
                            .forEach {
                                delete(it.id)
                                Log.d(TAG, "Deleted duplicate task history entry with weekKey: ${it.weekKey}")
                            }
                    }
                }

                // Save the record (will update if id is set, create new if not)
                save(entry)
            } else {
                // If finding existing records failed, just try to save
                save(entry)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Clean up duplicate records in Firestore
     */
    suspend fun cleanupDuplicates(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val result = getAll(FirebaseTaskHistoryEntry::class.java)

            if (result.isSuccess) {
                val allRecords = result.getOrThrow()

                // Group records by weekKey
                val recordGroups = allRecords.groupBy { it.weekKey }
                var deletedCount = 0

                // For each group, keep only the most recent record and delete others
                recordGroups.forEach { (weekKey, records) ->
                    if (records.size > 1) {
                        // Sort by lastUpdated (descending) and keep the first one
                        val sortedRecords = records.sortedByDescending { it.lastUpdated }
                        val recordToKeep = sortedRecords.first()

                        // Delete all other records
                        sortedRecords.drop(1).forEach {
                            delete(it.id)
                            deletedCount++
                            Log.d(TAG, "Cleaned up duplicate task history entry with weekKey: $weekKey")
                        }
                    }
                }

                return@withContext Result.success(deletedCount)
            } else {
                return@withContext Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            return@withContext Result.failure(e)
        }
    }

    /**
     * Sync local task history to Firebase
     */
    suspend fun syncToFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val localHistory = taskManager.getTaskHistory()
            var syncCount = 0

            localHistory.forEach { entry ->
                val firebaseEntry = FirebaseTaskHistoryEntry.fromTaskHistoryEntry(
                    entry,
                    authManager.getCurrentUserId()!!
                )
                saveOrUpdateRecord(firebaseEntry)
                syncCount++
            }

            Result.success(syncCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync Firebase task history to local storage
     */
    suspend fun syncFromFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            // First, clean up any duplicates in Firestore
            cleanupDuplicates()

            val result = getAll(FirebaseTaskHistoryEntry::class.java)

            if (result.isSuccess) {
                val firebaseEntries = result.getOrThrow()
                val localHistory = taskManager.getTaskHistory().toMutableList()

                // Convert Firebase entries to local entries
                val newEntries = firebaseEntries.map { it.toTaskHistoryEntry() }

                // Merge with local history (replace existing, add new)
                newEntries.forEach { newEntry ->
                    val existingIndex = localHistory.indexOfFirst {
                        it.weekKey == newEntry.weekKey
                    }

                    if (existingIndex >= 0) {
                        localHistory[existingIndex] = newEntry
                    } else {
                        localHistory.add(newEntry)
                    }
                }

                // Save merged history
                saveTaskHistoryToSharedPreferences(localHistory)

                Result.success(newEntries.size)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save a task history entry both locally and to Firebase
     */
    suspend fun saveTaskHistoryEntry(entry: TaskHistoryEntry): Result<TaskHistoryEntry> =
        withContext(Dispatchers.IO) {
            try {
                // Get current history and update/add the entry
                val history = taskManager.getTaskHistory().toMutableList()
                val existingIndex = history.indexOfFirst { it.weekKey == entry.weekKey }

                if (existingIndex >= 0) {
                    history[existingIndex] = entry
                } else {
                    history.add(entry)
                }

                // Save locally
                saveTaskHistoryToSharedPreferences(history)

                // Save to Firebase if logged in
                if (authManager.isLoggedIn()) {
                    val firebaseEntry = FirebaseTaskHistoryEntry.fromTaskHistoryEntry(
                        entry,
                        authManager.getCurrentUserId()!!
                    )
                    saveOrUpdateRecord(firebaseEntry)
                }

                Result.success(entry)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    /**
     * Save task history to SharedPreferences
     * This is a helper method since TaskManager doesn't have a direct saveTaskHistory method
     */
    private fun saveTaskHistoryToSharedPreferences(history: List<TaskHistoryEntry>) {
        val sharedPreferences = context.getSharedPreferences("tasks_prefs", Context.MODE_PRIVATE)
        val gson = com.google.gson.Gson()
        sharedPreferences.edit().putString("task_history", gson.toJson(history)).apply()
    }
}
