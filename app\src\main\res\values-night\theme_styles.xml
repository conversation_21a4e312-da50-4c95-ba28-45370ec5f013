<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Ocean Theme Styles for night mode -->
    <style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style>

    <style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style>

    <style name="OceanPositiveButtonStyle.Night">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style>

    <style name="OceanNegativeButtonStyle.Night">
        <item name="android:textColor">@color/ocean_light</item>
    </style>

    <!-- Crimson Theme Styles for night mode -->
    <style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style>

    <style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style>

    <style name="CrimsonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style>

    <style name="CrimsonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/crimson_light</item>
    </style>

    <!-- Neon Theme Styles for night mode -->
    <style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style>

    <style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style>

    <style name="NeonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/neon_purple_2</item>
    </style>

    <style name="NeonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/neon_pink_1</item>
    </style>
</resources>
