package com.atom.diesnookerapp.ui.trainingsplan

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import androidx.cardview.widget.CardView

class ExerciseCategoryAdapter(
    private val categories: List<ExerciseCategoryItem>,
    private val onItemClick: (String) -> Unit
) : RecyclerView.Adapter<ExerciseCategoryAdapter.CategoryViewHolder>() {

    class CategoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val nameTextView: TextView = itemView.findViewById(R.id.categoryNameTextView)
        val cardView: CardView = itemView.findViewById(R.id.cardView)
    }

    override fun onCreateViewHolder(parent: <PERSON>Group, viewType: Int): CategoryViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_exercise_category, parent, false)
        return CategoryViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        val category = categories[position]
        holder.nameTextView.text = category.name
        holder.cardView.setOnClickListener { onItemClick(category.name) }
    }

    override fun getItemCount() = categories.size

}
