package com.atom.diesnookerapp.ui.ergebniserfassung

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.atom.diesnookerapp.R
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import com.google.android.material.button.MaterialButton
import android.widget.DatePicker
import android.app.DatePickerDialog
import android.icu.util.Calendar
import android.widget.PopupMenu
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.atom.diesnookerapp.ui.settings.ThemePreferences
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet

class ExerciseGraphFragment : Fragment() {
    private var category: String? = null
    private var exerciseId: String? = null
    private lateinit var exercisePreferences: ExercisePreferences
    private lateinit var lineChart: LineChart
    private var startDate: LocalDate = LocalDate.now().minusMonths(1)
    private var endDate: LocalDate = LocalDate.now()
    private var selectedTimeframe: Timeframe = Timeframe.LAST_MONTH

    // Merged color map including specific spelt colors
    private val exerciseColorMap: Map<String, Int> = mapOf(
        "Gelb" to Color.YELLOW,
        "Grün" to Color.GREEN,
        "Braun" to Color.rgb(165, 42, 42), // Brown
        "Blau" to Color.BLUE,
        "Schwarz" to Color.BLACK,
        //"break_50" to Color.rgb(255, 165, 0), // Orange
        //"frames" to Color.MAGENTA,
        //"black_routines" to Color.CYAN,
        //"long_shots" to Color.DKGRAY,
        //"line_up" to Color.LTGRAY,
        "Gelb - 3" to getSpeltColor(Color.YELLOW, "3"),
        "Gelb - 6" to getSpeltColor(Color.YELLOW, "6"),
        "Gelb - 10" to getSpeltColor(Color.YELLOW, "10"),
        "Grün - 3" to getSpeltColor(Color.GREEN, "3"),
        "Grün - 6" to getSpeltColor(Color.GREEN, "6"),
        "Grün - 10" to getSpeltColor(Color.GREEN, "10"),
        "Braun - 3" to getSpeltColor(Color.rgb(165, 42, 42), "3"),
        "Braun - 6" to getSpeltColor(Color.rgb(165, 42, 42), "6"),
        "Braun - 10" to getSpeltColor(Color.rgb(165, 42, 42), "10"),
        "Blau - 3" to getSpeltColor(Color.BLUE, "3"),
        "Blau - 6" to getSpeltColor(Color.BLUE, "6"),
        "Blau - 10" to getSpeltColor(Color.BLUE, "10"),
        "Schwarz - 3" to getSpeltColor(Color.rgb(75, 75, 75), "0"),
        "Schwarz - 6" to getSpeltColor(Color.rgb(53, 53, 53), "0"),
        "Schwarz - 10" to getSpeltColor(Color.BLACK, "0"),
        // Add colors for Stellungsspiel exercises
        "stellungsspiel_gelb" to Color.rgb(255, 215, 0),  // Gold
        "stellungsspiel_gruen" to Color.rgb(0, 128, 0),   // Dark Green
        "stellungsspiel_braun" to Color.rgb(139, 69, 19),  // Saddle Brown
        //"pj_routines" to Color.rgb(147, 112, 219),        // Medium Purple
        //"random_bilder" to Color.rgb(255, 105, 180)       // Hot Pink
    )
    // Add enum for timeframes
    private enum class Timeframe {
        LAST_WEEK,
        LAST_MONTH,
        LAST_QUARTER,
        LAST_YEAR,
        ALL_TIME,
        CUSTOM
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            category = it.getString("category")
            exerciseId = it.getString("exerciseId")
        }
        exercisePreferences = ExercisePreferences(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exercise_graph, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (category != null) {
            view.findViewById<TextView>(R.id.categoryText).text = category
        } else {
            view.findViewById<TextView>(R.id.categoryText).text = getExerciseTitle(exerciseId)
        }

        // Setup timeframe button with popup menu
        val timeframeButton = view.findViewById<MaterialButton>(R.id.timeframeButton)
        timeframeButton.setOnClickListener { button ->
            showTimeframePopupMenu(button)
        }
        updateTimeframeButtonText()

        // Initialize chart
        lineChart = LineChart(requireContext())
        view.findViewById<ViewGroup>(R.id.graphContainer).addView(lineChart)

        setupChart()
        loadData()
    }

    private fun setupChart() {
        lineChart.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = DateAxisFormatter()
                granularity = 1f
                setDrawGridLines(false)
                labelCount = 5 // Limit the number of labels to avoid overcrowding
            }

            axisLeft.apply {
                setDrawGridLines(true)
                setDrawAxisLine(true)
                axisMinimum = 0f // Start from 0

                // Check if we're displaying a Stellungsspiel exercise
                if (exerciseId?.startsWith("stellungsspiel_") == true) {
                    // For Stellungsspiel, set maximum to 100% and add % to labels
                    axisMaximum = 100f
                    valueFormatter = object : ValueFormatter() {
                        override fun getFormattedValue(value: Float): String {
                            return "${value.toInt()}%"
                        }
                    }
                }
            }

            axisRight.isEnabled = false
            legend.apply {
                verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
                horizontalAlignment = Legend.LegendHorizontalAlignment.LEFT
                orientation = Legend.LegendOrientation.HORIZONTAL
                isWordWrapEnabled = true
            }

            setNoDataText("Keine Daten verfügbar")
        }
    }

    private fun loadData() {
        val records = exercisePreferences.getRecords()
            .filter { record ->
                val date = record.timestamp.toLocalDate()
                val isWithinDateRange = !date.isBefore(startDate) && !date.isAfter(endDate)
                if (exerciseId != null) {
                    record.exerciseId == exerciseId && isWithinDateRange
                } else {
                    category?.let { cat ->
                        getExerciseCategory(record.exerciseId) == cat && isWithinDateRange
                    } ?: false
                }
            }
            .groupBy { it.exerciseId }

        if (records.isEmpty()) {
            lineChart.setNoDataText("Keine Daten verfügbar")
            lineChart.invalidate()
            return
        }

        val allDates = records.values
            .flatMap { exerciseRecords ->
                exerciseRecords.map { it.timestamp.toLocalDate() }
            }
            .distinct()
            .sorted()
            .toList()

        (lineChart.xAxis.valueFormatter as? DateAxisFormatter)?.setDates(allDates)

        val dataSets = mutableListOf<LineDataSet>()
        records.forEach { (exerciseId, exerciseRecords) ->
            // Check if this is a Stellungsspiel exercise
            val isStellungsspiel = exerciseId.startsWith("stellungsspiel_")

            if (isStellungsspiel) {
                // For Stellungsspiel exercises, calculate overall accuracy by date
                val scoresByDate = exerciseRecords
                    .groupBy { it.timestamp.toLocalDate() }
                    .mapValues { (_, dateRecords) ->
                        val validRecords = dateRecords.filter { it.score != null && !it.isCompletionMarker && it.score != -1 }
                        if (validRecords.isEmpty()) {
                            Float.NaN
                        } else {
                            val successCount = validRecords.count { it.score == 1 }
                            (successCount.toFloat() / validRecords.size) * 100
                        }
                    }

                val entries = allDates.mapIndexedNotNull { index, date ->
                    scoresByDate[date]?.let { score ->
                        if (score.isNaN()) null else Entry(index.toFloat(), score, getExerciseTitle(exerciseId) ?: "Unknown Exercise")
                    }
                }

                if (entries.isNotEmpty()) {
                    val label = getExerciseTitle(exerciseId) ?: "Unknown Exercise"
                    LineDataSet(entries, label).apply {
                        color = exerciseColorMap[exerciseId] ?: run {
                            val exerciseIndex = records.keys.indexOf(exerciseId)
                            val hue = (exerciseIndex * 360f / records.size)
                            Color.HSVToColor(floatArrayOf(hue, 1f, 1f))
                        }

                        lineWidth = 2f
                        circleRadius = 4f
                        circleColors = listOf(color)
                        valueTextSize = 10f
                        setDrawValues(false)
                    }.also { dataSets.add(it) }
                }
            } else {
                // For other exercises, keep the original behavior
                val scoresBySpelt = exerciseRecords.groupBy { it.spelt }

                scoresBySpelt.forEach { (spelt, recordsForSpelt) ->
                    val scoresByDate = recordsForSpelt
                        .groupBy { it.timestamp.toLocalDate() }
                        .mapValues { (_, dateRecords) ->
                            dateRecords.mapNotNull { it.score }.average().toFloat()
                        }

                    val entries = allDates.mapIndexedNotNull { index, date ->
                        scoresByDate[date]?.let { score ->
                            if (score.isNaN()) null else Entry(index.toFloat(), score, "${getExerciseTitle(exerciseId) ?: "Unknown Exercise"}${if (spelt != null) " - $spelt" else ""}")
                        }
                    }

                    if (entries.isNotEmpty()) {
                        val label = "${getExerciseTitle(exerciseId) ?: "Unknown Exercise"}${if (spelt != null) " - $spelt" else ""}"
                        LineDataSet(entries, label).apply {
                            val exerciseTitle = getExerciseTitle(exerciseId)
                            val colorKey = if (spelt != null && exerciseTitle in listOf("Gelb", "Grün", "Braun", "Blau", "Schwarz")) {
                                "$exerciseTitle - $spelt"
                            } else {
                               exerciseId
                            }
                            color = exerciseColorMap[colorKey] ?: run {
                                val exerciseIndex = records.keys.indexOf(exerciseId)
                                val hue = (exerciseIndex * 360f / records.size)
                                Color.HSVToColor(floatArrayOf(hue, 1f, 1f))
                             }

                            lineWidth = 2f
                            circleRadius = 4f
                            circleColors = listOf(color)
                            valueTextSize = 10f
                            setDrawValues(false)
                        }.also { dataSets.add(it) }
                    }
                }
            }
        }

        lineChart.data = LineData(dataSets as MutableList<ILineDataSet>)
        lineChart.invalidate()
    }

    private inner class DateAxisFormatter : ValueFormatter() {
        private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM")
        private var dates = listOf<LocalDate>()

        fun setDates(newDates: List<LocalDate>) {
            dates = newDates
        }

        override fun getFormattedValue(value: Float): String {
            val index = value.toInt()
            return if (index >= 0 && index < dates.size) {
                dateFormatter.format(dates[index])
            } else {
                ""
            }
        }
    }

    private fun getExerciseCategory(exerciseId: String?): String {
        return when {
            exerciseId in listOf(
                "blue_to_pink", "long_shots", "medium_to_blue", "rest"
            ) -> "Potting"

            exerciseId in listOf(
                "line_up", "clearance", "sequence",
                "break_50", "break_75", "break_100", "break_125", "break_150",
                "t_break", "fifty_plus", "x_break", "y_break",
                "eight_reds", "five_reds", "frames", "black_routines",
                "brown_to_reds", "bc_to_reds", "pj_routines", "random_bilder"
            ) -> "Breakbuilding"

            exerciseId in listOf(
                "safety_shots", "snooker_escape", "defensive_position",
                "three_reds_save", "snooker_legen", "black_to_bc",
                "blue_pink_black"
            ) -> "Safeties"

            exerciseId in listOf(
                "split_shots", "cluster_break", "controlled_split",
                "black", "blue", "brown", "yellow_splits", "green"
            ) -> "Splits"

            // Add Stellungsspiel category
            exerciseId?.startsWith("stellungsspiel_") == true || exerciseId == "blau_durch_bc" || exerciseId == "hohe_schwarze" -> "Stellungsspiel"

            exerciseId in listOf(
                "blue_doubletouch", "langer_stoss", "gerader_stoss_2_kreiden"
            ) -> "Technik"

            else -> "Andere"
        }
    }

    private fun getExerciseTitle(exerciseId: String?): String {
        return when (exerciseId) {
            "blue_to_pink" -> "Blau zu Pink"
            "long_shots" -> "Lange Bälle"
            "medium_to_blue" -> "Medium zu Blau"
            "rest" -> "Rest"

            "line_up" -> "Line-up"
            "clearance" -> "Clearance"
            "sequence" -> "Sequenz"
            "break_50" -> "Break 50"
            "break_75" -> "Break 75"
            "break_100" -> "Break 100"
            "break_125" -> "Break 125"
            "break_150" -> "Break 150"
            "t_break" -> "T-Break"
            "fifty_plus" -> "50+"
            "x_break" -> "X-Break"
            "y_break" -> "Y-Break"
            "eight_reds" -> "8reds Break"
            "five_reds" -> "5reds clear"
            "frames" -> "Frames"
            "black_routines" -> "Black Routines"
            "brown_to_reds" -> "Braun zu Rot"
            "bc_to_reds" -> "BC zu Rot"
            "pj_routines" -> "PJ Routines"
            "random_bilder" -> "Random Bilder"

            "safety_shots" -> "Safety Shots"
            "snooker_escape" -> "Snooker Escape"
            "defensive_position" -> "Defensive Position"
            "three_reds_save" -> "3 Rote Save"
            "snooker_legen" -> "Snooker legen"
            "black_to_bc" -> "Schwarz zu BC"
            "blue_pink_black" -> "Blau Pink Schwarz"

            "split_shots" -> "Split Shots"
            "cluster_break" -> "Cluster Break"
            "controlled_split" -> "Kontrollierter Split"
            "black" -> "Schwarz"
            "blue" -> "Blau"
            "brown" -> "Braun"
            "yellow_splits" -> "Gelb"
            "green" -> "Grün"

            "blue_doubletouch" -> "Blue Doubletouch"
            "langer_stoss" -> "Langer Stoß"
            "gerader_stoss_2_kreiden" -> "Gerader Stoß 2 Kreiden"
            "blau_durch_bc" -> "Blau durch BC"

            // Stellungsspiel exercises
            "stellungsspiel_gelb" -> "Stellungsspiel Gelb"
            "stellungsspiel_gruen" -> "Stellungsspiel Grün"
            "stellungsspiel_braun" -> "Stellungsspiel Braun"
            "hohe_schwarze" -> "Hohe Schwarze"

            null -> "Unbekannte Übung"

            else -> exerciseId
        }
    }

    private fun getSpeltColor(baseColor: Int, spelt: String): Int {
        val hsv = FloatArray(3)
        Color.colorToHSV(baseColor, hsv)

        return when (spelt) {
            "3" -> {
                // Slightly lighter
                hsv[2] = (hsv[2] + 0.2f).coerceIn(0f,1f) //ensure that the value is between 0 and 1
                Color.HSVToColor(hsv)
            }

            "6" -> {
                // Base
                baseColor
            }

            "10" -> {
                // Slightly darker
                hsv[2] = (hsv[2] - 0.2f).coerceIn(0f,1f)//ensure that the value is between 0 and 1
                Color.HSVToColor(hsv)
            }

            else -> baseColor // Default to base color if spelt is unknown
        }
    }

    private fun showTimeframePopupMenu(view: View) {
        // Use ThemeHelper to create a themed PopupMenu
        val popupMenu = ThemeHelper.createThemedPopupMenu(requireContext(), view)
        popupMenu.menuInflater.inflate(R.menu.menu_timeframe, popupMenu.menu)
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_last_week -> setTimeframeAndUpdate(Timeframe.LAST_WEEK)
                R.id.action_last_month -> setTimeframeAndUpdate(Timeframe.LAST_MONTH)
                R.id.action_last_quarter -> setTimeframeAndUpdate(Timeframe.LAST_QUARTER)
                R.id.action_last_year -> setTimeframeAndUpdate(Timeframe.LAST_YEAR)
                R.id.action_all_time -> setTimeframeAndUpdate(Timeframe.ALL_TIME)
                R.id.action_custom -> {
                    selectedTimeframe = Timeframe.CUSTOM
                    showCustomTimeframeDialog()
                }
            }
            true
        }
        popupMenu.show()
    }

    private fun setTimeframeAndUpdate(timeframe: Timeframe) {
        selectedTimeframe = timeframe
        when (timeframe) {
            Timeframe.LAST_WEEK -> setTimeframe(LocalDate.now().minusWeeks(1), LocalDate.now())
            Timeframe.LAST_MONTH -> setTimeframe(LocalDate.now().minusMonths(1), LocalDate.now())
            Timeframe.LAST_QUARTER -> setTimeframe(LocalDate.now().minusMonths(3), LocalDate.now())
            Timeframe.LAST_YEAR -> setTimeframe(LocalDate.now().minusYears(1), LocalDate.now())
            Timeframe.ALL_TIME -> {
                val firstDate = exercisePreferences.getRecords()
                    .minOfOrNull { it.timestamp.toLocalDate() }
                    ?: LocalDate.now().minusYears(1)
                setTimeframe(firstDate, LocalDate.now())
            }

            Timeframe.CUSTOM -> {
            } // Handled separately
        }
        updateTimeframeButtonText()
    }

    private fun updateTimeframeButtonText() {
        view?.findViewById<MaterialButton>(R.id.timeframeButton)?.text = when (selectedTimeframe) {
            Timeframe.LAST_WEEK -> "Letzte Woche"
            Timeframe.LAST_MONTH -> "Letzter Monat"
            Timeframe.LAST_QUARTER -> "Letztes Quartal"
            Timeframe.LAST_YEAR -> "Letztes Jahr"
            Timeframe.ALL_TIME -> "Gesamter Zeitraum"
            Timeframe.CUSTOM -> {
                val formatter = DateTimeFormatter.ofPattern("dd.MM.yy")
                "${startDate.format(formatter)} - ${endDate.format(formatter)}"
            }
        }
    }

    private fun showCustomTimeframeDialog() {
        val view = layoutInflater.inflate(R.layout.dialog_custom_timeframe, null)
        val startDateButton = view.findViewById<MaterialButton>(R.id.startDateButton)
        val endDateButton = view.findViewById<MaterialButton>(R.id.endDateButton)

        var selectedStartDate = startDate
        var selectedEndDate = endDate

        fun updateDateButtons() {
            val formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
            startDateButton.text = selectedStartDate.format(formatter)
            endDateButton.text = selectedEndDate.format(formatter)
        }

        updateDateButtons()

        startDateButton.setOnClickListener {
            showDatePicker(selectedStartDate) { date ->
                selectedStartDate = date
                updateDateButtons()
            }
        }

        endDateButton.setOnClickListener {
            showDatePicker(selectedEndDate) { date ->
                selectedEndDate = date
                updateDateButtons()
            }
        }

        // Use ThemeHelper to create a themed AlertDialog with default animations
        ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setTitle("Zeitraum auswählen")
            .setView(view)
            .setPositiveButton("OK") { _, _ ->
                setTimeframe(selectedStartDate, selectedEndDate)
            }
            .setNegativeButton("Abbrechen", null)
            .show()
    }

    private fun showDatePicker(initialDate: LocalDate, onDateSelected: (LocalDate) -> Unit) {
        val calendar = Calendar.getInstance()
        calendar.set(initialDate.year, initialDate.monthValue - 1, initialDate.dayOfMonth)

        // Use ThemeHelper to create a themed DatePickerDialog
        ThemeHelper.createThemedDatePickerDialog(
            requireContext(),
            { _, year, month, dayOfMonth ->
                onDateSelected(LocalDate.of(year, month + 1, dayOfMonth))
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun setTimeframe(start: LocalDate, end: LocalDate) {
        startDate = start
        endDate = end
        loadData()
    }
}
