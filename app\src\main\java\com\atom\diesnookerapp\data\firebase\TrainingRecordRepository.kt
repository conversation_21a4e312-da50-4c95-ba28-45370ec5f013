package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.atom.diesnookerapp.ui.selbsteinschaetzung.DailyTrainingRecord
import com.atom.diesnookerapp.ui.selbsteinschaetzung.TrainingPreferences
import com.google.firebase.firestore.Query
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for training records
 */
class TrainingRecordRepository(private val context: Context) :
    FirebaseRepository<FirebaseTrainingRecord>("training_records") {

    private val localPreferences = TrainingPreferences(context)
    private val authManager = FirebaseAuthManager()

    /**
     * Find all existing training records by date and type
     */
    suspend fun findExistingRecords(date: String, type: String): Result<List<FirebaseTrainingRecord>> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))

        return try {
            val snapshot = getCollection()
                .whereEqualTo("userId", userId)
                .whereEqualTo("date", date)
                .whereEqualTo("type", type)
                .get()
                .await()

            val records = snapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseTrainingRecord::class.java)?.apply { id = doc.id }
            }

            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save or update a training record in Firebase
     */
    suspend fun saveOrUpdateRecord(record: FirebaseTrainingRecord): Result<FirebaseTrainingRecord> {
        return try {
            // Find all existing records with same date and type
            val existingRecordsResult = findExistingRecords(record.date, record.type)

            if (existingRecordsResult.isSuccess) {
                val existingRecords = existingRecordsResult.getOrThrow()

                if (existingRecords.isNotEmpty()) {
                    // Find the most recent record
                    val mostRecentRecord = existingRecords.maxByOrNull { it.lastUpdated }

                    if (mostRecentRecord != null) {
                        // Update the most recent record
                        record.id = mostRecentRecord.id

                        // Delete all other duplicates
                        existingRecords
                            .filter { it.id != mostRecentRecord.id }
                            .forEach { delete(it.id) }
                    }
                }

                // Save the record (will update if id is set, create new if not)
                save(record)
            } else {
                // If finding existing record failed, just try to save
                save(record)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync local records to Firebase
     */
    suspend fun syncToFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val localRecords = localPreferences.getRecords()
            var syncCount = 0

            localRecords.forEach { record ->
                val firebaseRecord = FirebaseTrainingRecord.fromDailyTrainingRecord(
                    record,
                    authManager.getCurrentUserId()!!
                )
                saveOrUpdateRecord(firebaseRecord)
                syncCount++
            }

            Result.success(syncCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync Firebase records to local storage
     */
    suspend fun syncFromFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val result = getAll(FirebaseTrainingRecord::class.java)

            if (result.isSuccess) {
                val firebaseRecords = result.getOrThrow()
                val localRecords = localPreferences.getRecords().toMutableList()

                // Convert Firebase records to local records
                val newRecords = firebaseRecords.map { it.toDailyTrainingRecord() }

                // Merge with local records (replace existing, add new)
                newRecords.forEach { newRecord ->
                    val existingIndex = localRecords.indexOfFirst {
                        it.date == newRecord.date && it.type == newRecord.type
                    }

                    if (existingIndex >= 0) {
                        localRecords[existingIndex] = newRecord
                    } else {
                        localRecords.add(newRecord)
                    }
                }

                // Save merged records
                localRecords.forEach { localPreferences.saveDaily(it) }

                Result.success(newRecords.size)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save a training record both locally and to Firebase
     */
    suspend fun saveRecord(record: DailyTrainingRecord): Result<DailyTrainingRecord> =
        withContext(Dispatchers.IO) {
            try {
                // Save locally
                localPreferences.saveDaily(record)

                // Save to Firebase if logged in
                if (authManager.isLoggedIn()) {
                    val firebaseRecord = FirebaseTrainingRecord.fromDailyTrainingRecord(
                        record,
                        authManager.getCurrentUserId()!!
                    )
                    saveOrUpdateRecord(firebaseRecord)
                }

                Result.success(record)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
}
