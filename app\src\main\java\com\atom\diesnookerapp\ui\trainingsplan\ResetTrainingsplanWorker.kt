package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import androidx.work.ListenableWorker

class ResetTrainingsplanWorker(appContext: Context, workerParams: WorkerParameters) :
    Worker(appContext, workerParams) {

    override fun doWork(): ListenableWorker.Result {
        val manager = TrainingsplanManager(applicationContext)
        manager.resetAndArchiveTrainingsplan()
        return ListenableWorker.Result.success()
    }
}
