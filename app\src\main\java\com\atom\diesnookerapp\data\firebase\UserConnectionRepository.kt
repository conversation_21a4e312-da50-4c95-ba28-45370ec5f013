package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for user connections
 */
class UserConnectionRepository(private val context: Context) {

    companion object {
        private const val TAG = "UserConnectionRepository"
        private const val COLLECTION_PATH = "user_connections"
    }

    private val authManager = FirebaseAuthManager()
    private val firestore = FirebaseFirestore.getInstance()
    private val userProfileRepository = UserProfileRepository(context)

    /**
     * Create a connection between a player and a trainer
     */
    suspend fun createConnection(
        targetEmail: String,
        targetRole: UserRole,
        trainerAccess: Boolean,
        mentalTrainerAccess: Boolean
    ): Result<FirebaseUserConnection> = withContext(Dispatchers.IO) {
        val currentUserId = authManager.getCurrentUserId() ?: return@withContext Result.failure(Exception("User not logged in"))
        
        // Get current user profile
        val currentUserProfileResult = userProfileRepository.getCurrentUserProfile()
        if (currentUserProfileResult.isFailure) {
            return@withContext Result.failure(currentUserProfileResult.exceptionOrNull() ?: Exception("Failed to get current user profile"))
        }
        val currentUserProfile = currentUserProfileResult.getOrNull()!!
        
        // Find target user by email
        val targetUserResult = userProfileRepository.findUserProfileByEmail(targetEmail)
        if (targetUserResult.isFailure) {
            return@withContext Result.failure(targetUserResult.exceptionOrNull() ?: Exception("Failed to find target user"))
        }
        
        val targetUser = targetUserResult.getOrNull()
        if (targetUser == null) {
            return@withContext Result.failure(Exception("User with email $targetEmail not found"))
        }
        
        // Check if target user has the correct role
        if (targetUser.role != targetRole) {
            return@withContext Result.failure(Exception("User with email $targetEmail is not a ${targetRole.name}"))
        }
        
        // Create connection
        val connection = FirebaseUserConnection(
            initiatorId = currentUserId,
            targetId = targetUser.userId,
            status = ConnectionStatus.PENDING,
            trainerAccess = trainerAccess,
            mentalTrainerAccess = mentalTrainerAccess,
            initiatorName = currentUserProfile.displayName,
            targetName = targetUser.displayName,
            targetEmail = targetEmail,
            targetRole = targetRole
        )
        
        // Generate connection ID
        val connectionId = FirebaseUserConnection.createConnectionId(currentUserId, targetUser.userId)
        
        return@withContext try {
            // Check if connection already exists
            val existingDoc = firestore.collection(COLLECTION_PATH).document(connectionId).get().await()
            if (existingDoc.exists()) {
                return@withContext Result.failure(Exception("Connection already exists"))
            }
            
            // Save connection
            firestore.collection(COLLECTION_PATH).document(connectionId).set(connection).await()
            
            // Update connection ID
            connection.id = connectionId
            
            Result.success(connection)
        } catch (e: Exception) {
            Log.e(TAG, "Error creating connection: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Get all connections for the current user
     */
    suspend fun getConnections(): Result<List<FirebaseUserConnection>> = withContext(Dispatchers.IO) {
        val userId = authManager.getCurrentUserId() ?: return@withContext Result.failure(Exception("User not logged in"))
        
        return@withContext try {
            // Get connections where current user is the initiator (player)
            val initiatorSnapshot = firestore.collection(COLLECTION_PATH)
                .whereEqualTo("initiatorId", userId)
                .get()
                .await()
            
            // Get connections where current user is the target (trainer)
            val targetSnapshot = firestore.collection(COLLECTION_PATH)
                .whereEqualTo("targetId", userId)
                .get()
                .await()
            
            val connections = mutableListOf<FirebaseUserConnection>()
            
            // Add initiator connections
            initiatorSnapshot.documents.forEach { doc ->
                val connection = doc.toObject(FirebaseUserConnection::class.java)?.apply { id = doc.id }
                connection?.let { connections.add(it) }
            }
            
            // Add target connections
            targetSnapshot.documents.forEach { doc ->
                val connection = doc.toObject(FirebaseUserConnection::class.java)?.apply { id = doc.id }
                connection?.let { connections.add(it) }
            }
            
            Result.success(connections)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting connections: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Update a connection status
     */
    suspend fun updateConnectionStatus(connectionId: String, status: ConnectionStatus): Result<Unit> = 
        withContext(Dispatchers.IO) {
            return@withContext try {
                firestore.collection(COLLECTION_PATH)
                    .document(connectionId)
                    .update(
                        mapOf(
                            "status" to status,
                            "lastUpdated" to System.currentTimeMillis()
                        )
                    )
                    .await()
                
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(TAG, "Error updating connection status: ${e.message}")
                Result.failure(e)
            }
        }

    /**
     * Update connection permissions
     */
    suspend fun updateConnectionPermissions(
        connectionId: String,
        trainerAccess: Boolean,
        mentalTrainerAccess: Boolean
    ): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            firestore.collection(COLLECTION_PATH)
                .document(connectionId)
                .update(
                    mapOf(
                        "trainerAccess" to trainerAccess,
                        "mentalTrainerAccess" to mentalTrainerAccess,
                        "lastUpdated" to System.currentTimeMillis()
                    )
                )
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating connection permissions: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Delete a connection
     */
    suspend fun deleteConnection(connectionId: String): Result<Unit> = withContext(Dispatchers.IO) {
        return@withContext try {
            firestore.collection(COLLECTION_PATH)
                .document(connectionId)
                .delete()
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting connection: ${e.message}")
            Result.failure(e)
        }
    }
}
