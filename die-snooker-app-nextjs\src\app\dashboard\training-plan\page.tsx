"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import { collection, query, where, orderBy, getDocs, Timestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface PlanItem {
  name: string;
  targetCount: number;
  completionCount: number;
  isChecked: boolean;
}

interface TrainingsplanHistoryEntry {
  id: string;
  userId: string;
  weekStartDate: string; // e.g., "2023-08-21" (Monday of the week)
  // monthKey?: string; // Could be "YYYY-MM" if plans are monthly
  items: PlanItem[];
  lastUpdated: Timestamp;
}

function TrainingPlanPage() {
  const { currentUser } = useAuth();
  const [planHistory, setPlanHistory] = useState<TrainingsplanHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const q = query(
          collection(db, "trainingsplan_history"),
          where("userId", "==", currentUser.uid),
          orderBy("lastUpdated", "desc") // Or weekStartDate if that's more reliable for sorting
        );
        const querySnapshot = await getDocs(q);
        const fetchedPlanHistory = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as TrainingsplanHistoryEntry));

        // Optional: Group by month if needed, similar to old app, or just list by week/update time
        // For now, just using the fetched order (desc by lastUpdated)
        setPlanHistory(fetchedPlanHistory);

      } catch (err: any) {
        console.error("Error fetching training plan history:", err);
        setError("Failed to load training plan history. " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentUser]);

  const getMonthYear = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', { month: 'long', year: 'numeric' });
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-red-500 p-4 bg-red-100 rounded-md">{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Trainingsplan Monatsübersicht</h1>
      {planHistory.length === 0 && !loading && (
        <p className="text-gray-600 bg-white p-6 rounded-lg shadow-md">No training plan history found.</p>
      )}
      <div className="space-y-6">
        {planHistory.map(plan => (
          <details key={plan.id} className="bg-white p-4 rounded-lg shadow-md">
            <summary className="font-medium text-lg cursor-pointer text-gray-800 hover:text-indigo-600">
              {/* Display month/year based on weekStartDate, or a more direct field if available */}
              Plan: {plan.weekStartDate ? getMonthYear(plan.weekStartDate) : `Updated ${new Date(plan.lastUpdated.toDate()).toLocaleDateString('de-DE')}`}
              <span className="text-sm text-gray-500 ml-2">({plan.items.length} Übungen)</span>
            </summary>
            <div className="mt-4">
              {plan.items.length > 0 ? (
                <ul className="list-none pl-0 space-y-2">
                  {plan.items.map((item, index) => (
                    <li key={index} className={`p-3 border rounded-md text-sm ${item.isChecked ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
                      <div className="flex justify-between items-center">
                        <span className={`font-medium ${item.isChecked ? 'text-green-700' : 'text-gray-700'}`}>
                          {item.isChecked ? '✓' : '✗'} {item.name}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${item.isChecked ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                          {item.completionCount}/{item.targetCount}
                        </span>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">No items in this training plan.</p>
              )}
            </div>
          </details>
        ))}
      </div>
    </div>
  );
}

export default withAuth(TrainingPlanPage, { redirectTo: '/login' });
