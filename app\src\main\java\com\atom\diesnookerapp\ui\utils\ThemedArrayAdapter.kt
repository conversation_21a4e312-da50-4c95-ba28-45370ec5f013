package com.atom.diesnookerapp.ui.utils

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.atom.diesnookerapp.ui.settings.ThemePreferences

/**
 * A custom ArrayAdapter that applies the correct theme colors to dropdown items
 */
class ThemedArrayAdapter<T>(
    context: Context,
    resource: Int,
    objects: List<T>
) : ArrayAdapter<T>(context, resource, objects) {

    private val themePreferences = ThemePreferences(context)
    private val currentTheme = themePreferences.getThemeMode()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = super.getView(position, convertView, parent)
        applyThemeToView(view)
        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = super.getDropDownView(position, convertView, parent)
        applyThemeToView(view)
        return view
    }

    private fun applyThemeToView(view: View) {
        if (view is TextView) {
            // Get the appropriate background drawable for the current theme
            val backgroundDrawable = ThemeHelper.getDropdownBackgroundForCurrentTheme(context)
            view.setBackgroundResource(backgroundDrawable)

            // Set text color based on theme
            when (currentTheme) {
                ThemePreferences.THEME_SNOOKER,
                ThemePreferences.THEME_BLUE,
                ThemePreferences.THEME_DARK_BLUE -> {
                    view.setTextColor(Color.BLACK)
                }
                ThemePreferences.THEME_DARK -> {
                    view.setTextColor(Color.WHITE)
                }
                else -> {
                    view.setTextColor(Color.BLACK)
                }
            }

            // Add padding to the dropdown items (matches dropdown_item.xml)
            view.setPadding(24, 12, 24, 12)
        }
    }
}
