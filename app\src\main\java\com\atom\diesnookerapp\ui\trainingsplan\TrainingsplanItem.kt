package com.atom.diesnookerapp.ui.trainingsplan

import org.threeten.bp.LocalDate

// Represents an item in the training plan
data class TrainingsplanItem(
    val id: String, // Unique identifier (e.g., exerciseId or a generated UUID)
    val name: String, // Name of the exercise or task
    var isChecked: Boolean = false, // Completion status
    val isUserDefined: Boolean = false, // true if the item was added by the user
    var date : LocalDate? = null, // Date when this was added to the history
    val exerciseType : String? = null, // The type of the exercise
    val exerciseId: String? = null, // The id of the Exercise
    var completionCount: Int = 0, // Number of times this exercise has been completed
    var targetCount: Int = 1 // Number of times this exercise needs to be completed
)
