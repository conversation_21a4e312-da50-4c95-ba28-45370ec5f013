"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";
import { db } from "@/lib/firebase";
import {
  collection, query, where, getDocs, addDoc, serverTimestamp,
  doc, deleteDoc, getDoc, DocumentData, Timestamp, writeBatch
} from "firebase/firestore";
import { useEffect, useState, FormEvent } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface UserProfile {
  userId: string;
  email: string;
  displayName: string;
  role: string;
}

interface UserConnection {
  id: string; // Firestore document ID
  initiatorId: string; // Player's UID
  targetId: string;    // Trainer's UID
  status: 'PENDING' | 'ACTIVE' | 'DECLINED' | 'REMOVED';
  trainerType: 'TRAINER' | 'MENTAL_TRAINER'; // Role of the trainer they are connecting to
  permissions: { // Permissions granted by player to this trainer
    exerciseAccess: boolean;
    selfAssessmentAccess: boolean;
  };
  createdAt: Timestamp;
  // Optional: trainerName and trainerEmail can be stored for easier display
  trainerProfile?: UserProfile; // Populated client-side
}

function TrainerConnectionsPage() {
  const { currentUser } = useAuth();
  const [connections, setConnections] = useState<UserConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionMessage, setActionMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  // Form state for adding a new trainer
  const [trainerEmail, setTrainerEmail] = useState('');
  const [trainerType, setTrainerType] = useState<'TRAINER' | 'MENTAL_TRAINER'>('TRAINER');
  const [exerciseAccess, setExerciseAccess] = useState(true);
  const [selfAssessmentAccess, setSelfAssessmentAccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchConnections = async () => {
    if (!currentUser) return;
    setLoading(true);
    setError(null);
    try {
      const q = query(
        collection(db, "user_connections"),
        where("initiatorId", "==", currentUser.uid),
        // where("status", "in", ["PENDING", "ACTIVE"]) // Optionally filter out DECLINED/REMOVED
      );
      const querySnapshot = await getDocs(q);
      const fetchedConnectionsPromises = querySnapshot.docs.map(async (docSnap) => {
        const firestoreData = docSnap.data();
        const connectionData = {
          id: docSnap.id,
          ...firestoreData,
          permissions: firestoreData.permissions || { exerciseAccess: false, selfAssessmentAccess: false } // Default permissions
        } as UserConnection;
        // Fetch trainer's profile for display name
        const trainerProfileDoc = await getDoc(doc(db, "user_profiles", connectionData.targetId));
        if (trainerProfileDoc.exists()) {
          connectionData.trainerProfile = trainerProfileDoc.data() as UserProfile;
        }
        return connectionData;
      });
      const fetchedConnections = await Promise.all(fetchedConnectionsPromises);
      setConnections(fetchedConnections.sort((a,b) => (b.createdAt?.toMillis() || 0) - (a.createdAt?.toMillis() || 0)));
    } catch (err: any) {
      console.error("Error fetching connections:", err);
      setError("Failed to load connections. " + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConnections();
  }, [currentUser]);

  const handleAddTrainer = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;
    setIsSubmitting(true);
    setActionMessage(null);

    try {
      // 1. Find trainer by email
      const trainerQuery = query(collection(db, "user_profiles"), where("email", "==", trainerEmail));
      const trainerSnapshot = await getDocs(trainerQuery);

      if (trainerSnapshot.empty) {
        throw new Error(`No trainer found with email ${trainerEmail}.`);
      }

      const trainerProfile = trainerSnapshot.docs[0].data() as UserProfile;
      const trainerId = trainerSnapshot.docs[0].id;

      if (trainerProfile.role !== 'TRAINER' && trainerProfile.role !== 'MENTAL_TRAINER') {
        throw new Error(`User ${trainerEmail} is not registered as a trainer.`);
      }

      // Check for existing connection
      const existingConnectionQuery = query(
        collection(db, "user_connections"),
        where("initiatorId", "==", currentUser.uid),
        where("targetId", "==", trainerId)
      );
      const existingConnectionSnapshot = await getDocs(existingConnectionQuery);
      if (!existingConnectionSnapshot.empty) {
         const existingStatus = existingConnectionSnapshot.docs[0].data().status;
         if(existingStatus === 'ACTIVE' || existingStatus === 'PENDING') {
            throw new Error(`You already have a ${existingStatus.toLowerCase()} connection with this trainer.`);
         }
      }


      // 2. Create connection request
      await addDoc(collection(db, "user_connections"), {
        initiatorId: currentUser.uid,
        targetId: trainerId,
        status: 'PENDING', // Trainers will need to accept this
        trainerType: trainerProfile.role, // Set based on the found trainer's role
        permissions: {
          exerciseAccess: trainerProfile.role === 'MENTAL_TRAINER' ? false : exerciseAccess, // Mental trainers might not need exercise access by default
          selfAssessmentAccess: trainerProfile.role === 'TRAINER' ? false : selfAssessmentAccess, // Regular trainers might not need self-assessment by default
        },
        createdAt: serverTimestamp(),
        initiatorEmail: currentUser.email, // For trainer's convenience
        initiatorName: currentUser.displayName || currentUser.email?.split('@')[0],
      });

      setActionMessage({ type: 'success', text: `Connection request sent to ${trainerProfile.displayName || trainerEmail}.` });
      setTrainerEmail(''); // Reset form
      fetchConnections(); // Refresh list
    } catch (err: any) {
      console.error("Error adding trainer:", err);
      setActionMessage({ type: 'error', text: err.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveConnection = async (connectionId: string) => {
    if (!window.confirm("Are you sure you want to remove this trainer connection?")) return;
    setLoading(true); // Use main loading or a specific one
    setActionMessage(null);
    try {
      await deleteDoc(doc(db, "user_connections", connectionId));
      setActionMessage({ type: 'success', text: "Connection removed successfully." });
      fetchConnections(); // Refresh list
    } catch (err: any) {
      console.error("Error removing connection:", err);
      setActionMessage({ type: 'error', text: "Failed to remove connection. " + err.message });
      setLoading(false);
    }
  };

  const getStatusChipColor = (status: UserConnection['status']) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'DECLINED': return 'bg-red-100 text-red-800';
      case 'REMOVED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };


  if (loading && !isSubmitting) return <LoadingSpinner />; // Don't show main loader if only form is submitting

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Trainer-Verbindungen</h1>

      {actionMessage && (
        <div className={`p-3 mb-4 rounded-md text-sm ${actionMessage.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {actionMessage.text}
        </div>
      )}
      {error && !actionMessage && <div className="text-red-500 p-4 bg-red-100 rounded-md mb-4">{error}</div>}


      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4 text-gray-700">Trainer hinzufügen</h2>
        <form onSubmit={handleAddTrainer} className="space-y-4">
          <div>
            <label htmlFor="trainer-email" className="block text-sm font-medium text-gray-700">
              E-Mail des Trainers
            </label>
            <input
              type="email"
              id="trainer-email"
              value={trainerEmail}
              onChange={(e) => setTrainerEmail(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="<EMAIL>"
            />
          </div>
          {/* Trainer Type selection by player is removed, it will be derived from trainer's profile */}
          <div>
            <p className="block text-sm font-medium text-gray-700 mb-1">Zugriffsberechtigungen (für Trainer):</p>
            <div className="space-y-2">
                <label className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        checked={exerciseAccess}
                        onChange={(e) => setExerciseAccess(e.target.checked)}
                        className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                    />
                    <span className="text-sm text-gray-600">Übungen und Trainingsplan (für technische Trainer)</span>
                </label>
                <label className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        checked={selfAssessmentAccess}
                        onChange={(e) => setSelfAssessmentAccess(e.target.checked)}
                        className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                    />
                    <span className="text-sm text-gray-600">Selbsteinschätzung (für Mental-Trainer)</span>
                </label>
            </div>
            <p className="text-xs text-gray-500 mt-1">Der Trainer-Typ (Technik/Mental) wird automatisch anhand des Trainerprofils bestimmt. Bitte wählen Sie die passenden Berechtigungen.</p>
          </div>
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white font-semibold rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400"
          >
            {isSubmitting ? 'Sende Anfrage...' : 'Verbindungsanfrage senden'}
          </button>
        </form>
      </div>

      <h2 className="text-xl font-semibold mb-4 text-gray-700">Aktive und Ausstehende Verbindungen</h2>
      {connections.length === 0 && !loading && (
        <p className="text-gray-600 bg-white p-6 rounded-lg shadow-md">Keine Verbindungen vorhanden.</p>
      )}
      <div className="space-y-4">
        {connections.map(conn => (
          <div key={conn.id} className="bg-white p-4 rounded-lg shadow-md flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <p className="font-medium text-gray-800">
                {conn.trainerProfile?.displayName || conn.trainerProfile?.email || 'Unbekannter Trainer'}
                <span className="text-xs text-gray-500 ml-2">({conn.trainerType})</span>
              </p>
              <p className="text-sm text-gray-600">
                Berechtigungen:
                {conn.permissions.exerciseAccess && " Übungen,"}
                {conn.permissions.selfAssessmentAccess && " Selbsteinschätzung"}
              </p>
               <p className="text-xs text-gray-500">
                Angefragt am: {conn.createdAt ? new Date(conn.createdAt.toDate()).toLocaleDateString('de-DE') : 'N/A'}
              </p>
            </div>
            <div className="mt-3 md:mt-0 flex items-center space-x-3">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusChipColor(conn.status)}`}>
                    {conn.status}
                </span>
                {(conn.status === 'ACTIVE' || conn.status === 'PENDING' || conn.status === 'DECLINED') && (
                    <button
                        onClick={() => handleRemoveConnection(conn.id)}
                        disabled={loading}
                        className="px-3 py-1 bg-red-500 text-white text-xs font-semibold rounded-md hover:bg-red-600 disabled:bg-gray-300"
                    >
                        Entfernen
                    </button>
                )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default withAuth(TrainerConnectionsPage, { redirectTo: '/login' });
