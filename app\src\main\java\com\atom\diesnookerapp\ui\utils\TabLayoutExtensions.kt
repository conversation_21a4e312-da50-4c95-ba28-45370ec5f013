package com.atom.diesnookerapp.ui.utils

import android.content.Context
import android.content.res.ColorStateList
import android.util.TypedValue
import androidx.annotation.StyleRes
import com.google.android.material.tabs.TabLayout

/**
 * Extension function to apply a style to a TabLayout
 */
fun TabLayout.setStyle(@StyleRes styleResId: Int) {
    val typedArray = context.obtainStyledAttributes(styleResId, intArrayOf(
        com.google.android.material.R.attr.tabBackground,
        com.google.android.material.R.attr.tabIndicatorColor,
        com.google.android.material.R.attr.tabSelectedTextColor,
        com.google.android.material.R.attr.tabTextColor
    ))
    
    try {
        // Get background color
        val backgroundColorResId = typedArray.getResourceId(0, 0)
        if (backgroundColorResId != 0) {
            setBackgroundResource(backgroundColorResId)
        }
        
        // Get indicator color
        val indicatorColor = typedArray.getColor(1, 0)
        if (indicatorColor != 0) {
            setSelectedTabIndicatorColor(indicatorColor)
        }
        
        // Get selected text color
        val selectedTextColor = typedArray.getColor(2, 0)
        // Get normal text color
        val textColor = typedArray.getColor(3, 0)
        
        if (selectedTextColor != 0 && textColor != 0) {
            val colorStateList = ColorStateList(
                arrayOf(
                    intArrayOf(android.R.attr.state_selected),
                    intArrayOf()
                ),
                intArrayOf(
                    selectedTextColor,
                    textColor
                )
            )
            tabTextColors = colorStateList
        }
    } finally {
        typedArray.recycle()
    }
}
