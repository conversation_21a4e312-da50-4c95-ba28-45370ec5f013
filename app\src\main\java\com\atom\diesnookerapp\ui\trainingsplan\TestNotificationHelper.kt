package com.atom.diesnookerapp.ui.trainingsplan

import android.content.Context
import android.util.Log

/**
 * Helper class to test notifications
 */
class TestNotificationHelper {
    companion object {
        private const val TAG = "TestNotificationHelper"
        
        /**
         * Manually triggers the notification to test if it works
         */
        fun triggerNotificationTest(context: Context): Boolean {
            Log.d(TAG, "Manually triggering notification test")
            val notificationManager = TrainingsplanNotificationManager(context)
            return notificationManager.checkAndNotifyIncompleteExercises()
        }
        
        /**
         * Forces the worker to run immediately, ignoring the day of week check
         */
        fun forceReminderWorkerRun(context: Context) {
            Log.d(TAG, "Forcing reminder worker to run immediately")
            
            // First, make sure we have a valid training plan with some incomplete exercises
            ensureIncompleteExercisesExist(context)
            
            // Manually trigger the notification
            val notificationManager = TrainingsplanNotificationManager(context)
            val sent = notificationManager.checkAndNotifyIncompleteExercises()
            
            Log.d(TAG, "Force notification result: $sent")
        }
        
        /**
         * Ensures there are some incomplete exercises in the training plan for testing
         */
        private fun ensureIncompleteExercisesExist(context: Context) {
            val trainingsplanManager = TrainingsplanManager(context)
            val currentPlan = trainingsplanManager.getCurrentTrainingsplan()
            
            // If the plan is empty or all exercises are completed, add a test exercise
            if (currentPlan.items.isEmpty() || currentPlan.items.all { it.isChecked }) {
                Log.d(TAG, "Adding test exercise to ensure notification can be sent")
                trainingsplanManager.addCustomExerciseToPlan(
                    "Test Exercise (for notification testing)",
                    "Test"
                )
            }
        }
    }
}
