import { initializeApp, getApps, FirebaseApp } from "firebase/app";
import { getAuth, Auth } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY", // Note: Placeholder API key
  authDomain: "die-snooker-app.firebaseapp.com",
  projectId: "die-snooker-app",
  storageBucket: "die-snooker-app.firebasestorage.app",
  messagingSenderId: "547283642216",
  appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
  measurementId: "G-GTFVZZ4LJ"
};

let app: FirebaseApp;
let auth: Auth;
let db: Firestore;

if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

auth = getAuth(app);
db = getFirestore(app);

export { app, auth, db };
