<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base PopupMenu style -->
    <style name="PopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/dropdown_background</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle</item>
    </style>

    <!-- Snooker theme PopupMenu style -->
    <style name="PopupMenuStyle_Snooker" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Snooker</item>
    </style>

    <!-- Blue theme PopupMenu style -->
    <style name="PopupMenuStyle_Blue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Blue</item>
    </style>

    <!-- Dark Blue theme PopupMenu style -->
    <style name="PopupMenuStyle_DarkBlue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_DarkBlue</item>
    </style>

    <!-- Dark theme PopupMenu style -->
    <style name="PopupMenuStyle_Dark" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Dark</item>
    </style>

    <!-- ListView styles for PopupMenu -->
    <style name="PopupMenuListViewStyle" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="android:background">@drawable/dropdown_background</item>
    </style>

    <style name="PopupMenuListViewStyle_Snooker" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_snooker</item>
    </style>

    <style name="PopupMenuListViewStyle_Blue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_blue</item>
    </style>

    <style name="PopupMenuListViewStyle_DarkBlue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark_blue</item>
    </style>

    <style name="PopupMenuListViewStyle_Dark" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>

    <!-- Ocean theme PopupMenu style -->
    <style name="PopupMenuStyle_Ocean" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Ocean</item>
    </style>

    <style name="PopupMenuListViewStyle_Ocean" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_ocean</item>
    </style>

    <!-- Crimson theme PopupMenu style -->
    <style name="PopupMenuStyle_Crimson" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Crimson</item>
    </style>

    <style name="PopupMenuListViewStyle_Crimson" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_crimson</item>
    </style>

    <!-- Neon theme PopupMenu style -->
    <style name="PopupMenuStyle_Neon" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_neon</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Neon</item>
    </style>

    <style name="PopupMenuListViewStyle_Neon" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_neon</item>
    </style>

    <!-- DatePicker Dialog Styles -->
    <style name="DatePickerDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorAccent">?attr/colorPrimary</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/DatePickerButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/DatePickerButtonStyle</item>
    </style>

    <style name="DatePickerButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
</resources>
