package com.atom.diesnookerapp.ui.settings

import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatDelegate

class ThemePreferences(context: Context) {
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("theme_prefs", Context.MODE_PRIVATE)

    companion object {
        const val THEME_LIGHT = AppCompatDelegate.MODE_NIGHT_NO
        const val THEME_DARK = AppCompatDelegate.MODE_NIGHT_YES
        const val THEME_SYSTEM = AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM

        // Custom theme constants
        const val THEME_SNOOKER = 10
        const val THEME_BLUE = 11
        const val THEME_DARK_BLUE = 12
        const val THEME_OCEAN = 13
        const val THEME_CRIMSON = 14
        const val THEME_NEON = 15

        private const val KEY_THEME_MODE = "theme_mode"
        private const val KEY_CUSTOM_THEME = "custom_theme"
    }

    fun saveThemeMode(themeMode: Int) {
        sharedPreferences.edit().putInt(KEY_THEME_MODE, themeMode).apply()
    }

    fun getThemeMode(): Int {
        return sharedPreferences.getInt(KEY_THEME_MODE, THEME_SYSTEM)
    }

    fun saveCustomTheme(customTheme: Int) {
        sharedPreferences.edit().putInt(KEY_CUSTOM_THEME, customTheme).apply()
    }

    fun getCustomTheme(): Int {
        return sharedPreferences.getInt(KEY_CUSTOM_THEME, THEME_SNOOKER)
    }

    fun isUsingCustomTheme(): Boolean {
        val themeMode = getThemeMode()
        return themeMode != THEME_LIGHT && themeMode != THEME_DARK && themeMode != THEME_SYSTEM
    }
}
