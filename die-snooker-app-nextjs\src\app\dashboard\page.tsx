"use client";

import withAuth from "@/components/auth/withAuth";
import { useAuth } from "@/context/AuthContext";

function PlayerDashboardPage() {
  const { currentUser, userProfile } = useAuth();

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Dashboard Home</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-lg text-gray-700">
          Welcome back, <span className="font-semibold">{userProfile?.displayName || currentUser?.email}</span>!
        </p>
        <p className="text-md text-gray-600 mt-2">
          This is your central hub for tracking progress, managing exercises, and connecting with trainers.
        </p>
        <p className="text-md text-gray-600 mt-1">
          Use the navigation on the left to access different sections of the application.
        </p>
        {/* More content or summary widgets can be added here later */}
      </div>
    </div>
  );
}

// Protect the component, redirect to /login if not authenticated as a player
// No specific role check needed here beyond being an authenticated user.
// The layout already handles logout and user display.
export default withAuth(PlayerDashboardPage, { redirectTo: '/login' });
