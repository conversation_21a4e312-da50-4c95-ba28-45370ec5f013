package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import com.atom.diesnookerapp.ui.ergebniserfassung.ExercisePreferences
import com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseRecord
import com.google.firebase.firestore.Query
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Repository for exercise records
 */
class ExerciseRecordRepository(private val context: Context) :
    FirebaseRepository<FirebaseExerciseRecord>("exercise_records") {

    private val localPreferences = ExercisePreferences(context)
    private val authManager = FirebaseAuthManager()

    /**
     * Find all existing exercise records by exerciseId and timestamp
     */
    suspend fun findExistingRecords(exerciseId: String, timestamp: String): Result<List<FirebaseExerciseRecord>> {
        val userId = authManager.getCurrentUserId() ?: return Result.failure(Exception("User not logged in"))

        return try {
            val snapshot = getCollection()
                .whereEqualTo("userId", userId)
                .whereEqualTo("exerciseId", exerciseId)
                .whereEqualTo("timestamp", timestamp)
                .get()
                .await()

            val records = snapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseExerciseRecord::class.java)?.apply { id = doc.id }
            }

            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save or update an exercise record in Firebase.
     * If the record has a blank ID, it's created as a new document.
     * If the record has a non-blank ID, the corresponding document is updated.
     */
    suspend fun saveOrUpdateRecord(record: FirebaseExerciseRecord): Result<FirebaseExerciseRecord> {
        // The save method in the base FirebaseRepository already handles
        // creating a new document if id is blank or updating if it exists.
        // The complex logic of finding duplicates by timestamp was fragile and has been removed.
        // The syncToFirebase function is now responsible for updating local records with the
        // ID assigned by Firestore, making this process much more robust.
        return save(record)
    }

    /**
     * Clean up duplicate records in Firestore
     */
    suspend fun cleanupDuplicates(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val result = getAll(FirebaseExerciseRecord::class.java)

            if (result.isSuccess) {
                val allRecords = result.getOrThrow()

                // Group records by exerciseId and timestamp
                val recordGroups = allRecords.groupBy { "${it.exerciseId}_${it.timestamp}" }
                var deletedCount = 0

                // For each group, keep only the most recent record and delete others
                recordGroups.forEach { (_, records) ->
                    if (records.size > 1) {
                        // Sort by lastUpdated (descending) and keep the first one
                        val sortedRecords = records.sortedByDescending { it.lastUpdated }
                        val recordToKeep = sortedRecords.first()

                        // Delete all other records
                        sortedRecords.drop(1).forEach {
                            delete(it.id)
                            deletedCount++
                        }
                    }
                }

                Result.success(deletedCount)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync local records to Firebase
     */
    suspend fun syncToFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            val localRecords = localPreferences.getRecords()
            val updatedLocalRecords = mutableListOf<ExerciseRecord>()
            var syncCount = 0

            localRecords.forEach { record ->
                val firebaseRecord = FirebaseExerciseRecord.fromExerciseRecord(
                    record,
                    authManager.getCurrentUserId()!!
                )
                val saveResult = saveOrUpdateRecord(firebaseRecord)
                if (saveResult.isSuccess) {
                    val savedFirebaseRecord = saveResult.getOrThrow()
                    // Convert back to local record to get the updated ID
                    val updatedLocalRecord = savedFirebaseRecord.toExerciseRecord()
                    updatedLocalRecords.add(updatedLocalRecord)
                    syncCount++
                } else {
                    // If saving fails, keep the original local record
                    updatedLocalRecords.add(record)
                }
            }

            // Save the entire updated list back to local preferences
            localPreferences.saveRecords(updatedLocalRecords)

            Result.success(syncCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sync Firebase records to local storage
     */
    suspend fun syncFromFirebase(): Result<Int> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            // First, clean up any duplicates in Firestore
            cleanupDuplicates()

            val result = getAll(FirebaseExerciseRecord::class.java)

            if (result.isSuccess) {
                val firebaseRecords = result.getOrThrow()
                val localRecords = localPreferences.getRecords().toMutableList()

                // Convert Firebase records to local records
                val newRecords = firebaseRecords.map { it.toExerciseRecord() }

                // Add new records (avoid duplicates by checking exerciseId and timestamp)
                val existingRecordKeys = localRecords.map {
                    "${it.exerciseId}_${it.timestamp}"
                }.toSet()

                val recordsToAdd = newRecords.filter {
                    "${it.exerciseId}_${it.timestamp}" !in existingRecordKeys
                }

                // Save new records
                recordsToAdd.forEach { localPreferences.saveRecord(it) }

                Result.success(recordsToAdd.size)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save an exercise record both locally and to Firebase
     */
    suspend fun saveRecord(record: ExerciseRecord): Result<ExerciseRecord> =
        withContext(Dispatchers.IO) {
            try {
                // Save locally
                localPreferences.saveRecord(record)

                // Save to Firebase if logged in
                if (authManager.isLoggedIn()) {
                    val firebaseRecord = FirebaseExerciseRecord.fromExerciseRecord(
                        record,
                        authManager.getCurrentUserId()!!
                    )
                    saveOrUpdateRecord(firebaseRecord)
                }

                Result.success(record)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
}
