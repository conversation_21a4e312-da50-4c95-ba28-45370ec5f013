rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isSignedIn() && request.auth.uid == userId;
    }

    function hasUserProfile(userId) {
      return exists(/databases/$(database)/documents/user_profiles/$(userId));
    }

    function hasConnection(userId1, userId2) {
      return exists(/databases/$(database)/documents/user_connections/$(userId1)_$(userId2)) ||
             exists(/databases/$(database)/documents/user_connections/$(userId2)_$(userId1));
    }

    function hasTrainerAccess(trainerId, playerId) {
      return exists(/databases/$(database)/documents/user_connections/$(playerId)_$(trainerId)) &&
             get(/databases/$(database)/documents/user_connections/$(playerId)_$(trainerId)).data.trainerAccess == true;
    }

    function hasMentalTrainerAccess(trainerId, playerId) {
      return exists(/databases/$(database)/documents/user_connections/$(playerId)_$(trainerId)) &&
             get(/databases/$(database)/documents/user_connections/$(playerId)_$(trainerId)).data.mentalTrainerAccess == true;
    }

    // Base rule - users can read/write their own data
    match /{document=**} {
      allow read, write: if isSignedIn() &&
                           (resource == null || request.auth.uid == resource.data.userId);
    }

    // User profiles collection
    match /user_profiles/{userId} {
      // Users can read/write their own profile
      allow read, write: if isOwner(userId);

      // Allow creating a new profile for the authenticated user
      allow create: if isSignedIn() && request.auth.uid == userId && request.resource.data.userId == userId;

      // Users can read profiles of users they are connected to
      allow read: if isSignedIn() && hasConnection(request.auth.uid, userId);

      // Trainers can read profiles of users who have granted them access
      allow read: if isSignedIn() &&
                   (hasTrainerAccess(request.auth.uid, userId) ||
                    hasMentalTrainerAccess(request.auth.uid, userId));
    }

    // User connections collection
    match /user_connections/{connectionId} {
      // Both users in the connection can read the connection document
      allow read: if isSignedIn() &&
                   (connectionId.split('_')[0] == request.auth.uid ||
                    connectionId.split('_')[1] == request.auth.uid);

      // Only the initiating user (player) can create/update/delete connections
      allow create, update, delete: if isSignedIn() &&
                                     connectionId.split('_')[0] == request.auth.uid &&
                                     request.resource.data.initiatorId == request.auth.uid;
    }

    // Training records (Selbsteinschätzung)
    match /training_records/{recordId} {
      // Owner can read/write
      allow read, write: if isSignedIn() &&
                           (resource == null || request.auth.uid == resource.data.userId);

      // Mental trainers can read if they have a connection with permission
      allow read: if isSignedIn() &&
                   resource != null &&
                   hasMentalTrainerAccess(request.auth.uid, resource.data.userId);
    }

    // Question records (part of Selbsteinschätzung)
    match /question_records/{recordId} {
      // Owner can read/write
      allow read, write: if isSignedIn() &&
                           (resource == null || request.auth.uid == resource.data.userId);

      // Mental trainers can read if they have a connection with permission
      allow read: if isSignedIn() &&
                   resource != null &&
                   hasMentalTrainerAccess(request.auth.uid, resource.data.userId);
    }

    // Exercise records (Übungen)
    match /exercise_records/{recordId} {
      // Owner can read/write
      allow read, write: if isSignedIn() &&
                           (resource == null || request.auth.uid == resource.data.userId);

      // Regular trainers can read if they have a connection with permission
      allow read: if isSignedIn() &&
                   resource != null &&
                   hasTrainerAccess(request.auth.uid, resource.data.userId);
    }

    // Trainingsplan records
    match /trainingsplan_history/{recordId} {
      // Owner can read/write
      allow read, write: if isSignedIn() &&
                           (resource == null || request.auth.uid == resource.data.userId);

      // Regular trainers can read if they have a connection with permission
      allow read: if isSignedIn() &&
                   resource != null &&
                   hasTrainerAccess(request.auth.uid, resource.data.userId);
    }

    // Access logs for transparency
    match /access_logs/{logId} {
      // Only the system can write logs
      allow write: if isSignedIn() && request.auth.token.admin == true;

      // Users can read logs about their own data
      allow read: if isSignedIn() && resource.data.dataOwnerId == request.auth.uid;
    }

    // User Exercise Definitions
    match /user_exercise_definitions/{exerciseDefId} {
      // Allow read if the document's userId matches the requestor's UID
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;

      // Allow create if the new document's userId matches the requestor's UID
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;

      // Allow update if the document's userId matches the requestor's UID
      // and the userId field itself is not being changed.
      allow update: if request.auth != null && resource.data.userId == request.auth.uid
                      && request.resource.data.userId == resource.data.userId;

      // Allow delete if the document's userId matches the requestor's UID
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
    }
  }
}
