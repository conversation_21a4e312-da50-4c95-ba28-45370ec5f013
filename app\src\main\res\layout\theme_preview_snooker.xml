<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/snookerThemePreview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/snooker_green_200"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Snooker Theme"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:background="@color/snooker_red_500" />

            <View
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:background="@color/snooker_green_500" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/snooker_yellow" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/snooker_brown" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/snooker_blue" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_weight="1"
                android:background="@color/snooker_pink" />

            <View
                android:layout_width="0dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:layout_weight="1"
                android:background="@color/snooker_black" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
