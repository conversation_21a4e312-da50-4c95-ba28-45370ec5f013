package com.atom.diesnookerapp.data.firebase

import com.atom.diesnookerapp.ui.aufgaben.TaskCompletionSummary
import com.atom.diesnookerapp.ui.aufgaben.TaskHistoryEntry
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

/**
 * Firebase model for task history entries
 */
data class FirebaseTaskHistoryEntry(
    var weekKey: String = "",
    var endDate: String? = null, // ISO format date, nullable
    var totalPoints: Int = 0,
    var taskCompletions: List<FirebaseTaskCompletionSummary> = emptyList()
) : FirebaseModel() {

    companion object {
        private val formatter = DateTimeFormatter.ISO_LOCAL_DATE

        fun fromTaskHistoryEntry(entry: TaskHistoryEntry, userId: String): FirebaseTaskHistoryEntry {
            return FirebaseTaskHistoryEntry(
                weekKey = entry.weekKey,
                endDate = entry.endDate?.format(formatter),
                totalPoints = entry.totalPoints,
                taskCompletions = entry.taskCompletions.map {
                    FirebaseTaskCompletionSummary.fromTaskCompletionSummary(it)
                }
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }

    fun toTaskHistoryEntry(): TaskHistoryEntry {
        return TaskHistoryEntry(
            weekKey = weekKey,
            endDate = endDate?.let { LocalDate.parse(it, formatter) },
            totalPoints = totalPoints,
            taskCompletions = taskCompletions.map { it.toTaskCompletionSummary() }
        )
    }
}

/**
 * Firebase model for task completion summaries
 */
data class FirebaseTaskCompletionSummary(
    var taskId: String = "",
    var category: String = "",
    var title: String = "",
    var completions: Int = 0,
    var maxCompletions: Int = 0,
    var points: Int = 0
) {
    companion object {
        fun fromTaskCompletionSummary(summary: TaskCompletionSummary): FirebaseTaskCompletionSummary {
            return FirebaseTaskCompletionSummary(
                taskId = summary.taskId,
                category = summary.category,
                title = summary.title,
                completions = summary.completions,
                maxCompletions = summary.maxCompletions,
                points = summary.points
            )
        }
    }

    fun toTaskCompletionSummary(): TaskCompletionSummary {
        return TaskCompletionSummary(
            taskId = taskId,
            category = category,
            title = title,
            completions = completions,
            maxCompletions = maxCompletions,
            points = points
        )
    }
}
