<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base dropdown style -->
    <style name="CustomDropdownStyle" parent="Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:background">@drawable/dropdown_background_solid_default</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    
    <!-- Snooker theme dropdown style -->
    <style name="CustomDropdownStyle.Snooker">
        <item name="android:background">@drawable/dropdown_background_solid_snooker</item>
        <item name="android:textColor">@color/black</item>
    </style>
    
    <!-- Blue theme dropdown style -->
    <style name="CustomDropdownStyle.Blue">
        <item name="android:background">@drawable/dropdown_background_solid_blue</item>
        <item name="android:textColor">@color/black</item>
    </style>
    
    <!-- Dark Blue theme dropdown style -->
    <style name="CustomDropdownStyle.DarkBlue">
        <item name="android:background">@drawable/dropdown_background_solid_dark_blue</item>
        <item name="android:textColor">@color/black</item>
    </style>
    
    <!-- Dark theme dropdown style -->
    <style name="CustomDropdownStyle.Dark">
        <item name="android:background">@drawable/dropdown_background_solid_dark</item>
        <item name="android:textColor">@color/white</item>
    </style>
</resources>
