package com.atom.diesnookerapp.ui.selbsteinschaetzung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.button.MaterialButton
import org.threeten.bp.LocalDate
import org.threeten.bp.ZoneId

class BeforeTournamentFragment : Fragment() {
    private var recyclerView: RecyclerView? = null
    private lateinit var trainingPreferences: TrainingPreferences
    private val assessmentItems = listOf(
        TrainingAssessmentItem(1, "Erholung"),
        TrainingAssessmentItem(2, "Motivation"),
        TrainingAssessmentItem(3, "Siegeswille"),
        TrainingAssessmentItem(4, "Gle<PERSON>gewicht"),
        TrainingAssessmentItem(5, "Flüssigkeit"),
        TrainingAssessmentItem(6, "Supplements"),
        TrainingAssessmentItem(7, "Wille alles zu geben"),
        TrainingAssessmentItem(8, "Freude"),
        TrainingAssessmentItem(9, "Material"),
        TrainingAssessmentItem(10, "Umfeld"),
        TrainingAssessmentItem(11, "Kondition"),
        TrainingAssessmentItem(12, "Augen"),
        TrainingAssessmentItem(13, "Arm locker")
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_before_tournament, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        trainingPreferences = TrainingPreferences(requireContext())

        recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView?.layoutManager = LinearLayoutManager(requireContext())

        val adapter = TrainingAssessmentAdapter(assessmentItems) { item ->
            println("Item ${item.id} score changed to ${item.score}")
        }

        recyclerView?.adapter = adapter

        view.findViewById<MaterialButton>(R.id.saveButton).setOnClickListener {
            saveAssessment()
        }
    }

    private fun saveAssessment() {
        val record = DailyTrainingRecord(
            date = LocalDate.now(ZoneId.systemDefault()),
            type = TrainingType.BEFORE_TOURNAMENT,
            items = assessmentItems
        )
        trainingPreferences.saveDaily(record)
        Toast.makeText(requireContext(), "Bewertungen gespeichert", Toast.LENGTH_SHORT).show()
    }
} 